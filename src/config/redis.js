const redis = require('redis');
require('dotenv').config();

const client = redis.createClient({
    url: process.env.REDIS_URL || process.env.REDIS_PRIVATE_URL || 'redis://localhost:6379',
    retry_strategy: (options) => {
        if (options.error && options.error.code === 'ECONNREFUSED') {
            return new Error('Redis服务器连接被拒绝');
        }
        if (options.total_retry_time > 1000 * 60 * 60) {
            return new Error('重试时间已耗尽');
        }
        if (options.attempt > 10) {
            return undefined;
        }
        return Math.min(options.attempt * 100, 3000);
    }
});

client.on('error', (err) => console.log('Redis客户端错误', err));
client.on('connect', () => console.log('Redis已连接'));

module.exports = client;