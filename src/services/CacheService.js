const redis = require('../config/redis');
const logger = require('../utils/logger');

class CacheService {
    constructor() {
        this.defaultTTL = 3600; // 1小时
    }

    async get(key) {
        try {
            const data = await redis.get(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            logger.error('缓存读取失败:', error.message);
            return null;
        }
    }

    async set(key, data, ttl = this.defaultTTL) {
        try {
            await redis.setEx(key, ttl, JSON.stringify(data));
            return true;
        } catch (error) {
            logger.error('缓存写入失败:', error.message);
            return false;
        }
    }

    async del(key) {
        try {
            await redis.del(key);
            return true;
        } catch (error) {
            logger.error('缓存删除失败:', error.message);
            return false;
        }
    }

    async getOrSet(key, fetchFunction, ttl = this.defaultTTL) {
        let data = await this.get(key);
        
        if (!data) {
            logger.info(`缓存未命中: ${key}, 重新获取数据`);
            data = await fetchFunction();
            await this.set(key, data, ttl);
        }
        
        return data;
    }

    // 批量缓存操作
    async mget(keys) {
        try {
            const values = await redis.mGet(keys);
            return values.map(val => val ? JSON.parse(val) : null);
        } catch (error) {
            logger.error('批量缓存读取失败:', error.message);
            return Array(keys.length).fill(null);
        }
    }

    async mset(keyValuePairs, ttl = this.defaultTTL) {
        try {
            const pipeline = redis.multi();
            for (const [key, value] of keyValuePairs) {
                pipeline.setEx(key, ttl, JSON.stringify(value));
            }
            await pipeline.exec();
            return true;
        } catch (error) {
            logger.error('批量缓存写入失败:', error.message);
            return false;
        }
    }
}

module.exports = new CacheService();