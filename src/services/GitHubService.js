const axios = require('axios');
const logger = require('../utils/logger');

class GitHubService {
    constructor() {
        this.token = process.env.GITHUB_TOKEN;
        this.baseURL = 'https://api.github.com';
        this.rateLimitRemaining = 5000;
        this.rateLimitReset = Date.now();
    }

    async makeRequest(url, params = {}) {
        const headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'GitHub-Trending-Stats/2.0'
        };

        if (this.token) {
            headers['Authorization'] = `token ${this.token}`;
        }

        // 检查速率限制
        if (this.rateLimitRemaining < 10 && Date.now() < this.rateLimitReset) {
            const waitTime = this.rateLimitReset - Date.now();
            logger.warn(`速率限制即将耗尽，等待 ${waitTime}ms`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        try {
            const response = await axios.get(url, { headers, params });
            
            // 更新速率限制信息
            this.rateLimitRemaining = parseInt(response.headers['x-ratelimit-remaining']) || this.rateLimitRemaining;
            this.rateLimitReset = parseInt(response.headers['x-ratelimit-reset']) * 1000 || this.rateLimitReset;

            return response.data;
        } catch (error) {
            logger.error('GitHub API请求失败:', error.message);
            throw error;
        }
    }

    async searchRepositories(query, options = {}) {
        const params = {
            q: query,
            sort: options.sort || 'stars',
            order: options.order || 'desc',
            per_page: options.perPage || 100,
            page: options.page || 1
        };

        const data = await this.makeRequest(`${this.baseURL}/search/repositories`, params);
        
        return data.items.map(repo => ({
            github_id: repo.id,
            name: repo.name,
            full_name: repo.full_name,
            url: repo.html_url,
            description: repo.description || '',
            stars: repo.stargazers_count,
            forks: repo.forks_count,
            language: repo.language || 'Unknown',
            topics: repo.topics || [],
            author: repo.owner.login,
            author_url: repo.owner.html_url,
            created_at: new Date(repo.created_at),
            updated_at: new Date(repo.updated_at)
        }));
    }

    async getTrendingByPeriod(days, limit = 10, language = null) {
        const date = new Date();
        date.setDate(date.getDate() - days);
        const dateStr = date.toISOString().split('T')[0];
        
        let query = `created:>${dateStr}`;
        if (language) {
            query += ` language:${language}`;
        }

        return await this.searchRepositories(query, { perPage: limit });
    }

    async getTrendingByLanguage() {
        const languages = ['JavaScript', 'Python', 'Java', 'TypeScript', 'Go', 'Rust', 'C++', 'C#', 'PHP', 'Ruby'];
        const results = {};

        for (const language of languages) {
            try {
                results[language] = await this.getTrendingByPeriod(7, 5, language);
                // 添加延迟避免速率限制
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                logger.error(`获取 ${language} 趋势数据失败:`, error.message);
                results[language] = [];
            }
        }

        return results;
    }
}

module.exports = new GitHubService();