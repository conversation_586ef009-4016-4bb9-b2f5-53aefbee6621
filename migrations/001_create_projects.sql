CREATE TABLE IF NOT EXISTS projects (
    id SERIAL PRIMARY KEY,
    github_id INTEGER UNIQUE NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    full_name VA<PERSON>HAR(255) NOT NULL,
    url TEXT NOT NULL,
    description TEXT,
    stars INTEGER DEFAULT 0,
    forks INTEGER DEFAULT 0,
    language VARCHAR(100),
    topics TEXT[],
    author VARCHA<PERSON>(255) NOT NULL,
    author_url TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_fetched TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_language (language),
    INDEX idx_created_at (created_at),
    INDEX idx_stars (stars),
    INDEX idx_last_fetched (last_fetched)
);