CREATE TABLE IF NOT EXISTS trend_history (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    period_type VARCHAR(20) NOT NULL, -- 'weekly', 'monthly', 'quarterly', 'yearly'
    rank_position INTEGER NOT NULL,
    stars_count INTEGER NOT NULL,
    forks_count INTEGER NOT NULL,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_period_rank (period_type, rank_position),
    INDEX idx_recorded_at (recorded_at),
    UNIQUE(project_id, period_type, recorded_at::date)
);