<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitHub Trending Stats</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='%23181717' d='M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 ********** 1.21 1.87.87 **********-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 *********-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 **********.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 **********.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .***********.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z'></path><path d='M2 8h2l2-4 2 8 2-4h4' stroke='%2334D058' stroke-width='1.5' fill='none'/></svg>">
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* CSS Variables for 2025 Design System */
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --accent-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            
            --bg-primary: #0d1117;
            --bg-secondary: #161b22;
            --bg-tertiary: #21262d;
            --bg-glass: rgba(255, 255, 255, 0.05);
            --bg-glass-hover: rgba(255, 255, 255, 0.1);
            
            --text-primary: #f0f6fc;
            --text-secondary: #7d8590;
            --text-muted: #484f58;
            
            --border-primary: rgba(240, 246, 252, 0.1);
            --border-secondary: rgba(240, 246, 252, 0.05);
            
            --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3);
            --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.4);
            --shadow-glow: 0 0 30px rgba(102, 126, 234, 0.3);
            
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;
            
            --font-display: 'Inter Display', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-body: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        /* Reset & Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-body);
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--bg-primary);
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Animated Background */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: var(--bg-primary);
        }

        .animated-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(240, 147, 251, 0.05) 0%, transparent 50%);
            animation: bgFloat 20s ease-in-out infinite;
        }

        @keyframes bgFloat {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            33% { transform: translate(-20px, -20px) rotate(1deg); }
            66% { transform: translate(20px, -10px) rotate(-1deg); }
        }

        /* Glassmorphism Container */
        .glass-container {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-glass);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Header with Enhanced Logo */
        header {
            text-align: center;
            padding: 3rem 2rem;
            margin-bottom: 2rem;
            position: relative;
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            animation: logoFloat 6s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .logo {
            position: relative;
        }

        .logo svg {
            filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.3));
            transition: all 0.3s ease;
        }

        .logo:hover svg {
            transform: scale(1.05);
            filter: drop-shadow(0 8px 16px rgba(102, 126, 234, 0.5));
        }

        h1 {
            font-family: var(--font-display);
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            letter-spacing: -0.02em;
            animation: titleGlow 3s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            from { filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.3)); }
            to { filter: drop-shadow(0 0 30px rgba(118, 75, 162, 0.5)); }
        }

        .subtitle {
            font-size: 1.25rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            font-weight: 400;
        }

        .stats-info {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .github-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            background: var(--bg-glass);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .github-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            transition: left 0.5s ease;
            z-index: -1;
        }

        .github-link:hover::before {
            left: 0;
        }

        .github-link:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
            border-color: rgba(102, 126, 234, 0.5);
        }

        /* Enhanced Filter Controls */
        .filter-controls {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-primary);
            box-shadow: var(--shadow-glass);
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-group label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .filter-input,
        .filter-select {
            padding: 0.75rem 1rem;
            background: var(--bg-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .filter-input:focus,
        .filter-select:focus {
            outline: none;
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .filter-select option {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .refresh-btn {
            padding: 0.75rem 1.5rem;
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .refresh-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .refresh-btn:hover::before {
            left: 0;
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
        }

        .refresh-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results-count {
            grid-column: 1 / -1;
            text-align: center;
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-secondary);
        }

        /* Enhanced Tabs */
        .tabs {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            padding: 0.5rem;
            background: var(--bg-glass);
            border-radius: var(--radius-lg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-primary);
        }

        .tab-button {
            padding: 1rem 2rem;
            border: none;
            border-radius: var(--radius-md);
            background: transparent;
            color: var(--text-secondary);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .tab-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .tab-button:hover {
            color: var(--text-primary);
            transform: translateY(-2px);
        }

        .tab-button:hover::before {
            opacity: 0.1;
        }

        .tab-button.active {
            color: white;
            box-shadow: var(--shadow-glow);
        }

        .tab-button.active::before {
            opacity: 1;
        }

        /* Main Content */
        main {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-lg);
            padding: 2rem;
            border: 1px solid var(--border-primary);
            box-shadow: var(--shadow-glass);
            margin-bottom: 2rem;
        }

        .tab-content {
            display: none;
            animation: fadeInUp 0.5s ease-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .tab-content h2 {
            margin-bottom: 2rem;
            color: var(--text-primary);
            font-size: 1.75rem;
            font-weight: 700;
            text-align: center;
            font-family: var(--font-display);
        }

        /* Enhanced Project Grid */
        .projects-grid {
            display: grid;
            gap: 1.5rem;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        }

        .project-card {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            border: 1px solid var(--border-primary);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: var(--primary-gradient);
        }

        .project-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.03), transparent);
            transition: left 0.6s ease;
        }

        .project-card:hover::after {
            left: 100%;
        }

        .project-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-hover);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .project-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 1rem;
            gap: 1rem;
        }

        .project-title-area {
            flex: 1;
        }

        .project-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .project-title:hover {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .project-rank {
            background: var(--primary-gradient);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 700;
            flex-shrink: 0;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .topics {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .topic-tag {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid var(--border-primary);
            transition: all 0.3s ease;
        }

        .topic-tag:hover {
            background: var(--primary-gradient);
            color: white;
            transform: translateY(-2px);
        }

        .project-description {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .project-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
            padding: 0.75rem;
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-primary);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-2px);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .stat-icon {
            font-size: 1.25rem;
        }

        .stat-value {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .project-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 0.875rem;
            color: var(--text-secondary);
            padding-top: 1rem;
            border-top: 1px solid var(--border-secondary);
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .author-info a {
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .author-info a:hover {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Loading & Error States */
        .loading {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-secondary);
        }

        .spinner {
            width: 48px;
            height: 48px;
            border: 3px solid var(--border-primary);
            border-top: 3px solid rgba(102, 126, 234, 1);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message,
        .no-results {
            text-align: center;
            color: var(--text-secondary);
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            padding: 2rem;
            margin: 2rem 0;
        }

        /* Footer */
        footer {
            text-align: center;
            color: var(--text-muted);
            font-size: 0.9rem;
            padding: 2rem;
            margin-top: 2rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            h1 {
                font-size: 2.5rem;
            }
            
            .projects-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-grid {
                grid-template-columns: 1fr;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .tab-button {
                width: 100%;
            }
            
            .project-stats {
                grid-template-columns: 1fr;
            }
            
            .project-meta {
                flex-direction: column;
                align-items: flex-start;
            }
        }

        @media (max-width: 480px) {
            .logo-container {
                flex-direction: column;
            }
            
            .stats-info {
                flex-direction: column;
                gap: 1rem;
            }
            
            .project-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .project-rank {
                align-self: flex-end;
            }
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-gradient);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--accent-gradient);
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>
    
    <div class="container">
        <header class="glass-container">
            <div class="logo-container">
                <div class="logo">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 80" width="240" height="80">
                        <!-- GitHub猫图标 -->
                        <path fill="#f0f6fc" d="M30 15c-8.3 0-15 6.7-15 15 0 6.6 4.3 12.2 10.3 14.2.8.1 1-.3 1-.7v-2.6c-4.2.9-5.1-2-5.1-2-.7-1.7-1.7-2.2-1.7-2.2-1.4-.9.1-.9.1-.9 1.5.1 2.3 1.5 2.3 1.5 1.3 2.3 3.5 1.6 4.4 1.2.1-1 .5-1.6 1-2-3.3-.4-6.8-1.7-6.8-7.4 0-1.6.6-3 1.5-4-.2-.4-.7-1.9.1-4 0 0 1.3-.4 4.1 1.5 1.2-.3 2.5-.5 3.8-.5 1.3 0 2.6.2 3.8.5 2.9-1.9 4.1-1.5 4.1-1.5.8 2.1.3 3.6.1 4 1 1 1.5 2.4 1.5 4 0 5.8-3.5 7-6.8 7.4.5.5 1 1.4 1 2.8v4.1c0 .4.3.9 1 .7 6-2 10.2-7.6 10.2-14.2C45 21.7 38.3 15 30 15z"/>
                        
                        <!-- 统计条形图 - 渐变色 -->
                        <defs>
                            <linearGradient id="grad1" x1="0%" y1="0%" x2="0%" y2="100%">
                                <stop offset="0%" style="stop-color:#34D058;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#28a745;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="grad2" x1="0%" y1="0%" x2="0%" y2="100%">
                                <stop offset="0%" style="stop-color:#F85149;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#d73a49;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="grad3" x1="0%" y1="0%" x2="0%" y2="100%">
                                <stop offset="0%" style="stop-color:#9ECBFF;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#79b8ff;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="grad4" x1="0%" y1="0%" x2="0%" y2="100%">
                                <stop offset="0%" style="stop-color:#FFDF5D;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#ffcc02;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="grad5" x1="0%" y1="0%" x2="0%" y2="100%">
                                <stop offset="0%" style="stop-color:#8B949E;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#6a737d;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        
                        <rect x="70" y="25" width="12" height="30" fill="url(#grad1)" rx="2">
                            <animate attributeName="height" values="25;35;30" dur="2s" repeatCount="indefinite"/>
                            <animate attributeName="y" values="30;20;25" dur="2s" repeatCount="indefinite"/>
                        </rect>
                        <rect x="88" y="20" width="12" height="35" fill="url(#grad2)" rx="2">
                            <animate attributeName="height" values="30;40;35" dur="2.5s" repeatCount="indefinite"/>
                            <animate attributeName="y" values="25;15;20" dur="2.5s" repeatCount="indefinite"/>
                        </rect>
                        <rect x="106" y="15" width="12" height="40" fill="url(#grad3)" rx="2">
                            <animate attributeName="height" values="35;45;40" dur="3s" repeatCount="indefinite"/>
                            <animate attributeName="y" values="20;10;15" dur="3s" repeatCount="indefinite"/>
                        </rect>
                        <rect x="124" y="30" width="12" height="25" fill="url(#grad4)" rx="2">
                            <animate attributeName="height" values="20;30;25" dur="2.2s" repeatCount="indefinite"/>
                            <animate attributeName="y" values="35;25;30" dur="2.2s" repeatCount="indefinite"/>
                        </rect>
                        <rect x="142" y="23" width="12" height="32" fill="url(#grad5)" rx="2">
                            <animate attributeName="height" values="27;37;32" dur="2.8s" repeatCount="indefinite"/>
                            <animate attributeName="y" values="28;18;23" dur="2.8s" repeatCount="indefinite"/>
                        </rect>
                        
                        <!-- 文字 -->
                        <text x="70" y="70" font-family="Arial, sans-serif" font-weight="bold" font-size="14" fill="#f0f6fc">GitHub 趋势统计</text>
                    </svg>
                </div>
            </div>
            
            <h1>GitHub Trending Stats</h1>
            <p class="subtitle">🚀 实时追踪 GitHub 最受欢迎的开源项目</p>
            
            <div class="stats-info">
                <span id="last-update">准备加载数据...</span>
                <a href="https://github.com/m2kall/github-trending-stats" target="_blank" class="github-link">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 ********** 1.21 1.87.87 **********-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 *********-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 **********.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 **********.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .***********.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"/>
                    </svg>
                    GitHub 仓库
                </a>
            </div>
        </header>

        <div class="filter-controls glass-container">
            <div class="filter-grid">
                <div class="filter-group">
                    <label for="search-input">🔍 搜索项目</label>
                    <input type="text" id="search-input" class="filter-input" placeholder="输入项目名称或描述...">
                </div>
                <div class="filter-group">
                    <label for="language-filter">💻 编程语言</label>
                    <select id="language-filter" class="filter-select">
                        <option value="all">全部语言</option>
                        <option value="JavaScript">JavaScript</option>
                        <option value="Python">Python</option>
                        <option value="Java">Java</option>
                        <option value="TypeScript">TypeScript</option>
                        <option value="Go">Go</option>
                        <option value="Rust">Rust</option>
                        <option value="C++">C++</option>
                        <option value="C#">C#</option>
                        <option value="PHP">PHP</option>
                        <option value="Ruby">Ruby</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="sort-filter">📊 排序方式</label>
                    <select id="sort-filter" class="filter-select">
                        <option value="stars">⭐ Stars</option>
                        <option value="forks">🔄 Forks</option>
                        <option value="created">📅 创建时间</option>
                        <option value="updated">🔄 更新时间</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button id="refresh-btn" class="refresh-btn">🔄 刷新数据</button>
                </div>
                <div class="results-count" id="results-count"></div>
            </div>
        </div>

        <nav class="tabs">
            <button class="tab-button active" data-tab="weekly">📊 周榜</button>
            <button class="tab-button" data-tab="monthly">📈 月榜</button>
            <button class="tab-button" data-tab="quarterly">📉 季榜</button>
            <button class="tab-button" data-tab="yearly">🏆 年榜</button>
        </nav>

        <main>
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>正在加载 GitHub 热门项目数据...</p>
            </div>

            <div id="weekly" class="tab-content active">
                <h2>📊 周榜 - 过去7天最受欢迎的新项目</h2>
                <div class="projects-grid" id="weekly-projects"></div>
            </div>

            <div id="monthly" class="tab-content">
                <h2>📈 月榜 - 过去30天最受欢迎的新项目</h2>
                <div class="projects-grid" id="monthly-projects"></div>
            </div>

            <div id="quarterly" class="tab-content">
                <h2>📉 季榜 - 过去90天最受欢迎的新项目</h2>
                <div class="projects-grid" id="quarterly-projects"></div>
            </div>

            <div id="yearly" class="tab-content">
                <h2>🏆 年榜 - 过去365天最受欢迎的新项目</h2>
                <div class="projects-grid" id="yearly-projects"></div>
            </div>
        </main>

        <footer>
            <p>
                🚀 部署于 Railway.app | 
                🤖 自动获取 GitHub API 数据 | 
                ⏰ 每小时自动更新 | 
                💻 开源项目统计工具
            </p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
