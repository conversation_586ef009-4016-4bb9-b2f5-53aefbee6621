* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
    line-height: 1.6;
    color: #24292f;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 40px 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.1rem;
    color: #656d76;
    margin-bottom: 20px;
}

.stats-info {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
    margin-top: 20px;
}

#last-update {
    color: #656d76;
    font-size: 0.9rem;
}

.github-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    color: #24292f;
    text-decoration: none;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 8px;
    border: 2px solid #d0d7de;
    transition: all 0.2s ease;
}

.github-link:hover {
    background-color: #f6f8fa;
    border-color: #24292f;
}

.tabs {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.tab-button {
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    color: #24292f;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.tab-button:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

.tab-button.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

main {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 30px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.tab-content h2 {
    margin-bottom: 25px;
    color: #24292f;
    font-size: 1.5rem;
    text-align: center;
}

.projects-grid {
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.project-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #d0d7de;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.project-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.project-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 15px;
}

.project-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #34D058;
    text-decoration: none;
    flex: 1;
    margin-right: 10px;
}

.project-title:hover {
    text-decoration: underline;
}

.project-rank {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    flex-shrink: 0;
}

.project-description {
    color: #656d76;
    margin-bottom: 15px;
    line-height: 1.5;
}

.project-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    color: #656d76;
}

.stat-item strong {
    color: #24292f;
}

.project-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.85rem;
    color: #656d76;
    border-top: 1px solid #f6f8fa;
    padding-top: 12px;
    flex-wrap: wrap;
    gap: 10px;
}

.loading {
    text-align: center;
    padding: 60px 20px;
    color: #656d76;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

footer {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    padding: 20px;
}

.error-message {
    text-align: center;
    color: #d1242f;
    background: #fff8f8;
    border: 1px solid #f8d7da;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .projects-grid {
        grid-template-columns: 1fr;
    }
    
    .tabs {
        flex-direction: column;
        align-items: center;
    }
    
    .tab-button {
        width: 200px;
    }
    
    .stats-info {
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    header {
        padding: 30px 20px;
    }
    
    main {
        padding: 20px;
    }
    
    .project-stats {
        grid-template-columns: 1fr;
    }
    
    .project-meta {
        flex-direction: column;
        align-items: flex-start;
    }
}