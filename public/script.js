/**
 * GitHub Trending Stats 前端脚本
 * 2025版增强版 - 现代化设计与交互
 */

class EnhancedGitHubTrendingStats {
    constructor() {
        this.apiBase = window.location.origin;
        this.currentFilter = {
            period: 'weekly',
            language: 'all',
            search: '',
            sortBy: 'stars'
        };
        this.projects = {};
        this.languageStats = {};
        this.trendsHistory = [];
        this.init();
    }

    async init() {
        console.log('🚀 初始化 GitHub Trending Stats 增强版...');
        this.setupEventListeners();
        await this.loadData();
    }

    setupEventListeners() {
        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 搜索功能
        const searchInput = document.getElementById('search-input');
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.currentFilter.search = e.target.value;
                this.filterAndDisplayProjects();
            }, 300);
        });

        // 语言筛选
        document.getElementById('language-filter').addEventListener('change', (e) => {
            this.currentFilter.language = e.target.value;
            this.filterAndDisplayProjects();
        });

        // 排序
        document.getElementById('sort-filter').addEventListener('change', (e) => {
            this.currentFilter.sortBy = e.target.value;
            this.filterAndDisplayProjects();
        });

        // 刷新按钮
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.refreshData();
        });
    }

    async loadData() {
        try {
            document.getElementById('loading').style.display = 'block';
            
            // 并行加载所有数据
            const [statsData, languageData, trendsData] = await Promise.all([
                this.fetchWithRetry(`${this.apiBase}/api/stats`),
                this.fetchWithRetry(`${this.apiBase}/api/stats/by-language`),
                this.fetchWithRetry(`${this.apiBase}/api/trends/history`)
            ]);

            // 存储数据
            this.projects = statsData;
            this.languageStats = languageData;
            this.trendsHistory = trendsData;

            // 更新最后更新时间
            if (statsData.lastUpdate) {
                document.getElementById('last-update').textContent = 
                    `最后更新: ${new Date(statsData.lastUpdate).toLocaleString('zh-CN')}`;
            }

            // 初始显示
            this.filterAndDisplayProjects();
            
            // 添加语言选项
            this.updateLanguageOptions();
            
        } catch (error) {
            console.error('加载数据失败:', error);
            this.showError('加载数据失败，请检查网络连接或稍后重试');
        } finally {
            document.getElementById('loading').style.display = 'none';
        }
    }
    
    async fetchWithRetry(url, options = {}, retries = 3) {
        try {
            const response = await fetch(url, options);
            
            if (!response.ok) {
                throw new Error(`HTTP错误 ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            if (retries > 0) {
                console.log(`请求失败，剩余重试次数: ${retries-1}`);
                await new Promise(resolve => setTimeout(resolve, 1000));
                return this.fetchWithRetry(url, options, retries - 1);
            }
            throw error;
        }
    }

    filterAndDisplayProjects() {
        const currentPeriod = this.currentFilter.period;
        let projects = this.projects[currentPeriod] || [];

        // 语言筛选
        if (this.currentFilter.language !== 'all') {
            projects = projects.filter(p => p.language === this.currentFilter.language);
        }

        // 搜索筛选
        if (this.currentFilter.search) {
            const searchTerm = this.currentFilter.search.toLowerCase();
            projects = projects.filter(p => 
                (p.name && p.name.toLowerCase().includes(searchTerm)) ||
                (p.description && p.description.toLowerCase().includes(searchTerm)) ||
                (p.author && p.author.toLowerCase().includes(searchTerm)) ||
                (p.topics && p.topics.some(topic => topic.toLowerCase().includes(searchTerm)))
            );
        }

        // 排序
        projects.sort((a, b) => {
            switch (this.currentFilter.sortBy) {
                case 'forks':
                    return b.forks - a.forks;
                case 'created':
                    return new Date(b.created_at) - new Date(a.created_at);
                case 'updated':
                    return new Date(b.updated_at) - new Date(a.updated_at);
                default: // stars
                    return b.stars - a.stars;
            }
        });

        this.renderProjects(currentPeriod, projects);
        this.updateResultsCount(projects.length);
    }

    renderProjects(category, projects) {
        const container = document.getElementById(`${category}-projects`);
        
        if (!projects || projects.length === 0) {
            container.innerHTML = '<div class="no-results">没有找到匹配的项目 🔍</div>';
            return;
        }

        container.innerHTML = projects.map((project, index) => {
            return this.createEnhancedProjectCard(project, index + 1);
        }).join('');
        
        // 渲染趋势图
        this.renderTrendCharts(projects);
    }

    renderTrendCharts(projects) {
        projects.forEach(project => {
            const chartContainer = document.querySelector(`.project-trend-chart[data-project-id="${project.github_id}"]`);
            if (chartContainer) {
                const projectHistory = this.trendsHistory.filter(trend => trend.github_id === project.github_id);
                if (projectHistory.length > 1) { // 至少需要两个点来绘制趋势
                    this.renderTrendChart(chartContainer, projectHistory);
                }
            }
        });
    }

    renderTrendChart(container, historyData) {
        // 确保容器是空的
        container.innerHTML = '';

        const canvas = document.createElement('canvas');
        container.appendChild(canvas);

        // 准备图表数据
        const labels = historyData.map(data => new Date(data.date).toLocaleDateString());
        const data = historyData.map(data => data.total_projects); // 假设 total_projects 是趋势数据

        new Chart(canvas, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Stars Trend', // 或者其他合适的标签
                    data: data,
                    borderColor: 'rgba(75, 192, 192, 1)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderWidth: 1,
                    pointRadius: 3,
                    pointBackgroundColor: 'rgba(75, 192, 192, 1)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    createEnhancedProjectCard(project, rank) {
        // 格式化数字
        const formatNumber = (num) => {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num;
        };

        // 处理可能的空值
        const description = project.description || '暂无描述';
        const language = project.language || 'Unknown';
        const topics = project.topics || [];
        const topicsHtml = topics.length > 0 
            ? topics.slice(0, 5).map(topic => `<span class="topic-tag">${topic}</span>`).join('')
            : '<span class="topic-tag">无标签</span>';

        // 计算项目年龄
        const createdDate = new Date(project.created_at);
        const now = new Date();
        const ageInDays = Math.floor((now - createdDate) / (1000 * 60 * 60 * 24));
        let ageText = '';
        
        if (ageInDays < 7) {
            ageText = `${ageInDays}天前`;
        } else if (ageInDays < 30) {
            ageText = `${Math.floor(ageInDays / 7)}周前`;
        } else if (ageInDays < 365) {
            ageText = `${Math.floor(ageInDays / 30)}个月前`;
        } else {
            ageText = `${Math.floor(ageInDays / 365)}年前`;
        }

        return `
            <div class="project-card">
                <div class="project-header">
                    <div class="project-title-area">
                        <a href="${project.url}" target="_blank" class="project-title">${project.name}</a>
                        <div class="author-info">
                            by <a href="${project.author_url}" target="_blank">${project.author}</a>
                        </div>
                    </div>
                    <div class="project-rank">#${rank}</div>
                </div>
                
                <div class="topics">
                    ${topicsHtml}
                </div>
                
                <p class="project-description">${description}</p>
                
                <div class="project-stats">
                    <div class="stat-item">
                        <div class="stat-icon">⭐</div>
                        <div class="stat-value">${formatNumber(project.stars)}</div>
                        <div class="stat-label">Stars</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">🔄</div>
                        <div class="stat-value">${formatNumber(project.forks)}</div>
                        <div class="stat-label">Forks</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">💻</div>
                        <div class="stat-value">${language}</div>
                        <div class="stat-label">语言</div>
                    </div>
                </div>
                
                <div class="project-meta">
                    <span>创建于 ${ageText}</span>
                    <span>更新于 ${new Date(project.updated_at).toLocaleDateString('zh-CN')}</span>
                </div>
                
                <!-- 添加趋势图容器 -->
                <div class="project-trend-chart" data-project-id="${project.github_id}"></div>
            </div>
        `;
    }

    switchTab(tabId) {
        // 更新当前过滤器
        this.currentFilter.period = tabId;
        
        // 更新标签页状态
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.toggle('active', button.dataset.tab === tabId);
        });
        
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.toggle('active', content.id === tabId);
        });
        
        // 重新过滤和显示项目
        this.filterAndDisplayProjects();
    }

    updateResultsCount(count) {
        const resultsCountElement = document.getElementById('results-count');
        resultsCountElement.textContent = `显示 ${count} 个项目`;
    }

    updateLanguageOptions() {
        const languageFilter = document.getElementById('language-filter');
        const currentValue = languageFilter.value;
        
        // 清除现有选项（保留"全部语言"）
        while (languageFilter.options.length > 1) {
            languageFilter.remove(1);
        }
        
        // 获取所有可用语言
        const languages = new Set();
        
        // 从项目中收集语言
        for (const period in this.projects) {
            if (Array.isArray(this.projects[period])) {
                this.projects[period].forEach(project => {
                    if (project.language && project.language !== 'Unknown') {
                        languages.add(project.language);
                    }
                });
            }
        }
        
        // 从语言统计中收集语言
        for (const language in this.languageStats) {
            languages.add(language);
        }
        
        // 添加语言选项
        const sortedLanguages = Array.from(languages).sort();
        sortedLanguages.forEach(language => {
            const option = document.createElement('option');
            option.value = language;
            option.textContent = language;
            languageFilter.appendChild(option);
        });
        
        // 恢复之前的选择
        if (sortedLanguages.includes(currentValue)) {
            languageFilter.value = currentValue;
        }
    }

    async refreshData() {
        try {
            const refreshBtn = document.getElementById('refresh-btn');
            refreshBtn.disabled = true;
            refreshBtn.textContent = '🔄 刷新中...';
            
            // 触发服务器数据刷新
            await fetch(`${this.apiBase}/api/data/refresh`, { method: 'POST' });
            
            // 等待一段时间让服务器处理
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 重新加载数据
            await this.loadData();
            
            // 显示成功消息
            this.showToast('✅ 数据已成功刷新');
        } catch (error) {
            console.error('刷新数据失败:', error);
            this.showToast('❌ 刷新数据失败，请稍后重试', 'error');
        } finally {
            const refreshBtn = document.getElementById('refresh-btn');
            refreshBtn.disabled = false;
            refreshBtn.textContent = '🔄 刷新数据';
        }
    }

    showError(message) {
        const mainContent = document.querySelector('main');
        const errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        errorElement.innerHTML = `
            <h3>❌ 出错了</h3>
            <p>${message}</p>
            <button id="retry-btn" class="refresh-btn">重试</button>
        `;
        
        // 清除现有内容
        mainContent.innerHTML = '';
        mainContent.appendChild(errorElement);
        
        // 添加重试按钮事件
        document.getElementById('retry-btn').addEventListener('click', () => {
            this.loadData();
        });
    }

    showToast(message, type = 'success') {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        
        // 添加到页面
        document.body.appendChild(toast);
        
        // 显示动画
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);
        
        // 自动消失
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 添加toast样式
    const style = document.createElement('style');
    style.textContent = `
        .toast {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%) translateY(100px);
            background: var(--bg-glass);
            backdrop-filter: blur(10px);
            color: var(--text-primary);
            padding: 12px 24px;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-glass);
            z-index: 1000;
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--border-primary);
        }
        
        .toast.success {
            border-left: 4px solid #43e97b;
        }
        
        .toast.error {
            border-left: 4px solid #f5576c;
        }
        
        .toast.show {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }
    `;
    document.head.appendChild(style);
    
    // 初始化应用
    window.app = new EnhancedGitHubTrendingStats();
});
