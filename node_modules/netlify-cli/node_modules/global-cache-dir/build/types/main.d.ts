/**
 * Get the global cache directory.
 * Ensures the directory exists.
 * The cache directory location is OS-specific.
 *
 * @example
 * ```js
 * const cacheDir = await globalCacheDir('myapp')
 * // Depending on the OS:
 * //  => '/Users/<USER>/Library/Caches/myapp'
 * //  => '/home/<USER>/.cache/myapp'
 * //  => 'C:\Users\<USER>\AppData\Local\myapp\Cache'
 * ```
 */
export default function globalCacheDir(name: string): Promise<string>
