  [38;5;164;1mtabtab [0mParsing env. CWORD: 2, COMP_POINT: 12, COMP_LINE: tabtab --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+3ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+34ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 2, COMP_POINT: 12, COMP_LINE: tabtab --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+4ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+27ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 2, COMP_POINT: 12, COMP_LINE: tabtab --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+6ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+32ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 2, COMP_POINT: 12, COMP_LINE: tabtab --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+5ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+24ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 2, COMP_POINT: 12, COMP_LINE: tabtab --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+7ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+25ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
2018-10-03T16:08:10.083Z tabtab:prompt answers bash
2018-10-03T16:08:10.084Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:08:12.021Z tabtab:prompt Validating input /tmp/foo
2018-10-03T16:08:12.024Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T16:08:12.025Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T16:08:12.025Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:08:12.026Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T16:08:12.027Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:08:12.027Z tabtab:installer with undefined
2018-10-03T16:08:12.773Z tabtab:prompt answers bash
2018-10-03T16:08:12.774Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:08:13.186Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T16:08:13.186Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T16:08:13.186Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T16:08:13.187Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:08:13.188Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T16:08:13.188Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:08:13.188Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: 2, COMP_POINT: 12, COMP_LINE: tabtab --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+3ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+3ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+24ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
2018-10-03T16:19:07.816Z tabtab:prompt answers bash
2018-10-03T16:19:07.818Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:19:09.752Z tabtab:prompt Validating input /tmp/foo
2018-10-03T16:19:09.757Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T16:19:09.757Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T16:19:09.758Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:19:09.759Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T16:19:09.761Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:19:09.761Z tabtab:installer with undefined
2018-10-03T16:19:10.515Z tabtab:prompt answers bash
2018-10-03T16:19:10.516Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:19:10.903Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T16:19:10.903Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T16:19:10.903Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T16:19:10.904Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:19:10.905Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T16:19:10.906Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:19:10.906Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+5ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+4ms[0m
2018-10-03T16:27:24.511Z tabtab:prompt answers bash
2018-10-03T16:27:24.512Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:27:26.453Z tabtab:prompt Validating input /tmp/foo
2018-10-03T16:27:26.455Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T16:27:26.455Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T16:27:26.456Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:27:26.457Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T16:27:26.458Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:27:26.458Z tabtab:installer with undefined
2018-10-03T16:27:27.175Z tabtab:prompt answers bash
2018-10-03T16:27:27.179Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:27:27.611Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T16:27:27.611Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T16:27:27.611Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T16:27:27.612Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:27:27.613Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T16:27:27.613Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:27:27.614Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+542ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
2018-10-03T16:27:52.841Z tabtab:prompt answers bash
2018-10-03T16:27:52.842Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:27:54.789Z tabtab:prompt Validating input /tmp/foo
2018-10-03T16:27:54.790Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T16:27:54.791Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T16:27:54.791Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:27:54.793Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T16:27:54.794Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:27:54.795Z tabtab:installer with undefined
2018-10-03T16:27:55.507Z tabtab:prompt answers bash
2018-10-03T16:27:55.508Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:27:55.946Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T16:27:55.946Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T16:27:55.947Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T16:27:55.947Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:27:55.949Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T16:27:55.950Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:27:55.950Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+7ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+38ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+4ms[0m
2018-10-03T16:38:44.779Z tabtab:prompt answers bash
2018-10-03T16:38:44.780Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:38:46.734Z tabtab:prompt Validating input /tmp/foo
2018-10-03T16:38:46.743Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T16:38:46.744Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T16:38:46.747Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:38:46.749Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T16:38:46.750Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:38:46.751Z tabtab:installer with undefined
2018-10-03T16:38:47.440Z tabtab:prompt answers bash
2018-10-03T16:38:47.442Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:38:47.882Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T16:38:47.882Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T16:38:47.882Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T16:38:47.882Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:38:47.883Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T16:38:47.884Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:38:47.884Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+32ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
2018-10-03T16:39:14.452Z tabtab:prompt answers bash
2018-10-03T16:39:14.453Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:39:16.383Z tabtab:prompt Validating input /tmp/foo
2018-10-03T16:39:16.384Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T16:39:16.385Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T16:39:16.386Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:39:16.387Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T16:39:16.388Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:39:16.388Z tabtab:installer with undefined
2018-10-03T16:39:17.083Z tabtab:prompt answers bash
2018-10-03T16:39:17.084Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:39:17.523Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T16:39:17.524Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T16:39:17.524Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T16:39:17.525Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:39:17.527Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T16:39:17.528Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:39:17.528Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+36ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+1ms[0m
2018-10-03T16:39:42.266Z tabtab:prompt answers bash
2018-10-03T16:39:42.267Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:39:44.201Z tabtab:prompt Validating input /tmp/foo
2018-10-03T16:39:44.203Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T16:39:44.203Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T16:39:44.203Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:39:44.204Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T16:39:44.205Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:39:44.205Z tabtab:installer with undefined
2018-10-03T16:39:44.905Z tabtab:prompt answers bash
2018-10-03T16:39:44.906Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:39:45.351Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T16:39:45.351Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T16:39:45.351Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T16:39:45.351Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:39:45.352Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T16:39:45.353Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:39:45.353Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+27ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
2018-10-03T16:40:21.556Z tabtab:prompt answers bash
2018-10-03T16:40:21.557Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:40:23.502Z tabtab:prompt Validating input /tmp/foo
2018-10-03T16:40:23.505Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T16:40:23.505Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T16:40:23.506Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:40:23.508Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T16:40:23.509Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:40:23.509Z tabtab:installer with undefined
2018-10-03T16:40:24.203Z tabtab:prompt answers bash
2018-10-03T16:40:24.204Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:40:24.637Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T16:40:24.637Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T16:40:24.638Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T16:40:24.638Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:40:24.639Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T16:40:24.639Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:40:24.639Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+25ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+1ms[0m
2018-10-03T16:40:47.640Z tabtab:prompt answers bash
2018-10-03T16:40:47.641Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:40:49.574Z tabtab:prompt Validating input /tmp/foo
2018-10-03T16:40:49.576Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T16:40:49.576Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T16:40:49.576Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:40:49.577Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T16:40:49.578Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:40:49.578Z tabtab:installer with undefined
2018-10-03T16:40:50.274Z tabtab:prompt answers bash
2018-10-03T16:40:50.275Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:40:50.721Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T16:40:50.721Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T16:40:50.722Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T16:40:50.722Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:40:50.722Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T16:40:50.723Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:40:50.723Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+51ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+3ms[0m
2018-10-03T16:51:15.681Z tabtab:prompt answers bash
2018-10-03T16:51:15.683Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:51:17.545Z tabtab:prompt Validating input /tmp/foo
2018-10-03T16:51:17.547Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T16:51:17.548Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T16:51:17.548Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:51:17.549Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T16:51:17.550Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:51:17.550Z tabtab:installer with undefined
2018-10-03T16:51:18.239Z tabtab:prompt answers bash
2018-10-03T16:51:18.241Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:51:18.687Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T16:51:18.687Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T16:51:18.687Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T16:51:18.688Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:51:18.688Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T16:51:18.689Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:51:18.689Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+36ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+4ms[0m
2018-10-03T16:52:14.357Z tabtab:prompt answers bash
2018-10-03T16:52:14.359Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:52:16.291Z tabtab:prompt Validating input /tmp/foo
2018-10-03T16:52:16.293Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T16:52:16.293Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T16:52:16.294Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:52:16.294Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T16:52:16.295Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:52:16.295Z tabtab:installer with undefined
2018-10-03T16:52:16.978Z tabtab:prompt answers bash
2018-10-03T16:52:16.979Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:52:17.429Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T16:52:17.430Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T16:52:17.430Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T16:52:17.431Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:52:17.432Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T16:52:17.433Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:52:17.433Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+1ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+25ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+6ms[0m
2018-10-03T16:52:55.206Z tabtab:prompt answers bash
2018-10-03T16:52:55.207Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:52:57.144Z tabtab:prompt Validating input /tmp/foo
2018-10-03T16:52:57.147Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T16:52:57.148Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T16:52:57.148Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:52:57.150Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T16:52:57.150Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:52:57.151Z tabtab:installer with undefined
2018-10-03T16:52:57.823Z tabtab:prompt answers bash
2018-10-03T16:52:57.825Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:52:58.282Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T16:52:58.283Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T16:52:58.283Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T16:52:58.283Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:52:58.284Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T16:52:58.284Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:52:58.284Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+32ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
2018-10-03T16:53:44.198Z tabtab:prompt answers bash
2018-10-03T16:53:44.199Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:53:46.130Z tabtab:prompt Validating input /tmp/foo
2018-10-03T16:53:46.133Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T16:53:46.133Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T16:53:46.133Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:53:46.135Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T16:53:46.137Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:53:46.138Z tabtab:installer with undefined
2018-10-03T16:53:46.823Z tabtab:prompt answers bash
2018-10-03T16:53:46.824Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:53:47.274Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T16:53:47.274Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T16:53:47.274Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T16:53:47.274Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:53:47.275Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T16:53:47.276Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:53:47.276Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+1ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+37ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
2018-10-03T16:54:20.442Z tabtab:prompt answers bash
2018-10-03T16:54:20.443Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:54:22.377Z tabtab:prompt Validating input /tmp/foo
2018-10-03T16:54:22.382Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T16:54:22.382Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T16:54:22.383Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:54:22.384Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T16:54:22.384Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T16:54:22.385Z tabtab:installer with undefined
2018-10-03T16:54:23.089Z tabtab:prompt answers bash
2018-10-03T16:54:23.091Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T16:54:23.533Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T16:54:23.533Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T16:54:23.533Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T16:54:23.533Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:54:23.534Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T16:54:23.534Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T16:54:23.534Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+41ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
2018-10-03T17:00:16.446Z tabtab:prompt answers bash
2018-10-03T17:00:16.447Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:00:18.373Z tabtab:prompt Validating input /tmp/foo
2018-10-03T17:00:18.375Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T17:00:18.375Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T17:00:18.375Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:00:18.376Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T17:00:18.377Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:00:18.377Z tabtab:installer with undefined
2018-10-03T17:00:19.067Z tabtab:prompt answers bash
2018-10-03T17:00:19.068Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:00:19.508Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T17:00:19.508Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T17:00:19.509Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T17:00:19.510Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:00:19.512Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T17:00:19.513Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:00:19.513Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+38ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+4ms[0m
2018-10-03T17:19:31.624Z tabtab:prompt answers bash
2018-10-03T17:19:31.626Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:19:33.550Z tabtab:prompt Validating input /tmp/foo
2018-10-03T17:19:33.552Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T17:19:33.553Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T17:19:33.554Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:19:33.555Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T17:19:33.556Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:19:33.556Z tabtab:installer with undefined
2018-10-03T17:19:34.263Z tabtab:prompt answers bash
2018-10-03T17:19:34.264Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:19:34.703Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T17:19:34.704Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T17:19:34.704Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T17:19:34.704Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:19:34.706Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T17:19:34.706Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:19:34.707Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+45ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+3ms[0m
2018-10-03T17:28:28.225Z tabtab:prompt answers bash
2018-10-03T17:28:28.226Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:28:30.131Z tabtab:prompt Validating input /tmp/foo
2018-10-03T17:28:30.132Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T17:28:30.133Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T17:28:30.133Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:28:30.133Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T17:28:30.134Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:28:30.134Z tabtab:installer with undefined
2018-10-03T17:28:30.865Z tabtab:prompt answers bash
2018-10-03T17:28:30.867Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:28:31.282Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T17:28:31.282Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T17:28:31.282Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T17:28:31.282Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:28:31.283Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T17:28:31.283Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:28:31.283Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+5ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+33ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
2018-10-03T17:30:29.540Z tabtab:prompt answers bash
2018-10-03T17:30:29.543Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:30:31.473Z tabtab:prompt Validating input /tmp/foo
2018-10-03T17:30:31.476Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T17:30:31.477Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T17:30:31.478Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:30:31.482Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T17:30:31.483Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:30:31.483Z tabtab:installer with undefined
2018-10-03T17:30:32.203Z tabtab:prompt answers bash
2018-10-03T17:30:32.205Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:30:32.620Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T17:30:32.620Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T17:30:32.620Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T17:30:32.621Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:30:32.622Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T17:30:32.622Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:30:32.623Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+1ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+34ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
2018-10-03T17:31:37.404Z tabtab:prompt answers bash
2018-10-03T17:31:37.406Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:31:39.303Z tabtab:prompt Validating input /tmp/foo
2018-10-03T17:31:39.306Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T17:31:39.307Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T17:31:39.307Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:31:39.309Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T17:31:39.309Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:31:39.310Z tabtab:installer with undefined
2018-10-03T17:31:40.060Z tabtab:prompt answers bash
2018-10-03T17:31:40.062Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:31:40.464Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T17:31:40.464Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T17:31:40.464Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T17:31:40.465Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:31:40.466Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T17:31:40.466Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:31:40.466Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+4ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+1ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+56ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+7ms[0m
2018-10-03T17:33:19.879Z tabtab:prompt answers bash
2018-10-03T17:33:19.884Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:33:21.708Z tabtab:prompt Validating input /tmp/foo
2018-10-03T17:33:21.710Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T17:33:21.710Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T17:33:21.710Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:33:21.711Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T17:33:21.712Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:33:21.712Z tabtab:installer with undefined
2018-10-03T17:33:22.437Z tabtab:prompt answers bash
2018-10-03T17:33:22.439Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:33:22.866Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T17:33:22.866Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T17:33:22.866Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T17:33:22.867Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:33:22.867Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T17:33:22.868Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:33:22.868Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+4ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+31ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
2018-10-03T17:58:06.284Z tabtab:prompt answers bash
2018-10-03T17:58:06.286Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:58:08.249Z tabtab:prompt Validating input /tmp/foo
2018-10-03T17:58:08.254Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T17:58:08.255Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T17:58:08.256Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:58:08.259Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T17:58:08.260Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:58:08.260Z tabtab:installer with undefined
2018-10-03T17:58:08.922Z tabtab:prompt answers bash
2018-10-03T17:58:08.923Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:58:09.410Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T17:58:09.410Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T17:58:09.411Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T17:58:09.412Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:58:09.415Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T17:58:09.416Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:58:09.416Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+29ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+4ms[0m
2018-10-03T17:59:53.599Z tabtab:prompt answers bash
2018-10-03T17:59:53.600Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:59:55.580Z tabtab:prompt Validating input /tmp/foo
2018-10-03T17:59:55.581Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T17:59:55.581Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T17:59:55.582Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:59:55.582Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T17:59:55.582Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T17:59:55.582Z tabtab:installer with undefined
2018-10-03T17:59:56.344Z tabtab:prompt answers bash
2018-10-03T17:59:56.345Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T17:59:56.719Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T17:59:56.719Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T17:59:56.719Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T17:59:56.719Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:59:56.720Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T17:59:56.720Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T17:59:56.720Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+29ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+1ms[0m
2018-10-03T18:02:15.758Z tabtab:prompt answers bash
2018-10-03T18:02:15.759Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T18:02:17.732Z tabtab:prompt Validating input /tmp/foo
2018-10-03T18:02:17.737Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T18:02:17.737Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T18:02:17.738Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T18:02:17.740Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T18:02:17.742Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T18:02:17.742Z tabtab:installer with undefined
2018-10-03T18:02:18.407Z tabtab:prompt answers bash
2018-10-03T18:02:18.408Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T18:02:18.899Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T18:02:18.899Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T18:02:18.900Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T18:02:18.900Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T18:02:18.901Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T18:02:18.902Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T18:02:18.902Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+21ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+1ms[0m
2018-10-03T18:18:19.304Z tabtab:prompt answers bash
2018-10-03T18:18:19.305Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T18:18:21.280Z tabtab:prompt Validating input /tmp/foo
2018-10-03T18:18:21.285Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T18:18:21.286Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T18:18:21.287Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T18:18:21.288Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T18:18:21.290Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T18:18:21.290Z tabtab:installer with undefined
2018-10-03T18:18:21.953Z tabtab:prompt answers bash
2018-10-03T18:18:21.955Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T18:18:22.438Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T18:18:22.439Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T18:18:22.439Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T18:18:22.440Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T18:18:22.442Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T18:18:22.443Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T18:18:22.443Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+3ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+39ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+18ms[0m
2018-10-03T18:19:44.201Z tabtab:prompt answers bash
2018-10-03T18:19:44.202Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T18:19:46.113Z tabtab:prompt Validating input /tmp/foo
2018-10-03T18:19:46.120Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T18:19:46.120Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T18:19:46.121Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T18:19:46.124Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T18:19:46.126Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T18:19:46.126Z tabtab:installer with undefined
2018-10-03T18:19:46.794Z tabtab:prompt answers bash
2018-10-03T18:19:46.796Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T18:19:47.278Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T18:19:47.278Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T18:19:47.279Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T18:19:47.279Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T18:19:47.280Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T18:19:47.281Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T18:19:47.281Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+7ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+22ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
2018-10-03T18:22:53.906Z tabtab:prompt answers bash
2018-10-03T18:22:53.907Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T18:22:55.879Z tabtab:prompt Validating input /tmp/foo
2018-10-03T18:22:55.889Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T18:22:55.890Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T18:22:55.891Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T18:22:55.893Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T18:22:55.893Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T18:22:55.893Z tabtab:installer with undefined
2018-10-03T18:22:56.547Z tabtab:prompt answers bash
2018-10-03T18:22:56.548Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T18:22:57.033Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T18:22:57.033Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T18:22:57.033Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T18:22:57.034Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T18:22:57.035Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T18:22:57.035Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T18:22:57.036Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+4ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+1ms[0m
2018-10-03T18:26:38.907Z tabtab:prompt answers bash
2018-10-03T18:26:38.908Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T18:26:40.891Z tabtab:prompt Validating input /tmp/foo
2018-10-03T18:26:40.894Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T18:26:40.895Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T18:26:40.895Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T18:26:40.896Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T18:26:40.897Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T18:26:40.897Z tabtab:installer with undefined
2018-10-03T18:26:41.540Z tabtab:prompt answers bash
2018-10-03T18:26:41.541Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T18:26:42.023Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T18:26:42.023Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T18:26:42.023Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T18:26:42.024Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T18:26:42.025Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T18:26:42.025Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T18:26:42.025Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+5ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+5ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+1ms[0m
2018-10-03T18:27:07.478Z tabtab:prompt answers bash
2018-10-03T18:27:07.479Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T18:27:09.468Z tabtab:prompt Validating input /tmp/foo
2018-10-03T18:27:09.472Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T18:27:09.473Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T18:27:09.474Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T18:27:09.477Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T18:27:09.479Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T18:27:09.479Z tabtab:installer with undefined
2018-10-03T18:27:10.184Z tabtab:prompt answers bash
2018-10-03T18:27:10.185Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T18:27:10.635Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T18:27:10.635Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T18:27:10.635Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T18:27:10.635Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T18:27:10.636Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T18:27:10.637Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T18:27:10.637Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+4ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+1ms[0m
2018-10-03T18:27:22.043Z tabtab:prompt answers bash
2018-10-03T18:27:22.045Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T18:27:24.019Z tabtab:prompt Validating input /tmp/foo
2018-10-03T18:27:24.021Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T18:27:24.021Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T18:27:24.022Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T18:27:24.023Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T18:27:24.024Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T18:27:24.024Z tabtab:installer with undefined
2018-10-03T18:27:24.679Z tabtab:prompt answers bash
2018-10-03T18:27:24.680Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T18:27:25.163Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T18:27:25.163Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T18:27:25.163Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T18:27:25.163Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T18:27:25.164Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T18:27:25.164Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T18:27:25.164Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+3ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+3ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+3ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+6ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+11ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+11ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+7ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+11ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo option"} [38;5;164m+3ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo option"} [38;5;164m+6ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+3ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo option"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+3ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+3ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo option"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo:bar","description":"Foo option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar:foo","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+5ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo option"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo:bar","description":"Foo option"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar:foo","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mAdding tabtab script to ~/.bashrc [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWhich filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (~/.bashrc) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo option"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo:bar","description":"Foo option"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar:foo","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
2018-10-03T19:05:16.542Z tabtab:prompt answers bash
2018-10-03T19:05:16.543Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T19:05:18.431Z tabtab:prompt Validating input /tmp/foo
2018-10-03T19:05:18.432Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T19:05:18.433Z tabtab:installer Adding tabtab script to /tmp/foo
2018-10-03T19:05:18.433Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T19:05:18.434Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T19:05:18.434Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T19:05:18.434Z tabtab:installer with undefined
2018-10-03T19:05:19.108Z tabtab:prompt answers bash
2018-10-03T19:05:19.109Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T19:05:19.553Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T19:05:19.553Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T19:05:19.553Z tabtab:installer Adding tabtab script to ~/.bashrc
2018-10-03T19:05:19.553Z tabtab:installer Which filename /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T19:05:19.554Z tabtab:installer Writing to shell configuration file (~/.bashrc)
2018-10-03T19:05:19.554Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T19:05:19.554Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mCheck filename (~/.bashrc) for "# tabtab source for completion packages" [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mCheck filename (/mnt/c/Users/<USER>/dev/tabtab/.completions/__tabtab.bash) for "# tabtab source for foo package" [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mTabtab line already exists in ~/.bashrc file [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mWriting to shell configuration file (/mnt/c/Users/<USER>/dev/tabtab/.completions/__tabtab.bash) [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mscriptname: /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+3ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo:bar","description":"Foo option"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar:foo","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+4ms[0m
2018-10-03T20:54:10.987Z tabtab:prompt answers bash
2018-10-03T20:54:10.988Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T20:54:12.954Z tabtab:prompt Validating input /tmp/foo
2018-10-03T20:54:12.957Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T20:54:12.959Z tabtab:installer Check filename (/tmp/foo) for "# tabtab source for completion packages"
2018-10-03T20:54:12.963Z tabtab:installer Check filename (/mnt/c/Users/<USER>/dev/tabtab/.completions/__tabtab.undefined) for "# tabtab source for foo package"
2018-10-03T20:54:12.964Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T20:54:12.964Z tabtab:installer with undefined
2018-10-03T20:54:12.977Z tabtab:installer Writing to shell configuration file (/mnt/c/Users/<USER>/dev/tabtab/.completions/__tabtab.undefined)
2018-10-03T20:54:12.977Z tabtab:installer scriptname: /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T20:54:12.981Z tabtab:installer Writing to shell configuration file (/tmp/foo)
2018-10-03T20:54:12.981Z tabtab:installer scriptname: /mnt/c/Users/<USER>/dev/tabtab/.completions/__tabtab.undefined
2018-10-03T20:54:13.637Z tabtab:prompt answers bash
2018-10-03T20:54:13.639Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T20:54:14.123Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T20:54:14.123Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T20:54:14.125Z tabtab:installer Check filename (~/.bashrc) for "# tabtab source for completion packages"
2018-10-03T20:54:14.132Z tabtab:installer Check filename (/mnt/c/Users/<USER>/dev/tabtab/.completions/__tabtab.bash) for "# tabtab source for foo package"
2018-10-03T20:54:14.133Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T20:54:14.133Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
2018-10-03T20:54:14.141Z tabtab:installer Tabtab line already exists in ~/.bashrc file
2018-10-03T20:54:14.143Z tabtab:installer Tabtab line already exists in /mnt/c/Users/<USER>/dev/tabtab/.completions/__tabtab.bash file
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"","completer":"","location":""} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo "} [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo ","completer":"foo-complete"} [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mInstall with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"} [38;5;80m+4ms[0m
  [38;5;80;1mtabtab:installer [0mCheck filename (~/.bashrc) for "# tabtab source for completion packages" [38;5;80m+1ms[0m
  [38;5;80;1mtabtab:installer [0mCheck filename (/mnt/c/Users/<USER>/dev/tabtab/.completions/__tabtab.bash) for "# tabtab source for foo package" [38;5;80m+2ms[0m
  [38;5;80;1mtabtab:installer [0mWriting completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mwith /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh [38;5;80m+0ms[0m
  [38;5;80;1mtabtab:installer [0mTabtab line already exists in ~/.bashrc file [38;5;80m+3ms[0m
  [38;5;80;1mtabtab:installer [0mTabtab line already exists in /mnt/c/Users/<USER>/dev/tabtab/.completions/__tabtab.bash file [38;5;80m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item --foo [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item --bar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+2ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo options"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo option"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo","description":"Foo option"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--foo:bar","description":"Foo option"} [38;5;164m+1ms[0m
  [38;5;164;1mtabtab [0mcompletion item {"name":"--bar:foo","description":"Bar option"} [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mcompletion item barfoo:barfoo is not foobar [38;5;164m+0ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: 3, COMP_POINT: 11, COMP_LINE: foo bar baz [38;5;164m+3ms[0m
  [38;5;164;1mtabtab [0mParsing env. CWORD: undefined, COMP_POINT: undefined, COMP_LINE: undefined [38;5;164m+2ms[0m
2018-10-03T20:55:39.291Z tabtab:prompt answers bash
2018-10-03T20:55:39.293Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T20:55:41.246Z tabtab:prompt Validating input /tmp/foo
2018-10-03T20:55:41.249Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"/tmp/foo"}
2018-10-03T20:55:41.251Z tabtab:installer Check filename (/tmp/foo) for "# tabtab source for completion packages"
2018-10-03T20:55:41.254Z tabtab:installer Check filename (/mnt/c/Users/<USER>/dev/tabtab/.completions/__tabtab.undefined) for "# tabtab source for foo package"
2018-10-03T20:55:41.258Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.undefined
2018-10-03T20:55:41.259Z tabtab:installer with undefined
2018-10-03T20:55:41.271Z tabtab:installer Tabtab line already exists in /tmp/foo file
2018-10-03T20:55:41.272Z tabtab:installer Tabtab line already exists in /mnt/c/Users/<USER>/dev/tabtab/.completions/__tabtab.undefined file
2018-10-03T20:55:41.923Z tabtab:prompt answers bash
2018-10-03T20:55:41.924Z tabtab:prompt Will install completion to ~/.bashrc
2018-10-03T20:55:42.399Z tabtab:prompt location is ok, return {"location":"~/.bashrc","shell":"bash"}
2018-10-03T20:55:42.400Z tabtab:installer Install with options {"name":"foo","completer":"foo-complete","location":"~/.bashrc"}
2018-10-03T20:55:42.400Z tabtab:installer Check filename (~/.bashrc) for "# tabtab source for completion packages"
2018-10-03T20:55:42.402Z tabtab:installer Check filename (/mnt/c/Users/<USER>/dev/tabtab/.completions/__tabtab.bash) for "# tabtab source for foo package"
2018-10-03T20:55:42.402Z tabtab:installer Writing completion script to /mnt/c/Users/<USER>/dev/tabtab/.completions/foo.bash
2018-10-03T20:55:42.402Z tabtab:installer with /mnt/c/Users/<USER>/dev/tabtab/scripts/bash.sh
2018-10-03T20:55:42.406Z tabtab:installer Tabtab line already exists in ~/.bashrc file
2018-10-03T20:55:42.406Z tabtab:installer Tabtab line already exists in /mnt/c/Users/<USER>/dev/tabtab/.completions/__tabtab.bash file
