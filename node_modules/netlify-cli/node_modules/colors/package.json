{"name": "colors", "description": "get colors in your node.js console", "version": "1.4.0", "author": "Marak Squires", "contributors": [{"name": "DABH", "url": "https://github.com/DABH"}], "homepage": "https://github.com/Marak/colors.js", "bugs": "https://github.com/Marak/colors.js/issues", "keywords": ["ansi", "terminal", "colors"], "repository": {"type": "git", "url": "http://github.com/Marak/colors.js.git"}, "license": "MIT", "scripts": {"lint": "eslint . --fix", "test": "node tests/basic-test.js && node tests/safe-test.js"}, "engines": {"node": ">=0.1.90"}, "main": "lib/index.js", "files": ["examples", "lib", "LICENSE", "safe.js", "themes", "index.d.ts", "safe.d.ts"], "devDependencies": {"eslint": "^5.2.0", "eslint-config-google": "^0.11.0"}}