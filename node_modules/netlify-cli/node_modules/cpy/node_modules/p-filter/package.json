{"name": "p-filter", "version": "3.0.0", "description": "Filter promises concurrently", "license": "MIT", "repository": "sindresorhus/p-filter", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "filter", "collection", "iterable", "iterator", "fulfilled", "async", "await", "promises", "concurrently", "concurrency", "parallel", "bluebird"], "dependencies": {"p-map": "^5.1.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}}