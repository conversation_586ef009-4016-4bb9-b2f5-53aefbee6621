{"name": "iserror", "version": "0.0.2", "description": "Test whether value is error object", "main": "index.js", "scripts": {"test": "tape test.js | tap-spec", "test-cov": "nyc npm test", "report-cov": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/yefremov/iserror.git"}, "keywords": ["test", "object", "error", "iserror", "browser", "node"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/yefremov/iserror/issues"}, "homepage": "https://github.com/yefremov/iserror#readme", "devDependencies": {"coveralls": "^2.12.0", "nyc": "^10.1.2", "tap-spec": "^4.1.1", "tape": "^4.6.3"}}