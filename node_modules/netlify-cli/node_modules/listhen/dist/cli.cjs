'use strict';

const citty = require('citty');
const pathe = require('pathe');
const index = require('./index.cjs');
require('node:http');
require('node:https');
require('node:util');
require('get-port-please');
require('http-shutdown');
require('consola');
require('defu');
require('consola/utils');
require('uqr');
require('node:child_process');
require('node:fs');
require('node:os');
require('node:path');
require('std-env');
require('node-forge');
require('node:fs/promises');
require('mlly');

const name = "listhen";
const version = "1.5.6";
const description = "👂 Elegant HTTP Listener";

const main = citty.defineCommand({
  meta: {
    name,
    description,
    version
  },
  args: {
    cwd: {
      type: "string",
      description: "Current working directory"
    },
    entry: {
      type: "positional",
      description: "Listener entry file (./app.ts)",
      required: true
    },
    name: {
      type: "string",
      description: "Name to use in the banner"
    },
    baseURL: {
      type: "string",
      description: "Base URL to use"
    },
    watch: {
      type: "boolean",
      description: "Watch for changes",
      alias: "w"
    },
    ws: {
      type: "boolean",
      description: "Enable Experimental WebSocket support"
    },
    ...getArgs()
  },
  async run({ args }) {
    const opts = {
      ...args,
      ...parseArgs(args),
      baseURL: args.baseURL,
      name: args.name
    };
    const entry = pathe.isAbsolute(args.entry) || args.entry.startsWith(".") ? args.entry : `./${args.entry}`;
    if (args.watch) {
      await index.listenAndWatch(entry, opts);
    } else {
      const devServer = await index.createDevServer(entry, opts);
      await index.listen(devServer.nodeListener, {
        ...opts,
        _entry: devServer._entry,
        ws: opts.ws ? devServer._ws : void 0
      });
      await devServer.reload(true);
    }
  }
});
const runMain = () => citty.runMain(main);
function getArgs() {
  return {
    port: {
      type: "string",
      description: "Port to listen on (use `PORT` environment variable to override)"
    },
    host: {
      description: "Host to listen on. If no value or an empty string provided, will listen on all available interfaces (use `HOST` environment variable to override)"
    },
    clipboard: {
      type: "boolean",
      description: "Copy the URL to the clipboard"
    },
    open: {
      type: "boolean",
      description: "Open the URL in the browser"
    },
    https: {
      type: "boolean",
      description: "Enable HTTPS"
    },
    "https.cert": {
      type: "string",
      description: "Path to TLS certificate used with HTTPS in PEM format"
    },
    "https.key": {
      type: "string",
      description: "Path to TLS key used with HTTPS in PEM format"
    },
    "https.pfx": {
      type: "string",
      description: "Path to PKCS#12 (.p12/.pfx) keystore containing a TLS certificate and Key"
    },
    "https.passphrase": {
      type: "string",
      description: "Passphrase used for TLS key or keystore"
    },
    "https.validityDays": {
      type: "string",
      description: "Validity in days of the autogenerated TLS certificate (https: true)"
    },
    "https.domains": {
      type: "string",
      description: "Comma seperated list of domains and IPs, the autogenerated certificate should be valid for (https: true)"
    },
    publicURL: {
      type: "string",
      description: "Displayed public URL (used for QR code)",
      required: false
    },
    qr: {
      type: "boolean",
      description: "Display The QR code of public URL when available",
      required: false
    },
    public: {
      type: "boolean",
      description: "Listen to all network interfaces",
      required: false
    },
    tunnel: {
      type: "boolean",
      description: "Open a tunnel using https://github.com/unjs/untun",
      required: false
    }
  };
}
function parseArgs(args) {
  return {
    port: args.port,
    // prettier-ignore
    hostname: typeof args.host === "string" ? args.host : args.host === true ? "" : void 0,
    clipboard: args.clipboard,
    open: args.open,
    qr: args.qr,
    publicURL: args.publicURL,
    public: args.public,
    tunnel: args.tunnel,
    https: args.https ? {
      cert: args["https.cert"],
      key: args["https.key"],
      pfx: args["https.pfx"],
      passphrase: args["https.passphrase"],
      validityDays: args["https.validityDays"] ? +args["https.validityDays"] : void 0,
      domains: args["https.domains"] ? args["https.domains"].split(",") : void 0
    } : false
  };
}

exports.getArgs = getArgs;
exports.main = main;
exports.parseArgs = parseArgs;
exports.runMain = runMain;
