{"name": "color", "version": "3.2.1", "description": "Color conversion and manipulation with CSS string support", "keywords": ["color", "colour", "css"], "authors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON>"], "license": "MIT", "repository": "Qix-/color", "xo": {"rules": {"no-cond-assign": 0, "new-cap": 0}}, "files": ["CHANGELOG.md", "LICENSE", "index.js"], "scripts": {"pretest": "xo", "test": "mocha"}, "dependencies": {"color-convert": "^1.9.3", "color-string": "^1.6.0"}, "devDependencies": {"mocha": "9.0.2", "xo": "0.12.1"}}