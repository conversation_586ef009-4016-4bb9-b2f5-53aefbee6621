import { Analog } from './analog.js';
import { Angular } from './angular.js';
import { Assemble } from './assemble.js';
import { Astro } from './astro.js';
import { Blitz } from './blitz.js';
import { Brunch } from './brunch.js';
import { <PERSON> } from './cecil.js';
import { DocPad } from './docpad.js';
import { Docusaurus } from './docusaurus.js';
import { Eleventy } from './eleventy.js';
import { Ember } from './ember.js';
import { Expo } from './expo.js';
import { Gatsby } from './gatsby.js';
import { Gridsome } from './gridsome.js';
import { Grunt } from './grunt.js';
import { Gulp } from './gulp.js';
import { Harp } from './harp.js';
import { Hexo } from './hexo.js';
import { <PERSON> } from './hugo.js';
import { Hydrogen } from './hydrogen.js';
import { Jekyll } from './jekyll.js';
import { Metalsmith } from './metalsmith.js';
import { Middleman } from './middleman.js';
import { Next } from './next.js';
import { Nuxt } from './nuxt.js';
import { Observable } from './observable.js';
import { Parcel } from './parcel.js';
import { Phenomic } from './phenomic.js';
import { Quasar } from './quasar.js';
import { Qwik } from './qwik.js';
import { ReactRouter } from './react-router.js';
import { ReactStatic } from './react-static.js';
import { CreateReactApp } from './react.js';
import { RedwoodJS } from './redwoodjs.js';
import { Remix } from './remix.js';
import { Roots } from './roots.js';
import { Sapper } from './sapper.js';
import { SolidJs } from './solid-js.js';
import { SolidStart } from './solid-start.js';
import { Stencil } from './stencil.js';
import { SvelteKit } from './svelte-kit.js';
import { Svelte } from './svelte.js';
import { TanStackRouter } from './tanstack-router.js';
import { TanStackStart } from './tanstack-start.js';
import { Vite } from './vite.js';
import { Vue } from './vue.js';
import { VuePress } from './vuepress.js';
import { Wintersmith } from './wintersmith.js';
import { WMR } from './wmr.js';
import { Zola } from './zola.js';
export declare const frameworks: (typeof Analog | typeof Angular | typeof Assemble | typeof Astro | typeof Blitz | typeof Brunch | typeof Cecil | typeof DocPad | typeof Docusaurus | typeof Eleventy | typeof Ember | typeof Expo | typeof Gatsby | typeof Gridsome | typeof Grunt | typeof Gulp | typeof Harp | typeof Hexo | typeof Hugo | typeof Hydrogen | typeof Jekyll | typeof Metalsmith | typeof Middleman | typeof Next | typeof Nuxt | typeof Observable | typeof Parcel | typeof Phenomic | typeof Quasar | typeof Qwik | typeof ReactRouter | typeof ReactStatic | typeof CreateReactApp | typeof RedwoodJS | typeof Remix | typeof Roots | typeof Sapper | typeof SolidJs | typeof SolidStart | typeof Stencil | typeof SvelteKit | typeof Svelte | typeof TanStackRouter | typeof TanStackStart | typeof Vite | typeof Vue | typeof VuePress | typeof Wintersmith | typeof WMR | typeof Zola)[];
type Frameworks = typeof frameworks;
export type FrameworkName = InstanceType<Frameworks[number]>['id'];
export type { FrameworkInfo, PollingStrategy } from './framework.js';
