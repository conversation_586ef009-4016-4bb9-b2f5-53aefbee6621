import type { Package<PERSON>son } from 'read-pkg';
import { SemVer } from 'semver';
import { Project } from '../project.js';
export declare enum Category {
    FrontendFramework = "frontend_framework",
    SSG = "static_site_generator",
    BuildTool = "build_tool"
}
export declare enum Accuracy {
    Forced = 5,// Forced framework, this means that we don't detect the framework instead it get's set either through the user inside the toml or through the build system like nx-integrated
    NPM = 4,// Matched the npm dependency (highest accuracy on detecting it)
    ConfigOnly = 3,// Only a config file was specified and matched
    Config = 2,// Has npm dependencies specified as well but there it did not match (least resort)
    NPMHoisted = 1
}
export type PollingStrategy = {
    name: any;
};
/** Information on how it was detected and how accurate the detection is */
export type Detection = {
    /**
     * The grade of how much we trust in having the right result detected.
     * Sometimes it's hard to predict it to 100% as for config files, some frameworks can share the same config file
     */
    accuracy: Accuracy;
    /** The NPM package that was able to detect it (high accuracy) */
    package?: {
        name: string;
        version?: SemVer;
    };
    packageJSON?: PackageJson;
    /** The absolute path to config file that is associated with the framework */
    config?: string;
    /** The name of config file that is associated with the framework */
    configName?: string;
};
export type FrameworkInfo = ReturnType<Framework['toJSON']>;
export interface Framework {
    project: Project;
    id: string;
    name: string;
    category: Category;
    /**
     * If this is set, at least ONE of these must exist, anywhere in the project
     */
    configFiles: string[];
    /**
     * If this is set, at least ONE of these must be present in the `package.json` `dependencies|devDependencies`
     */
    npmDependencies: string[];
    /**
     * if this is set, NONE of these must be present in the `package.json` `dependencies|devDependencies`
     */
    excludedNpmDependencies?: string[];
    version?: SemVer;
    /** Information on how it was detected and how accurate the detection is */
    detected?: Detection;
    staticAssetsDirectory?: string;
    dev?: {
        command: string;
        port?: number;
        pollingStrategies?: PollingStrategy[];
        clearPublishDirectory?: boolean;
    };
    build: {
        command: string;
        directory: string;
    };
    logo?: {
        default: string;
        light?: string;
        dark?: string;
    };
    plugins: string[];
    env: Record<string, string>;
    detect(): Promise<DetectedFramework | undefined>;
    getDevCommands(): string[];
    getBuildCommands(): string[];
    toJSON(): {
        id: string;
        name: string;
        category: Category;
        package: {
            name?: string;
            version: string | 'unknown';
        };
        dev: {
            commands: string[];
            port?: number;
            pollingStrategies?: PollingStrategy[];
        };
        build: {
            commands: string[];
            directory: string;
        };
        staticAssetsDirectory?: string;
        env: Record<string, string>;
        logo?: Record<string, string>;
        plugins: string[];
    };
}
export type DetectedFramework = Omit<Framework, 'detected'> & {
    detected: Detection;
};
/** Filters a list of detected frameworks by relevance, meaning we drop build tools if we find static site generators */
export declare function filterByRelevance(detected: DetectedFramework[]): DetectedFramework[];
/**
 * sort a list of frameworks based on the accuracy and on it's type (prefer static site generators over build tools)
 * from most accurate to least accurate
 * 1. a npm dependency was specified and matched
 * 2. only a config file was specified and matched
 * 3. an npm dependency was specified but matched over the config file (least accurate)
 */
export declare function sortFrameworksBasedOnAccuracy(a: DetectedFramework, b: DetectedFramework): number;
/** Merges a list of detection results based on accuracy to get the one with the highest accuracy that still contains information provided by all other detections */
export declare function mergeDetections(detections: Array<Detection | undefined>): Detection | undefined;
export declare abstract class BaseFramework implements Framework {
    /** The current project inside we want to detect the framework */
    project: Project;
    /** An absolute path considered as the baseDirectory for detection, prefer that over the project baseDirectory */
    path?: string | undefined;
    id: string;
    name: string;
    category: Category;
    detected?: Detection;
    version?: SemVer;
    configFiles: string[];
    npmDependencies: string[];
    excludedNpmDependencies: string[];
    plugins: string[];
    staticAssetsDirectory?: string;
    env: {};
    dev?: {
        command: string;
        port?: number;
        pollingStrategies?: PollingStrategy[];
    };
    build: {
        command: string;
        directory: string;
    };
    logo?: {
        default: string;
        light?: string;
        dark?: string;
    };
    constructor(
    /** The current project inside we want to detect the framework */
    project: Project, 
    /** An absolute path considered as the baseDirectory for detection, prefer that over the project baseDirectory */
    path?: string | undefined);
    setDetected(accuracy: Accuracy, reason?: {
        name: string;
        version?: SemVer;
    } | string): DetectedFramework;
    /** Retrieves the version of a npm package from the node_modules */
    private getVersionFromNodeModules;
    /** check if the npmDependencies are used inside the provided package.json */
    private npmDependenciesUsed;
    /** detect if the framework occurs inside the package.json dependencies */
    private detectNpmDependency;
    /** detect if the framework config file is located somewhere up the tree */
    protected detectConfigFile(configFiles: string[]): Promise<Detection | undefined>;
    /**
     * Checks if the project is using a specific framework:
     * - if `npmDependencies` is set, one of them must be present in the `package.json` `dependencies|devDependencies`
     * - if `excludedNpmDependencies` is set, none of them must be present in the `package.json` `dependencies|devDependencies`
     * - if `configFiles` is set, one of the files must exist
     */
    detect(): Promise<DetectedFramework | undefined>;
    /**
     * Retrieve framework's dev commands.
     * We use, in priority order:
     *   - `package.json` `scripts` containing the frameworks dev command
     *   - `package.json` `scripts` whose names are among a list of common dev scripts like: `dev`, `serve`, `develop`, ...
     *   -  The frameworks dev command
     */
    getDevCommands(): string[];
    getBuildCommands(): string[];
    /** This method will be called by the JSON.stringify */
    toJSON(): {
        id: string;
        name: string;
        package: {
            name: string;
            version: string;
        };
        category: Category;
        dev: {
            commands: string[];
            port: number | undefined;
            pollingStrategies: PollingStrategy[] | undefined;
        };
        build: {
            commands: string[];
            directory: string;
        };
        staticAssetsDirectory: string | undefined;
        env: {};
        logo: {} | undefined;
        plugins: string[];
    };
}
