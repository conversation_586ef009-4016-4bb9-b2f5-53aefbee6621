import { <PERSON><PERSON> } from './bazel.js';
import { <PERSON> } from './buck.js';
import { <PERSON><PERSON><PERSON> } from './gradle.js';
import { Lage } from './lage.js';
import { Lerna } from './lerna.js';
import { <PERSON> } from './moon.js';
import { <PERSON> } from './nix.js';
import { Nx } from './nx.js';
import { NPM, PNPM, Yarn } from './package-managers.js';
import { Pants } from './pants.js';
import { Rush } from './rush.js';
import { Turbo } from './turbo.js';
export declare const buildSystems: readonly [typeof Bazel, typeof Buck, typeof Gradle, typeof Lage, typeof Lerna, typeof Moon, typeof Nix, typeof Nx, typeof Pants, typeof Rush, typeof Turbo, typeof PNPM, typeof NPM, typeof Yarn];
