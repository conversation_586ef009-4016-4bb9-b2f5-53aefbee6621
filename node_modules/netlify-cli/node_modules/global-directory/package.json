{"name": "global-directory", "version": "4.0.1", "description": "Get the directory of globally installed packages and binaries", "license": "MIT", "repository": "sindresorhus/global-directory", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["global", "prefix", "path", "paths", "npm", "yarn", "node", "modules", "node-modules", "package", "packages", "binary", "binaries", "bin", "directory", "directories", "npmrc", "rc", "config", "root", "resolve"], "dependencies": {"ini": "4.1.1"}, "devDependencies": {"ava": "^5.3.1", "execa": "^8.0.1", "tsd": "^0.29.0", "xo": "^0.56.0"}}