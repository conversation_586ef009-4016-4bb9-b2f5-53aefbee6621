{"name": "maxstache", "version": "1.0.7", "description": "Minimalist mustache template replacement", "main": "index.js", "scripts": {"deps": "dependency-check . && dependency-check . --extra --no-dev", "deps:pkg": "ncu", "deps:update": "ncu -a", "test": "standard && npm run deps && NODE_ENV=test node test", "test:cov": "standard && npm run deps && NODE_ENV=test istanbul cover test.js"}, "repository": "yoshuawuyts/maxstache", "keywords": ["mustache", "template", "minstache"], "license": "MIT", "dependencies": {}, "devDependencies": {"dependency-check": "^2.5.1", "istanbul": "^0.3.22", "standard": "^5.3.1", "tape": "^4.2.1"}, "files": ["index.js", "bin/*"]}