{"name": "express-logging", "description": "Express middleware to log each request and response", "version": "1.1.1", "license": "Apache-2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "**************:telefonica/node-express-logging.git"}, "main": "lib/logging", "engines": {"node": ">= 0.10.26"}, "scripts": {"test": "mocha -R spec test/environment.js test/unit/*-test.js", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha -- -R dot test/environment.js test/unit/*-test.js", "lint": "jscs lib && eslint lib", "prepublish": "npm run test && npm run lint", "travis": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- -R spec test/environment.js test/*-test.js && cat ./coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js && rm -rf ./coverage"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.2", "istanbul": "^0.3.16", "mocha": "^2.2.5", "proxyquire": "^1.5.0", "should": "^7.0.1", "sinon": "~1.15.3", "sinon-chai": "^2.8.0", "supertest": "^1.0.1", "xunit-file": "^0.0.4", "jscs": "^1.13.1", "eslint": "^0.23.0"}, "dependencies": {"on-headers": "^1.0.0"}, "keywords": ["logging", "express", "middleware"]}