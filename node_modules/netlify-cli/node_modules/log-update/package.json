{"name": "log-update", "version": "6.1.0", "description": "Log by overwriting the previous output in the terminal. Useful for rendering progress bars, animations, etc.", "license": "MIT", "repository": "sindresorhus/log-update", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["log", "logger", "logging", "cli", "terminal", "term", "console", "shell", "update", "refresh", "overwrite", "output", "stdout", "progress", "bar", "animation"], "dependencies": {"ansi-escapes": "^7.0.0", "cli-cursor": "^5.0.0", "slice-ansi": "^7.1.0", "strip-ansi": "^7.1.0", "wrap-ansi": "^9.0.0"}, "devDependencies": {"@types/node": "^20.14.12", "ava": "^6.1.3", "terminal.js": "^1.0.11", "tsd": "^0.31.1", "wcwidth": "^1.0.1", "xo": "^0.59.2"}, "xo": {"rules": {"@typescript-eslint/no-unsafe-argument": "off"}}}