# JSON-Schema-Test-Suite

You can find all test cases [here](https://github.com/json-schema-org/JSON-Schema-Test-Suite).
It contains a set of JSON objects that implementors of JSON Schema validation libraries can use to test their validators.

# How to add another test case?

1. Navigate to [JSON-Schema-Test-Suite](https://github.com/json-schema-org/JSON-Schema-Test-Suite/tree/master/tests)
2. Choose a draft `draft4`, `draft6` or `draft7`
3. Copy & paste the `test-case.json` to the project and add a test like in the `draft4.test.js`