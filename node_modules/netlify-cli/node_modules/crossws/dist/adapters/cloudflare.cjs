'use strict';

const peer = require('../shared/crossws.21e14e0d.cjs');

const cloudflare = peer.defineWebSocketAdapter(
  (hooks, opts = {}) => {
    const handleUpgrade = (request, env, context) => {
      const pair = new WebSocketPair();
      const client = pair[0];
      const server = pair[1];
      const peer$1 = new CloudflareWebSocketPeer({
        cloudflare: { client, server, request, env, context }
      });
      server.accept();
      hooks["cloudflare:accept"]?.(peer$1);
      hooks.open?.(peer$1);
      server.addEventListener("message", (event) => {
        hooks["cloudflare:message"]?.(peer$1, event);
        hooks.message?.(peer$1, new peer.WebSocketMessage(event.data));
      });
      server.addEventListener("error", (event) => {
        hooks["cloudflare:error"]?.(peer$1, event);
        hooks.error?.(peer$1, new peer.WebSocketError(event.error));
      });
      server.addEventListener("close", (event) => {
        hooks["cloudflare:close"]?.(peer$1, event);
        hooks.close?.(peer$1, { code: event.code, reason: event.reason });
      });
      return new Response(null, {
        status: 101,
        webSocket: client
      });
    };
    return {
      handleUpgrade
    };
  }
);
class CloudflareWebSocketPeer extends peer.WebSocketPeerBase {
  get id() {
    return void 0;
  }
  get readyState() {
    return this.ctx.cloudflare.client.readyState;
  }
  send(message) {
    this.ctx.cloudflare.server.send(message);
    return 0;
  }
}

module.exports = cloudflare;
