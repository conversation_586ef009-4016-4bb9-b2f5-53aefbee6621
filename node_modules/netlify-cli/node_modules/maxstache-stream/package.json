{"name": "maxstache-stream", "version": "1.0.4", "description": "Maxstache transform stream", "main": "index.js", "scripts": {"deps": "dependency-check . && dependency-check . --extra --no-dev", "test": "standard && npm run deps && NODE_ENV=test node test", "test:cov": "standard && npm run deps && NODE_ENV=test istanbul cover test.js"}, "repository": "yoshuawuyts/maxstache-stream", "keywords": ["maxstache", "variable", "stream", "inject", "template"], "license": "MIT", "dependencies": {"maxstache": "^1.0.0", "pump": "^1.0.0", "split2": "^1.0.0", "through2": "^2.0.0"}, "devDependencies": {"concat-stream": "^1.5.0", "dependency-check": "^2.5.1", "from2-string": "^1.1.0", "istanbul": "^0.3.21", "standard": "^5.0.0", "tape": "^4.2.0"}, "files": ["index.js", "bin/*"]}