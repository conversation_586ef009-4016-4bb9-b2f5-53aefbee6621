{"version": 3, "file": "polyfill.es6.min.js", "sources": ["../src/stub/symbol.ts", "../src/utils.ts", "../src/lib/helpers/miscellaneous.ts", "../src/lib/helpers/webidl.ts", "../src/lib/simple-queue.ts", "../src/lib/readable-stream/generic-reader.ts", "../src/lib/abstract-ops/internal-methods.ts", "../src/stub/number-isfinite.ts", "../src/stub/math-trunc.ts", "../src/lib/validators/basic.ts", "../src/lib/validators/readable-stream.ts", "../src/lib/readable-stream/default-reader.ts", "../src/target/es5/stub/async-iterator-prototype.ts", "../src/lib/readable-stream/async-iterator.ts", "../src/stub/number-isnan.ts", "../src/lib/abstract-ops/ecmascript.ts", "../src/lib/abstract-ops/miscellaneous.ts", "../src/lib/abstract-ops/queue-with-sizes.ts", "../src/lib/readable-stream/byte-stream-controller.ts", "../src/lib/readable-stream/byob-reader.ts", "../src/lib/abstract-ops/queuing-strategy.ts", "../src/lib/validators/queuing-strategy.ts", "../src/lib/validators/underlying-sink.ts", "../src/lib/validators/writable-stream.ts", "../src/lib/abort-signal.ts", "../src/lib/writable-stream.ts", "../src/stub/native.ts", "../src/stub/dom-exception.ts", "../src/lib/readable-stream/pipe.ts", "../src/lib/readable-stream/default-controller.ts", "../src/lib/readable-stream/tee.ts", "../src/lib/validators/underlying-source.ts", "../src/lib/validators/reader-options.ts", "../src/lib/validators/pipe-options.ts", "../src/lib/readable-stream.ts", "../src/lib/validators/readable-writable-pair.ts", "../src/lib/validators/iterator-options.ts", "../src/lib/validators/queuing-strategy-init.ts", "../src/lib/byte-length-queuing-strategy.ts", "../src/lib/count-queuing-strategy.ts", "../src/lib/validators/transformer.ts", "../src/lib/transform-stream.ts", "../src/polyfill.ts"], "sourcesContent": ["/// <reference lib=\"es2015.symbol\" />\n\nconst SymbolPolyfill: (description?: string) => symbol =\n  typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol' ?\n    Symbol :\n    description => `Symbol(${description})` as any as symbol;\n\nexport default SymbolPolyfill;\n", "/// <reference lib=\"dom\" />\n\nexport function noop(): undefined {\n  return undefined;\n}\n\nfunction getGlobals() {\n  if (typeof self !== 'undefined') {\n    return self;\n  } else if (typeof window !== 'undefined') {\n    return window;\n  } else if (typeof global !== 'undefined') {\n    return global;\n  }\n  return undefined;\n}\n\nexport const globals = getGlobals();\n", "import { noop } from '../../utils';\nimport { AssertionError } from '../../stub/assert';\n\nexport function typeIsObject(x: any): x is object {\n  return (typeof x === 'object' && x !== null) || typeof x === 'function';\n}\n\nexport const rethrowAssertionErrorRejection: (e: any) => void =\n  DEBUG ? e => {\n    // Used throughout the reference implementation, as `.catch(rethrowAssertionErrorRejection)`, to ensure any errors\n    // get shown. There are places in the spec where we do promise transformations and purposefully ignore or don't\n    // expect any errors, but assertion errors are always problematic.\n    if (e && e instanceof AssertionError) {\n      setTimeout(() => {\n        throw e;\n      }, 0);\n    }\n  } : noop;\n", "import { globals } from '../../utils';\nimport { rethrowAssertionErrorRejection } from './miscellaneous';\nimport assert from '../../stub/assert';\n\nconst originalPromise = Promise;\nconst originalPromiseThen = Promise.prototype.then;\nconst originalPromiseResolve = Promise.resolve.bind(originalPromise);\nconst originalPromiseReject = Promise.reject.bind(originalPromise);\n\nexport function newPromise<T>(executor: (\n  resolve: (value: T | PromiseLike<T>) => void,\n  reject: (reason?: any) => void\n) => void): Promise<T> {\n  return new originalPromise(executor);\n}\n\nexport function promiseResolvedWith<T>(value: T | PromiseLike<T>): Promise<T> {\n  return originalPromiseResolve(value);\n}\n\nexport function promiseRejectedWith<T = never>(reason: any): Promise<T> {\n  return originalPromiseReject(reason);\n}\n\nexport function PerformPromiseThen<T, TResult1 = T, TResult2 = never>(\n  promise: Promise<T>,\n  onFulfilled?: (value: T) => TResult1 | PromiseLike<TResult1>,\n  onRejected?: (reason: any) => TResult2 | PromiseLike<TResult2>): Promise<TResult1 | TResult2> {\n  // There doesn't appear to be any way to correctly emulate the behaviour from JavaScript, so this is just an\n  // approximation.\n  return originalPromiseThen.call(promise, onFulfilled, onRejected) as Promise<TResult1 | TResult2>;\n}\n\nexport function uponPromise<T>(\n  promise: Promise<T>,\n  onFulfilled?: (value: T) => void | PromiseLike<void>,\n  onRejected?: (reason: any) => void | PromiseLike<void>): void {\n  PerformPromiseThen(\n    PerformPromiseThen(promise, onFulfilled, onRejected),\n    undefined,\n    rethrowAssertionErrorRejection\n  );\n}\n\nexport function uponFulfillment<T>(promise: Promise<T>, onFulfilled: (value: T) => void | PromiseLike<void>): void {\n  uponPromise(promise, onFulfilled);\n}\n\nexport function uponRejection(promise: Promise<unknown>, onRejected: (reason: any) => void | PromiseLike<void>): void {\n  uponPromise(promise, undefined, onRejected);\n}\n\nexport function transformPromiseWith<T, TResult1 = T, TResult2 = never>(\n  promise: Promise<T>,\n  fulfillmentHandler?: (value: T) => TResult1 | PromiseLike<TResult1>,\n  rejectionHandler?: (reason: any) => TResult2 | PromiseLike<TResult2>): Promise<TResult1 | TResult2> {\n  return PerformPromiseThen(promise, fulfillmentHandler, rejectionHandler);\n}\n\nexport function setPromiseIsHandledToTrue(promise: Promise<unknown>): void {\n  PerformPromiseThen(promise, undefined, rethrowAssertionErrorRejection);\n}\n\nexport const queueMicrotask: (fn: () => void) => void = (() => {\n  const globalQueueMicrotask = globals && globals.queueMicrotask;\n  if (typeof globalQueueMicrotask === 'function') {\n    return globalQueueMicrotask;\n  }\n\n  const resolvedPromise = promiseResolvedWith(undefined);\n  return (fn: () => void) => PerformPromiseThen(resolvedPromise, fn);\n})();\n\nexport function reflectCall<T, A extends any[], R>(F: (this: T, ...fnArgs: A) => R, V: T, args: A): R {\n  if (typeof F !== 'function') {\n    throw new TypeError('Argument is not a function');\n  }\n  return Function.prototype.apply.call(F, V, args);\n}\n\nexport function promiseCall<T, A extends any[], R>(F: (this: T, ...fnArgs: A) => R | PromiseLike<R>,\n                                                   V: T,\n                                                   args: A): Promise<R> {\n  assert(typeof F === 'function');\n  assert(V !== undefined);\n  assert(Array.isArray(args));\n  try {\n    return promiseResolvedWith(reflectCall(F, V, args));\n  } catch (value) {\n    return promiseRejectedWith(value);\n  }\n}\n", "import assert from '../stub/assert';\n\n// Original from Chromium\n// https://chromium.googlesource.com/chromium/src/+/0aee4434a4dba42a42abaea9bfbc0cd196a63bc1/third_party/blink/renderer/core/streams/SimpleQueue.js\n\nconst QUEUE_MAX_ARRAY_SIZE = 16384;\n\ninterface Node<T> {\n  _elements: T[];\n  _next: Node<T> | undefined;\n}\n\n/**\n * Simple queue structure.\n *\n * Avoids scalability issues with using a packed array directly by using\n * multiple arrays in a linked list and keeping the array size bounded.\n */\nexport class SimpleQueue<T> {\n  private _front: Node<T>;\n  private _back: Node<T>;\n  private _cursor = 0;\n  private _size = 0;\n\n  constructor() {\n    // _front and _back are always defined.\n    this._front = {\n      _elements: [],\n      _next: undefined\n    };\n    this._back = this._front;\n    // The cursor is used to avoid calling Array.shift().\n    // It contains the index of the front element of the array inside the\n    // front-most node. It is always in the range [0, QUEUE_MAX_ARRAY_SIZE).\n    this._cursor = 0;\n    // When there is only one node, size === elements.length - cursor.\n    this._size = 0;\n  }\n\n  get length(): number {\n    return this._size;\n  }\n\n  // For exception safety, this method is structured in order:\n  // 1. Read state\n  // 2. Calculate required state mutations\n  // 3. Perform state mutations\n  push(element: T): void {\n    const oldBack = this._back;\n    let newBack = oldBack;\n    assert(oldBack._next === undefined);\n    if (oldBack._elements.length === QUEUE_MAX_ARRAY_SIZE - 1) {\n      newBack = {\n        _elements: [],\n        _next: undefined\n      };\n    }\n\n    // push() is the mutation most likely to throw an exception, so it\n    // goes first.\n    oldBack._elements.push(element);\n    if (newBack !== oldBack) {\n      this._back = newBack;\n      oldBack._next = newBack;\n    }\n    ++this._size;\n  }\n\n  // Like push(), shift() follows the read -> calculate -> mutate pattern for\n  // exception safety.\n  shift(): T {\n    assert(this._size > 0); // must not be called on an empty queue\n\n    const oldFront = this._front;\n    let newFront = oldFront;\n    const oldCursor = this._cursor;\n    let newCursor = oldCursor + 1;\n\n    const elements = oldFront._elements;\n    const element = elements[oldCursor];\n\n    if (newCursor === QUEUE_MAX_ARRAY_SIZE) {\n      assert(elements.length === QUEUE_MAX_ARRAY_SIZE);\n      assert(oldFront._next !== undefined);\n      newFront = oldFront._next!;\n      newCursor = 0;\n    }\n\n    // No mutations before this point.\n    --this._size;\n    this._cursor = newCursor;\n    if (oldFront !== newFront) {\n      this._front = newFront;\n    }\n\n    // Permit shifted element to be garbage collected.\n    elements[oldCursor] = undefined!;\n\n    return element;\n  }\n\n  // The tricky thing about forEach() is that it can be called\n  // re-entrantly. The queue may be mutated inside the callback. It is easy to\n  // see that push() within the callback has no negative effects since the end\n  // of the queue is checked for on every iteration. If shift() is called\n  // repeatedly within the callback then the next iteration may return an\n  // element that has been removed. In this case the callback will be called\n  // with undefined values until we either \"catch up\" with elements that still\n  // exist or reach the back of the queue.\n  forEach(callback: (element: T) => void): void {\n    let i = this._cursor;\n    let node = this._front;\n    let elements = node._elements;\n    while (i !== elements.length || node._next !== undefined) {\n      if (i === elements.length) {\n        assert(node._next !== undefined);\n        assert(i === QUEUE_MAX_ARRAY_SIZE);\n        node = node._next!;\n        elements = node._elements;\n        i = 0;\n        if (elements.length === 0) {\n          break;\n        }\n      }\n      callback(elements[i]);\n      ++i;\n    }\n  }\n\n  // Return the element that would be returned if shift() was called now,\n  // without modifying the queue.\n  peek(): T {\n    assert(this._size > 0); // must not be called on an empty queue\n\n    const front = this._front;\n    const cursor = this._cursor;\n    return front._elements[cursor];\n  }\n}\n", "import assert from '../../stub/assert';\nimport { ReadableStream, ReadableStreamCancel, ReadableStreamReader } from '../readable-stream';\nimport { newPromise, setPromiseIsHandledToTrue } from '../helpers/webidl';\n\nexport function ReadableStreamReaderGenericInitialize<R>(reader: ReadableStreamReader<R>, stream: ReadableStream<R>) {\n  reader._ownerReadableStream = stream;\n  stream._reader = reader;\n\n  if (stream._state === 'readable') {\n    defaultReaderClosedPromiseInitialize(reader);\n  } else if (stream._state === 'closed') {\n    defaultReaderClosedPromiseInitializeAsResolved(reader);\n  } else {\n    assert(stream._state === 'errored');\n\n    defaultReaderClosedPromiseInitializeAsRejected(reader, stream._storedError);\n  }\n}\n\n// A client of ReadableStreamDefaultReader and ReadableStreamBYOBReader may use these functions directly to bypass state\n// check.\n\nexport function ReadableStreamReaderGenericCancel(reader: ReadableStreamReader<any>, reason: any): Promise<undefined> {\n  const stream = reader._ownerReadableStream;\n  assert(stream !== undefined);\n  return ReadableStreamCancel(stream, reason);\n}\n\nexport function ReadableStreamReaderGenericRelease(reader: ReadableStreamReader<any>) {\n  assert(reader._ownerReadableStream !== undefined);\n  assert(reader._ownerReadableStream._reader === reader);\n\n  if (reader._ownerReadableStream._state === 'readable') {\n    defaultReaderClosedPromiseReject(\n      reader,\n      new TypeError(`Reader was released and can no longer be used to monitor the stream's closedness`));\n  } else {\n    defaultReaderClosedPromiseResetToRejected(\n      reader,\n      new TypeError(`Reader was released and can no longer be used to monitor the stream's closedness`));\n  }\n\n  reader._ownerReadableStream._reader = undefined;\n  reader._ownerReadableStream = undefined!;\n}\n\n// Helper functions for the readers.\n\nexport function readerLockException(name: string): TypeError {\n  return new TypeError('Cannot ' + name + ' a stream using a released reader');\n}\n\n// Helper functions for the ReadableStreamDefaultReader.\n\nexport function defaultReaderClosedPromiseInitialize(reader: ReadableStreamReader<any>) {\n  reader._closedPromise = newPromise((resolve, reject) => {\n    reader._closedPromise_resolve = resolve;\n    reader._closedPromise_reject = reject;\n  });\n}\n\nexport function defaultReaderClosedPromiseInitializeAsRejected(reader: ReadableStreamReader<any>, reason: any) {\n  defaultReaderClosedPromiseInitialize(reader);\n  defaultReaderClosedPromiseReject(reader, reason);\n}\n\nexport function defaultReaderClosedPromiseInitializeAsResolved(reader: ReadableStreamReader<any>) {\n  defaultReaderClosedPromiseInitialize(reader);\n  defaultReaderClosedPromiseResolve(reader);\n}\n\nexport function defaultReaderClosedPromiseReject(reader: ReadableStreamReader<any>, reason: any) {\n  if (reader._closedPromise_reject === undefined) {\n    return;\n  }\n\n  setPromiseIsHandledToTrue(reader._closedPromise);\n  reader._closedPromise_reject(reason);\n  reader._closedPromise_resolve = undefined;\n  reader._closedPromise_reject = undefined;\n}\n\nexport function defaultReaderClosedPromiseResetToRejected(reader: ReadableStreamReader<any>, reason: any) {\n  assert(reader._closedPromise_resolve === undefined);\n  assert(reader._closedPromise_reject === undefined);\n\n  defaultReaderClosedPromiseInitializeAsRejected(reader, reason);\n}\n\nexport function defaultReaderClosedPromiseResolve(reader: ReadableStreamReader<any>) {\n  if (reader._closedPromise_resolve === undefined) {\n    return;\n  }\n\n  reader._closedPromise_resolve(undefined);\n  reader._closedPromise_resolve = undefined;\n  reader._closedPromise_reject = undefined;\n}\n", "export const AbortSteps = Symbol('[[AbortSteps]]');\nexport const ErrorSteps = Symbol('[[ErrorSteps]]');\nexport const CancelSteps = Symbol('[[CancelSteps]]');\nexport const PullSteps = Symbol('[[PullSteps]]');\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isFinite#Polyfill\nconst NumberIsFinite: typeof Number.isFinite = Number.isFinite || function (x) {\n  return typeof x === 'number' && isFinite(x);\n};\n\nexport default NumberIsFinite;\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/trunc#Polyfill\nconst MathTrunc: typeof Math.trunc = Math.trunc || function (v) {\n  return v < 0 ? Math.ceil(v) : Math.floor(v);\n};\n\nexport default MathTrunc;\n", "import NumberIsFinite from '../../stub/number-isfinite';\nimport MathTrunc from '../../stub/math-trunc';\n\n// https://heycam.github.io/webidl/#idl-dictionaries\nexport function isDictionary(x: any): x is object | null {\n  return typeof x === 'object' || typeof x === 'function';\n}\n\nexport function assertDictionary(obj: unknown,\n                                 context: string): asserts obj is object | null | undefined {\n  if (obj !== undefined && !isDictionary(obj)) {\n    throw new TypeError(`${context} is not an object.`);\n  }\n}\n\nexport type AnyFunction = (...args: any[]) => any;\n\n// https://heycam.github.io/webidl/#idl-callback-functions\nexport function assertFunction(x: unknown, context: string): asserts x is AnyFunction {\n  if (typeof x !== 'function') {\n    throw new TypeError(`${context} is not a function.`);\n  }\n}\n\n// https://heycam.github.io/webidl/#idl-object\nexport function isObject(x: any): x is object {\n  return (typeof x === 'object' && x !== null) || typeof x === 'function';\n}\n\nexport function assertObject(x: unknown,\n                             context: string): asserts x is object {\n  if (!isObject(x)) {\n    throw new TypeError(`${context} is not an object.`);\n  }\n}\n\nexport function assertRequiredArgument<T extends any>(x: T | undefined,\n                                                      position: number,\n                                                      context: string): asserts x is T {\n  if (x === undefined) {\n    throw new TypeError(`Parameter ${position} is required in '${context}'.`);\n  }\n}\n\nexport function assertRequiredField<T extends any>(x: T | undefined,\n                                                   field: string,\n                                                   context: string): asserts x is T {\n  if (x === undefined) {\n    throw new TypeError(`${field} is required in '${context}'.`);\n  }\n}\n\n// https://heycam.github.io/webidl/#idl-unrestricted-double\nexport function convertUnrestrictedDouble(value: unknown): number {\n  return Number(value);\n}\n\nfunction censorNegativeZero(x: number): number {\n  return x === 0 ? 0 : x;\n}\n\nfunction integerPart(x: number): number {\n  return censorNegativeZero(MathTrunc(x));\n}\n\n// https://heycam.github.io/webidl/#idl-unsigned-long-long\nexport function convertUnsignedLongLongWithEnforceRange(value: unknown, context: string): number {\n  const lowerBound = 0;\n  const upperBound = Number.MAX_SAFE_INTEGER;\n\n  let x = Number(value);\n  x = censorNegativeZero(x);\n\n  if (!NumberIsFinite(x)) {\n    throw new TypeError(`${context} is not a finite number`);\n  }\n\n  x = integerPart(x);\n\n  if (x < lowerBound || x > upperBound) {\n    throw new TypeError(`${context} is outside the accepted range of ${lowerBound} to ${upperBound}, inclusive`);\n  }\n\n  if (!NumberIsFinite(x) || x === 0) {\n    return 0;\n  }\n\n  // TODO Use BigInt if supported?\n  // let xBigInt = BigInt(integerPart(x));\n  // xBigInt = BigInt.asUintN(64, xBigInt);\n  // return Number(xBigInt);\n\n  return x;\n}\n", "import { IsReadableStream, ReadableStream } from '../readable-stream';\n\nexport function assertReadableStream(x: unknown, context: string): asserts x is ReadableStream {\n  if (!IsReadableStream(x)) {\n    throw new TypeError(`${context} is not a ReadableStream.`);\n  }\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport {\n  ReadableStreamReaderGenericCancel,\n  ReadableStreamReaderGenericInitialize,\n  ReadableStreamReaderGenericRelease,\n  readerLockException\n} from './generic-reader';\nimport { IsReadableStreamLocked, ReadableStream } from '../readable-stream';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport { PullSteps } from '../abstract-ops/internal-methods';\nimport { newPromise, promiseRejectedWith } from '../helpers/webidl';\nimport { assertRequiredArgument } from '../validators/basic';\nimport { assertReadableStream } from '../validators/readable-stream';\n\n/**\n * A result returned by {@link ReadableStreamDefaultReader.read}.\n *\n * @public\n */\nexport type ReadableStreamDefaultReadResult<T> = {\n  done: false;\n  value: T;\n} | {\n  done: true;\n  value?: undefined;\n}\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamDefaultReader<R>(stream: ReadableStream): ReadableStreamDefaultReader<R> {\n  return new ReadableStreamDefaultReader(stream);\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamAddReadRequest<R>(stream: ReadableStream<R>,\n                                                readRequest: ReadRequest<R>): void {\n  assert(IsReadableStreamDefaultReader(stream._reader));\n  assert(stream._state === 'readable');\n\n  (stream._reader! as ReadableStreamDefaultReader<R>)._readRequests.push(readRequest);\n}\n\nexport function ReadableStreamFulfillReadRequest<R>(stream: ReadableStream<R>, chunk: R | undefined, done: boolean) {\n  const reader = stream._reader as ReadableStreamDefaultReader<R>;\n\n  assert(reader._readRequests.length > 0);\n\n  const readRequest = reader._readRequests.shift()!;\n  if (done) {\n    readRequest._closeSteps();\n  } else {\n    readRequest._chunkSteps(chunk!);\n  }\n}\n\nexport function ReadableStreamGetNumReadRequests<R>(stream: ReadableStream<R>): number {\n  return (stream._reader as ReadableStreamDefaultReader<R>)._readRequests.length;\n}\n\nexport function ReadableStreamHasDefaultReader(stream: ReadableStream): boolean {\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return false;\n  }\n\n  if (!IsReadableStreamDefaultReader(reader)) {\n    return false;\n  }\n\n  return true;\n}\n\n// Readers\n\nexport interface ReadRequest<R> {\n  _chunkSteps(chunk: R): void;\n\n  _closeSteps(): void;\n\n  _errorSteps(e: any): void;\n}\n\n/**\n * A default reader vended by a {@link ReadableStream}.\n *\n * @public\n */\nexport class ReadableStreamDefaultReader<R = any> {\n  /** @internal */\n  _ownerReadableStream!: ReadableStream<R>;\n  /** @internal */\n  _closedPromise!: Promise<undefined>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readRequests: SimpleQueue<ReadRequest<R>>;\n\n  constructor(stream: ReadableStream<R>) {\n    assertRequiredArgument(stream, 1, 'ReadableStreamDefaultReader');\n    assertReadableStream(stream, 'First parameter');\n\n    if (IsReadableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive reading by another reader');\n    }\n\n    ReadableStreamReaderGenericInitialize(this, stream);\n\n    this._readRequests = new SimpleQueue();\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed,\n   * or rejected if the stream ever errors or the reader's lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<undefined> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('cancel'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('cancel'));\n    }\n\n    return ReadableStreamReaderGenericCancel(this, reason);\n  }\n\n  /**\n   * Returns a promise that allows access to the next chunk from the stream's internal queue, if available.\n   *\n   * If reading a chunk causes the queue to become empty, more data will be pulled from the underlying source.\n   */\n  read(): Promise<ReadableStreamDefaultReadResult<R>> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('read'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('read from'));\n    }\n\n    let resolvePromise!: (result: ReadableStreamDefaultReadResult<R>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamDefaultReadResult<R>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => resolvePromise({ value: chunk, done: false }),\n      _closeSteps: () => resolvePromise({ value: undefined, done: true }),\n      _errorSteps: e => rejectPromise(e)\n    };\n    ReadableStreamDefaultReaderRead(this, readRequest);\n    return promise;\n  }\n\n  /**\n   * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.\n   * If the associated stream is errored when the lock is released, the reader will appear errored in the same way\n   * from now on; otherwise, the reader will appear closed.\n   *\n   * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by\n   * the reader's {@link ReadableStreamDefaultReader.read | read()} method has not yet been settled. Attempting to\n   * do so will throw a `TypeError` and leave the reader locked to the stream.\n   */\n  releaseLock(): void {\n    if (!IsReadableStreamDefaultReader(this)) {\n      throw defaultReaderBrandCheckException('releaseLock');\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return;\n    }\n\n    if (this._readRequests.length > 0) {\n      throw new TypeError('Tried to release a reader lock when that reader has pending read() calls un-settled');\n    }\n\n    ReadableStreamReaderGenericRelease(this);\n  }\n}\n\nObject.defineProperties(ReadableStreamDefaultReader.prototype, {\n  cancel: { enumerable: true },\n  read: { enumerable: true },\n  releaseLock: { enumerable: true },\n  closed: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamDefaultReader.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamDefaultReader',\n    configurable: true\n  });\n}\n\n// Abstract operations for the readers.\n\nexport function IsReadableStreamDefaultReader<R = any>(x: any): x is ReadableStreamDefaultReader<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readRequests')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamDefaultReader;\n}\n\nexport function ReadableStreamDefaultReaderRead<R>(reader: ReadableStreamDefaultReader<R>,\n                                                   readRequest: ReadRequest<R>): void {\n  const stream = reader._ownerReadableStream;\n\n  assert(stream !== undefined);\n\n  stream._disturbed = true;\n\n  if (stream._state === 'closed') {\n    readRequest._closeSteps();\n  } else if (stream._state === 'errored') {\n    readRequest._errorSteps(stream._storedError);\n  } else {\n    assert(stream._state === 'readable');\n    stream._readableStreamController[PullSteps](readRequest as ReadRequest<any>);\n  }\n}\n\n// Helper functions for the ReadableStreamDefaultReader.\n\nfunction defaultReaderBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamDefaultReader.prototype.${name} can only be used on a ReadableStreamDefaultReader`);\n}\n", "/// <reference lib=\"es2018.asynciterable\" />\n\nexport let AsyncIteratorPrototype: AsyncIterable<any> | undefined;\n\nif (typeof Symbol.asyncIterator === 'symbol') {\n  // We're running inside a ES2018+ environment, but we're compiling to an older syntax.\n  // We cannot access %AsyncIteratorPrototype% without non-ES2018 syntax, but we can re-create it.\n  AsyncIteratorPrototype = {\n    // 25.1.3.1 %AsyncIteratorPrototype% [ @@asyncIterator ] ( )\n    // https://tc39.github.io/ecma262/#sec-asynciteratorprototype-asynciterator\n    [Symbol.asyncIterator](this: AsyncIterator<any>) {\n      return this;\n    }\n  };\n  Object.defineProperty(AsyncIteratorPrototype, Symbol.asyncIterator, { enumerable: false });\n}\n", "/// <reference lib=\"es2018.asynciterable\" />\n\nimport { ReadableStream } from '../readable-stream';\nimport {\n  AcquireReadableStreamDefaultReader,\n  ReadableStreamDefaultReader,\n  ReadableStreamDefaultReaderRead,\n  ReadableStreamDefaultReadResult,\n  ReadRequest\n} from './default-reader';\nimport {\n  ReadableStreamReaderGenericCancel,\n  ReadableStreamReaderGenericRelease,\n  readerLockException\n} from './generic-reader';\nimport assert from '../../stub/assert';\nimport { AsyncIteratorPrototype } from '@@target/stub/async-iterator-prototype';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport {\n  newPromise,\n  promiseRejectedWith,\n  promiseResolvedWith,\n  queueMicrotask,\n  transformPromiseWith\n} from '../helpers/webidl';\n\n/**\n * An async iterator returned by {@link ReadableStream.values}.\n *\n * @public\n */\nexport interface ReadableStreamAsyncIterator<R> extends AsyncIterator<R> {\n  next(): Promise<IteratorResult<R, undefined>>;\n\n  return(value?: any): Promise<IteratorResult<any>>;\n}\n\nexport class ReadableStreamAsyncIteratorImpl<R> {\n  private readonly _reader: ReadableStreamDefaultReader<R>;\n  private readonly _preventCancel: boolean;\n  private _ongoingPromise: Promise<ReadableStreamDefaultReadResult<R>> | undefined = undefined;\n  private _isFinished = false;\n\n  constructor(reader: ReadableStreamDefaultReader<R>, preventCancel: boolean) {\n    this._reader = reader;\n    this._preventCancel = preventCancel;\n  }\n\n  next(): Promise<ReadableStreamDefaultReadResult<R>> {\n    const nextSteps = () => this._nextSteps();\n    this._ongoingPromise = this._ongoingPromise ?\n      transformPromiseWith(this._ongoingPromise, nextSteps, nextSteps) :\n      nextSteps();\n    return this._ongoingPromise;\n  }\n\n  return(value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    const returnSteps = () => this._returnSteps(value);\n    return this._ongoingPromise ?\n      transformPromiseWith(this._ongoingPromise, returnSteps, returnSteps) :\n      returnSteps();\n  }\n\n  private _nextSteps(): Promise<ReadableStreamDefaultReadResult<R>> {\n    if (this._isFinished) {\n      return Promise.resolve({ value: undefined, done: true });\n    }\n\n    const reader = this._reader;\n    if (reader._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('iterate'));\n    }\n\n    let resolvePromise!: (result: ReadableStreamDefaultReadResult<R>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamDefaultReadResult<R>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => {\n        this._ongoingPromise = undefined;\n        // This needs to be delayed by one microtask, otherwise we stop pulling too early which breaks a test.\n        // FIXME Is this a bug in the specification, or in the test?\n        queueMicrotask(() => resolvePromise({ value: chunk, done: false }));\n      },\n      _closeSteps: () => {\n        this._ongoingPromise = undefined;\n        this._isFinished = true;\n        ReadableStreamReaderGenericRelease(reader);\n        resolvePromise({ value: undefined, done: true });\n      },\n      _errorSteps: reason => {\n        this._ongoingPromise = undefined;\n        this._isFinished = true;\n        ReadableStreamReaderGenericRelease(reader);\n        rejectPromise(reason);\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n    return promise;\n  }\n\n  private _returnSteps(value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (this._isFinished) {\n      return Promise.resolve({ value, done: true });\n    }\n    this._isFinished = true;\n\n    const reader = this._reader;\n    if (reader._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('finish iterating'));\n    }\n\n    assert(reader._readRequests.length === 0);\n\n    if (!this._preventCancel) {\n      const result = ReadableStreamReaderGenericCancel(reader, value);\n      ReadableStreamReaderGenericRelease(reader);\n      return transformPromiseWith(result, () => ({ value, done: true }));\n    }\n\n    ReadableStreamReaderGenericRelease(reader);\n    return promiseResolvedWith({ value, done: true });\n  }\n}\n\ndeclare class ReadableStreamAsyncIteratorInstance<R> implements ReadableStreamAsyncIterator<R> {\n  /** @interal */\n  _asyncIteratorImpl: ReadableStreamAsyncIteratorImpl<R>;\n\n  next(): Promise<IteratorResult<R, undefined>>;\n\n  return(value?: any): Promise<IteratorResult<any>>;\n}\n\nconst ReadableStreamAsyncIteratorPrototype: ReadableStreamAsyncIteratorInstance<any> = {\n  next(this: ReadableStreamAsyncIteratorInstance<any>): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (!IsReadableStreamAsyncIterator(this)) {\n      return promiseRejectedWith(streamAsyncIteratorBrandCheckException('next'));\n    }\n    return this._asyncIteratorImpl.next();\n  },\n\n  return(this: ReadableStreamAsyncIteratorInstance<any>, value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (!IsReadableStreamAsyncIterator(this)) {\n      return promiseRejectedWith(streamAsyncIteratorBrandCheckException('return'));\n    }\n    return this._asyncIteratorImpl.return(value);\n  }\n} as any;\nif (AsyncIteratorPrototype !== undefined) {\n  Object.setPrototypeOf(ReadableStreamAsyncIteratorPrototype, AsyncIteratorPrototype);\n}\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamAsyncIterator<R>(stream: ReadableStream<R>,\n                                                      preventCancel: boolean): ReadableStreamAsyncIterator<R> {\n  const reader = AcquireReadableStreamDefaultReader<R>(stream);\n  const impl = new ReadableStreamAsyncIteratorImpl(reader, preventCancel);\n  const iterator: ReadableStreamAsyncIteratorInstance<R> = Object.create(ReadableStreamAsyncIteratorPrototype);\n  iterator._asyncIteratorImpl = impl;\n  return iterator;\n}\n\nfunction IsReadableStreamAsyncIterator<R = any>(x: any): x is ReadableStreamAsyncIterator<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_asyncIteratorImpl')) {\n    return false;\n  }\n\n  try {\n    // noinspection SuspiciousTypeOfGuard\n    return (x as ReadableStreamAsyncIteratorInstance<any>)._asyncIteratorImpl instanceof\n      ReadableStreamAsyncIteratorImpl;\n  } catch {\n    return false;\n  }\n}\n\n// Helper functions for the ReadableStream.\n\nfunction streamAsyncIteratorBrandCheckException(name: string): TypeError {\n  return new TypeError(`ReadableStreamAsyncIterator.${name} can only be used on a ReadableSteamAsyncIterator`);\n}\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isNaN#Polyfill\nconst NumberIsNaN: typeof Number.isNaN = Number.isNaN || function (x) {\n  // eslint-disable-next-line no-self-compare\n  return x !== x;\n};\n\nexport default NumberIsNaN;\n", "export function CreateArrayFromList<T extends any[]>(elements: T): T {\n  // We use arrays to represent lists, so this is basically a no-op.\n  // Do a slice though just in case we happen to depend on the unique-ness.\n  return elements.slice() as T;\n}\n\nexport function CopyDataBlockBytes(dest: ArrayBuffer,\n                                   destOffset: number,\n                                   src: ArrayBuffer,\n                                   srcOffset: number,\n                                   n: number) {\n  new Uint8Array(dest).set(new Uint8Array(src, srcOffset, n), destOffset);\n}\n\n// Not implemented correctly\nexport function TransferArrayBuffer<T extends ArrayBufferLike>(O: T): T {\n  return O;\n}\n\n// Not implemented correctly\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function CanTransferArrayBuffer(O: ArrayBufferLike): boolean {\n  return true;\n}\n\n// Not implemented correctly\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function IsDetachedBuffer(O: ArrayBufferLike): boolean {\n  return false;\n}\n\nexport function ArrayBufferSlice(buffer: ArrayBufferLike, begin: number, end: number): ArrayBufferLike {\n  // ArrayBuffer.prototype.slice is not available on IE10\n  // https://www.caniuse.com/mdn-javascript_builtins_arraybuffer_slice\n  if (buffer.slice) {\n    return buffer.slice(begin, end);\n  }\n  const length = end - begin;\n  const slice = new ArrayBuffer(length);\n  CopyDataBlockBytes(slice, 0, buffer, begin, length);\n  return slice;\n}\n", "import NumberIsNaN from '../../stub/number-isnan';\nimport { ArrayBufferSlice } from './ecmascript';\n\nexport function IsNonNegativeNumber(v: number): boolean {\n  if (typeof v !== 'number') {\n    return false;\n  }\n\n  if (NumberIsNaN(v)) {\n    return false;\n  }\n\n  if (v < 0) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function CloneAsUint8Array(O: ArrayBufferView): Uint8Array {\n  const buffer = ArrayBufferSlice(O.buffer, O.byteOffset, O.byteOffset + O.byteLength);\n  return new Uint8Array(buffer);\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport { IsNonNegativeNumber } from './miscellaneous';\n\nexport interface QueueContainer<T> {\n  _queue: SimpleQueue<T>;\n  _queueTotalSize: number;\n}\n\nexport interface QueuePair<T> {\n  value: T;\n  size: number;\n}\n\nexport function DequeueValue<T>(container: QueueContainer<QueuePair<T>>): T {\n  assert('_queue' in container && '_queueTotalSize' in container);\n  assert(container._queue.length > 0);\n\n  const pair = container._queue.shift()!;\n  container._queueTotalSize -= pair.size;\n  if (container._queueTotalSize < 0) {\n    container._queueTotalSize = 0;\n  }\n\n  return pair.value;\n}\n\nexport function EnqueueValueWithSize<T>(container: QueueContainer<QueuePair<T>>, value: T, size: number) {\n  assert('_queue' in container && '_queueTotalSize' in container);\n\n  if (!IsNonNegativeNumber(size) || size === Infinity) {\n    throw new RangeError('Size must be a finite, non-NaN, non-negative number.');\n  }\n\n  container._queue.push({ value, size });\n  container._queueTotalSize += size;\n}\n\nexport function PeekQueueValue<T>(container: QueueContainer<QueuePair<T>>): T {\n  assert('_queue' in container && '_queueTotalSize' in container);\n  assert(container._queue.length > 0);\n\n  const pair = container._queue.peek();\n  return pair.value;\n}\n\nexport function ResetQueue<T>(container: QueueContainer<T>) {\n  assert('_queue' in container && '_queueTotalSize' in container);\n\n  container._queue = new SimpleQueue<T>();\n  container._queueTotalSize = 0;\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport { ResetQueue } from '../abstract-ops/queue-with-sizes';\nimport {\n  ReadableStreamAddReadRequest,\n  ReadableStreamFulfillReadRequest,\n  ReadableStreamGetNumReadRequests,\n  ReadableStreamHasDefaultReader,\n  ReadRequest\n} from './default-reader';\nimport {\n  ReadableStreamAddReadIntoRequest,\n  ReadableStreamFulfillReadIntoRequest,\n  ReadableStreamGetNumReadIntoRequests,\n  ReadableStreamHasBYOBReader,\n  ReadIntoRequest\n} from './byob-reader';\nimport NumberIsInteger from '../../stub/number-isinteger';\nimport {\n  IsReadableStreamLocked,\n  ReadableByteStream,\n  ReadableStreamClose,\n  ReadableStreamError\n} from '../readable-stream';\nimport { ValidatedUnderlyingByteSource } from './underlying-source';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport {\n  <PERSON>rrayBufferSlice,\n  CanTransferArrayBuffer,\n  CopyDataBlockBytes,\n  IsDetachedBuffer,\n  TransferArrayBuffer\n} from '../abstract-ops/ecmascript';\nimport { CancelSteps, PullSteps } from '../abstract-ops/internal-methods';\nimport { promiseResolvedWith, uponPromise } from '../helpers/webidl';\nimport { assertRequiredArgument, convertUnsignedLongLongWithEnforceRange } from '../validators/basic';\n\n/**\n * A pull-into request in a {@link ReadableByteStreamController}.\n *\n * @public\n */\nexport class ReadableStreamBYOBRequest {\n  /** @internal */\n  _associatedReadableByteStreamController!: ReadableByteStreamController;\n  /** @internal */\n  _view!: ArrayBufferView | null;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the view for writing in to, or `null` if the BYOB request has already been responded to.\n   */\n  get view(): ArrayBufferView | null {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('view');\n    }\n\n    return this._view;\n  }\n\n  /**\n   * Indicates to the associated readable byte stream that `bytesWritten` bytes were written into\n   * {@link ReadableStreamBYOBRequest.view | view}, causing the result be surfaced to the consumer.\n   *\n   * After this method is called, {@link ReadableStreamBYOBRequest.view | view} will be transferred and no longer\n   * modifiable.\n   */\n  respond(bytesWritten: number): void;\n  respond(bytesWritten: number | undefined): void {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('respond');\n    }\n    assertRequiredArgument(bytesWritten, 1, 'respond');\n    bytesWritten = convertUnsignedLongLongWithEnforceRange(bytesWritten, 'First parameter');\n\n    if (this._associatedReadableByteStreamController === undefined) {\n      throw new TypeError('This BYOB request has been invalidated');\n    }\n\n    if (IsDetachedBuffer(this._view!.buffer)) {\n      throw new TypeError(`The BYOB request's buffer has been detached and so cannot be used as a response`);\n    }\n\n    assert(this._view!.byteLength > 0);\n    assert(this._view!.buffer.byteLength > 0);\n\n    ReadableByteStreamControllerRespond(this._associatedReadableByteStreamController, bytesWritten);\n  }\n\n  /**\n   * Indicates to the associated readable byte stream that instead of writing into\n   * {@link ReadableStreamBYOBRequest.view | view}, the underlying byte source is providing a new `ArrayBufferView`,\n   * which will be given to the consumer of the readable byte stream.\n   *\n   * After this method is called, `view` will be transferred and no longer modifiable.\n   */\n  respondWithNewView(view: ArrayBufferView): void;\n  respondWithNewView(view: ArrayBufferView | undefined): void {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('respondWithNewView');\n    }\n    assertRequiredArgument(view, 1, 'respondWithNewView');\n\n    if (!ArrayBuffer.isView(view)) {\n      throw new TypeError('You can only respond with array buffer views');\n    }\n\n    if (this._associatedReadableByteStreamController === undefined) {\n      throw new TypeError('This BYOB request has been invalidated');\n    }\n\n    if (IsDetachedBuffer(view.buffer)) {\n      throw new TypeError('The given view\\'s buffer has been detached and so cannot be used as a response');\n    }\n\n    ReadableByteStreamControllerRespondWithNewView(this._associatedReadableByteStreamController, view);\n  }\n}\n\nObject.defineProperties(ReadableStreamBYOBRequest.prototype, {\n  respond: { enumerable: true },\n  respondWithNewView: { enumerable: true },\n  view: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamBYOBRequest.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamBYOBRequest',\n    configurable: true\n  });\n}\n\ninterface ArrayBufferViewConstructor<T extends ArrayBufferView = ArrayBufferView> {\n  new(buffer: ArrayBufferLike, byteOffset: number, length?: number): T;\n\n  readonly prototype: T;\n  readonly BYTES_PER_ELEMENT: number;\n}\n\ninterface ByteQueueElement {\n  buffer: ArrayBufferLike;\n  byteOffset: number;\n  byteLength: number;\n}\n\ntype PullIntoDescriptor<T extends ArrayBufferView = ArrayBufferView> =\n  DefaultPullIntoDescriptor\n  | BYOBPullIntoDescriptor<T>;\n\ninterface DefaultPullIntoDescriptor {\n  buffer: ArrayBufferLike;\n  bufferByteLength: number;\n  byteOffset: number;\n  byteLength: number;\n  bytesFilled: number;\n  elementSize: number;\n  viewConstructor: ArrayBufferViewConstructor<Uint8Array>;\n  readerType: 'default';\n}\n\ninterface BYOBPullIntoDescriptor<T extends ArrayBufferView = ArrayBufferView> {\n  buffer: ArrayBufferLike;\n  bufferByteLength: number;\n  byteOffset: number;\n  byteLength: number;\n  bytesFilled: number;\n  elementSize: number;\n  viewConstructor: ArrayBufferViewConstructor<T>;\n  readerType: 'byob';\n}\n\n/**\n * Allows control of a {@link ReadableStream | readable byte stream}'s state and internal queue.\n *\n * @public\n */\nexport class ReadableByteStreamController {\n  /** @internal */\n  _controlledReadableByteStream!: ReadableByteStream;\n  /** @internal */\n  _queue!: SimpleQueue<ByteQueueElement>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _closeRequested!: boolean;\n  /** @internal */\n  _pullAgain!: boolean;\n  /** @internal */\n  _pulling !: boolean;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _pullAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _cancelAlgorithm!: (reason: any) => Promise<void>;\n  /** @internal */\n  _autoAllocateChunkSize: number | undefined;\n  /** @internal */\n  _byobRequest: ReadableStreamBYOBRequest | null;\n  /** @internal */\n  _pendingPullIntos!: SimpleQueue<PullIntoDescriptor>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the current BYOB pull request, or `null` if there isn't one.\n   */\n  get byobRequest(): ReadableStreamBYOBRequest | null {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('byobRequest');\n    }\n\n    return ReadableByteStreamControllerGetBYOBRequest(this);\n  }\n\n  /**\n   * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is\n   * over-full. An underlying byte source ought to use this information to determine when and how to apply backpressure.\n   */\n  get desiredSize(): number | null {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('desiredSize');\n    }\n\n    return ReadableByteStreamControllerGetDesiredSize(this);\n  }\n\n  /**\n   * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from\n   * the stream, but once those are read, the stream will become closed.\n   */\n  close(): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('close');\n    }\n\n    if (this._closeRequested) {\n      throw new TypeError('The stream has already been closed; do not close it again!');\n    }\n\n    const state = this._controlledReadableByteStream._state;\n    if (state !== 'readable') {\n      throw new TypeError(`The stream (in ${state} state) is not in the readable state and cannot be closed`);\n    }\n\n    ReadableByteStreamControllerClose(this);\n  }\n\n  /**\n   * Enqueues the given chunk chunk in the controlled readable stream.\n   * The chunk has to be an `ArrayBufferView` instance, or else a `TypeError` will be thrown.\n   */\n  enqueue(chunk: ArrayBufferView): void;\n  enqueue(chunk: ArrayBufferView | undefined): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('enqueue');\n    }\n\n    assertRequiredArgument(chunk, 1, 'enqueue');\n    if (!ArrayBuffer.isView(chunk)) {\n      throw new TypeError('chunk must be an array buffer view');\n    }\n    if (chunk.byteLength === 0) {\n      throw new TypeError('chunk must have non-zero byteLength');\n    }\n    if (chunk.buffer.byteLength === 0) {\n      throw new TypeError(`chunk's buffer must have non-zero byteLength`);\n    }\n\n    if (this._closeRequested) {\n      throw new TypeError('stream is closed or draining');\n    }\n\n    const state = this._controlledReadableByteStream._state;\n    if (state !== 'readable') {\n      throw new TypeError(`The stream (in ${state} state) is not in the readable state and cannot be enqueued to`);\n    }\n\n    ReadableByteStreamControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.\n   */\n  error(e: any = undefined): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('error');\n    }\n\n    ReadableByteStreamControllerError(this, e);\n  }\n\n  /** @internal */\n  [CancelSteps](reason: any): Promise<void> {\n    ReadableByteStreamControllerClearPendingPullIntos(this);\n\n    ResetQueue(this);\n\n    const result = this._cancelAlgorithm(reason);\n    ReadableByteStreamControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [PullSteps](readRequest: ReadRequest<Uint8Array>): void {\n    const stream = this._controlledReadableByteStream;\n    assert(ReadableStreamHasDefaultReader(stream));\n\n    if (this._queueTotalSize > 0) {\n      assert(ReadableStreamGetNumReadRequests(stream) === 0);\n\n      const entry = this._queue.shift()!;\n      this._queueTotalSize -= entry.byteLength;\n\n      ReadableByteStreamControllerHandleQueueDrain(this);\n\n      const view = new Uint8Array(entry.buffer, entry.byteOffset, entry.byteLength);\n\n      readRequest._chunkSteps(view);\n      return;\n    }\n\n    const autoAllocateChunkSize = this._autoAllocateChunkSize;\n    if (autoAllocateChunkSize !== undefined) {\n      let buffer: ArrayBuffer;\n      try {\n        buffer = new ArrayBuffer(autoAllocateChunkSize);\n      } catch (bufferE) {\n        readRequest._errorSteps(bufferE);\n        return;\n      }\n\n      const pullIntoDescriptor: DefaultPullIntoDescriptor = {\n        buffer,\n        bufferByteLength: autoAllocateChunkSize,\n        byteOffset: 0,\n        byteLength: autoAllocateChunkSize,\n        bytesFilled: 0,\n        elementSize: 1,\n        viewConstructor: Uint8Array,\n        readerType: 'default'\n      };\n\n      this._pendingPullIntos.push(pullIntoDescriptor);\n    }\n\n    ReadableStreamAddReadRequest(stream, readRequest);\n    ReadableByteStreamControllerCallPullIfNeeded(this);\n  }\n}\n\nObject.defineProperties(ReadableByteStreamController.prototype, {\n  close: { enumerable: true },\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  byobRequest: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableByteStreamController.prototype, Symbol.toStringTag, {\n    value: 'ReadableByteStreamController',\n    configurable: true\n  });\n}\n\n// Abstract operations for the ReadableByteStreamController.\n\nexport function IsReadableByteStreamController(x: any): x is ReadableByteStreamController {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledReadableByteStream')) {\n    return false;\n  }\n\n  return x instanceof ReadableByteStreamController;\n}\n\nfunction IsReadableStreamBYOBRequest(x: any): x is ReadableStreamBYOBRequest {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_associatedReadableByteStreamController')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamBYOBRequest;\n}\n\nfunction ReadableByteStreamControllerCallPullIfNeeded(controller: ReadableByteStreamController): void {\n  const shouldPull = ReadableByteStreamControllerShouldCallPull(controller);\n  if (!shouldPull) {\n    return;\n  }\n\n  if (controller._pulling) {\n    controller._pullAgain = true;\n    return;\n  }\n\n  assert(!controller._pullAgain);\n\n  controller._pulling = true;\n\n  // TODO: Test controller argument\n  const pullPromise = controller._pullAlgorithm();\n  uponPromise(\n    pullPromise,\n    () => {\n      controller._pulling = false;\n\n      if (controller._pullAgain) {\n        controller._pullAgain = false;\n        ReadableByteStreamControllerCallPullIfNeeded(controller);\n      }\n    },\n    e => {\n      ReadableByteStreamControllerError(controller, e);\n    }\n  );\n}\n\nfunction ReadableByteStreamControllerClearPendingPullIntos(controller: ReadableByteStreamController) {\n  ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n  controller._pendingPullIntos = new SimpleQueue();\n}\n\nfunction ReadableByteStreamControllerCommitPullIntoDescriptor<T extends ArrayBufferView>(\n  stream: ReadableByteStream,\n  pullIntoDescriptor: PullIntoDescriptor<T>\n) {\n  assert(stream._state !== 'errored');\n\n  let done = false;\n  if (stream._state === 'closed') {\n    assert(pullIntoDescriptor.bytesFilled === 0);\n    done = true;\n  }\n\n  const filledView = ReadableByteStreamControllerConvertPullIntoDescriptor<T>(pullIntoDescriptor);\n  if (pullIntoDescriptor.readerType === 'default') {\n    ReadableStreamFulfillReadRequest(stream, filledView as unknown as Uint8Array, done);\n  } else {\n    assert(pullIntoDescriptor.readerType === 'byob');\n    ReadableStreamFulfillReadIntoRequest(stream, filledView, done);\n  }\n}\n\nfunction ReadableByteStreamControllerConvertPullIntoDescriptor<T extends ArrayBufferView>(\n  pullIntoDescriptor: PullIntoDescriptor<T>\n): T {\n  const bytesFilled = pullIntoDescriptor.bytesFilled;\n  const elementSize = pullIntoDescriptor.elementSize;\n\n  assert(bytesFilled <= pullIntoDescriptor.byteLength);\n  assert(bytesFilled % elementSize === 0);\n\n  return new pullIntoDescriptor.viewConstructor(\n    pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, bytesFilled / elementSize) as T;\n}\n\nfunction ReadableByteStreamControllerEnqueueChunkToQueue(controller: ReadableByteStreamController,\n                                                         buffer: ArrayBufferLike,\n                                                         byteOffset: number,\n                                                         byteLength: number) {\n  controller._queue.push({ buffer, byteOffset, byteLength });\n  controller._queueTotalSize += byteLength;\n}\n\nfunction ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller: ReadableByteStreamController,\n                                                                     pullIntoDescriptor: PullIntoDescriptor) {\n  const elementSize = pullIntoDescriptor.elementSize;\n\n  const currentAlignedBytes = pullIntoDescriptor.bytesFilled - pullIntoDescriptor.bytesFilled % elementSize;\n\n  const maxBytesToCopy = Math.min(controller._queueTotalSize,\n                                  pullIntoDescriptor.byteLength - pullIntoDescriptor.bytesFilled);\n  const maxBytesFilled = pullIntoDescriptor.bytesFilled + maxBytesToCopy;\n  const maxAlignedBytes = maxBytesFilled - maxBytesFilled % elementSize;\n\n  let totalBytesToCopyRemaining = maxBytesToCopy;\n  let ready = false;\n  if (maxAlignedBytes > currentAlignedBytes) {\n    totalBytesToCopyRemaining = maxAlignedBytes - pullIntoDescriptor.bytesFilled;\n    ready = true;\n  }\n\n  const queue = controller._queue;\n\n  while (totalBytesToCopyRemaining > 0) {\n    const headOfQueue = queue.peek();\n\n    const bytesToCopy = Math.min(totalBytesToCopyRemaining, headOfQueue.byteLength);\n\n    const destStart = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;\n    CopyDataBlockBytes(pullIntoDescriptor.buffer, destStart, headOfQueue.buffer, headOfQueue.byteOffset, bytesToCopy);\n\n    if (headOfQueue.byteLength === bytesToCopy) {\n      queue.shift();\n    } else {\n      headOfQueue.byteOffset += bytesToCopy;\n      headOfQueue.byteLength -= bytesToCopy;\n    }\n    controller._queueTotalSize -= bytesToCopy;\n\n    ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesToCopy, pullIntoDescriptor);\n\n    totalBytesToCopyRemaining -= bytesToCopy;\n  }\n\n  if (!ready) {\n    assert(controller._queueTotalSize === 0);\n    assert(pullIntoDescriptor.bytesFilled > 0);\n    assert(pullIntoDescriptor.bytesFilled < pullIntoDescriptor.elementSize);\n  }\n\n  return ready;\n}\n\nfunction ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller: ReadableByteStreamController,\n                                                                size: number,\n                                                                pullIntoDescriptor: PullIntoDescriptor) {\n  assert(controller._pendingPullIntos.length === 0 || controller._pendingPullIntos.peek() === pullIntoDescriptor);\n  assert(controller._byobRequest === null);\n  pullIntoDescriptor.bytesFilled += size;\n}\n\nfunction ReadableByteStreamControllerHandleQueueDrain(controller: ReadableByteStreamController) {\n  assert(controller._controlledReadableByteStream._state === 'readable');\n\n  if (controller._queueTotalSize === 0 && controller._closeRequested) {\n    ReadableByteStreamControllerClearAlgorithms(controller);\n    ReadableStreamClose(controller._controlledReadableByteStream);\n  } else {\n    ReadableByteStreamControllerCallPullIfNeeded(controller);\n  }\n}\n\nfunction ReadableByteStreamControllerInvalidateBYOBRequest(controller: ReadableByteStreamController) {\n  if (controller._byobRequest === null) {\n    return;\n  }\n\n  controller._byobRequest._associatedReadableByteStreamController = undefined!;\n  controller._byobRequest._view = null!;\n  controller._byobRequest = null;\n}\n\nfunction ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller: ReadableByteStreamController) {\n  assert(!controller._closeRequested);\n\n  while (controller._pendingPullIntos.length > 0) {\n    if (controller._queueTotalSize === 0) {\n      return;\n    }\n\n    const pullIntoDescriptor = controller._pendingPullIntos.peek();\n\n    if (ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor)) {\n      ReadableByteStreamControllerShiftPendingPullInto(controller);\n\n      ReadableByteStreamControllerCommitPullIntoDescriptor(\n        controller._controlledReadableByteStream,\n        pullIntoDescriptor\n      );\n    }\n  }\n}\n\nexport function ReadableByteStreamControllerPullInto<T extends ArrayBufferView>(\n  controller: ReadableByteStreamController,\n  view: T,\n  readIntoRequest: ReadIntoRequest<T>\n): void {\n  const stream = controller._controlledReadableByteStream;\n\n  let elementSize = 1;\n  if (view.constructor !== DataView) {\n    elementSize = (view.constructor as ArrayBufferViewConstructor<T>).BYTES_PER_ELEMENT;\n  }\n\n  const ctor = view.constructor as ArrayBufferViewConstructor<T>;\n\n  // try {\n  const buffer = TransferArrayBuffer(view.buffer);\n  // } catch (e) {\n  //   readIntoRequest._errorSteps(e);\n  //   return;\n  // }\n\n  const pullIntoDescriptor: BYOBPullIntoDescriptor<T> = {\n    buffer,\n    bufferByteLength: buffer.byteLength,\n    byteOffset: view.byteOffset,\n    byteLength: view.byteLength,\n    bytesFilled: 0,\n    elementSize,\n    viewConstructor: ctor,\n    readerType: 'byob'\n  };\n\n  if (controller._pendingPullIntos.length > 0) {\n    controller._pendingPullIntos.push(pullIntoDescriptor);\n\n    // No ReadableByteStreamControllerCallPullIfNeeded() call since:\n    // - No change happens on desiredSize\n    // - The source has already been notified of that there's at least 1 pending read(view)\n\n    ReadableStreamAddReadIntoRequest(stream, readIntoRequest);\n    return;\n  }\n\n  if (stream._state === 'closed') {\n    const emptyView = new ctor(pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, 0);\n    readIntoRequest._closeSteps(emptyView);\n    return;\n  }\n\n  if (controller._queueTotalSize > 0) {\n    if (ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor)) {\n      const filledView = ReadableByteStreamControllerConvertPullIntoDescriptor<T>(pullIntoDescriptor);\n\n      ReadableByteStreamControllerHandleQueueDrain(controller);\n\n      readIntoRequest._chunkSteps(filledView);\n      return;\n    }\n\n    if (controller._closeRequested) {\n      const e = new TypeError('Insufficient bytes to fill elements in the given buffer');\n      ReadableByteStreamControllerError(controller, e);\n\n      readIntoRequest._errorSteps(e);\n      return;\n    }\n  }\n\n  controller._pendingPullIntos.push(pullIntoDescriptor);\n\n  ReadableStreamAddReadIntoRequest<T>(stream, readIntoRequest);\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nfunction ReadableByteStreamControllerRespondInClosedState(controller: ReadableByteStreamController,\n                                                          firstDescriptor: PullIntoDescriptor) {\n  assert(firstDescriptor.bytesFilled === 0);\n\n  const stream = controller._controlledReadableByteStream;\n  if (ReadableStreamHasBYOBReader(stream)) {\n    while (ReadableStreamGetNumReadIntoRequests(stream) > 0) {\n      const pullIntoDescriptor = ReadableByteStreamControllerShiftPendingPullInto(controller);\n      ReadableByteStreamControllerCommitPullIntoDescriptor(stream, pullIntoDescriptor);\n    }\n  }\n}\n\nfunction ReadableByteStreamControllerRespondInReadableState(controller: ReadableByteStreamController,\n                                                            bytesWritten: number,\n                                                            pullIntoDescriptor: PullIntoDescriptor) {\n  assert(pullIntoDescriptor.bytesFilled + bytesWritten <= pullIntoDescriptor.byteLength);\n\n  ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesWritten, pullIntoDescriptor);\n\n  if (pullIntoDescriptor.bytesFilled < pullIntoDescriptor.elementSize) {\n    return;\n  }\n\n  ReadableByteStreamControllerShiftPendingPullInto(controller);\n\n  const remainderSize = pullIntoDescriptor.bytesFilled % pullIntoDescriptor.elementSize;\n  if (remainderSize > 0) {\n    const end = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;\n    const remainder = ArrayBufferSlice(pullIntoDescriptor.buffer, end - remainderSize, end);\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, remainder, 0, remainder.byteLength);\n  }\n\n  pullIntoDescriptor.bytesFilled -= remainderSize;\n  ReadableByteStreamControllerCommitPullIntoDescriptor(controller._controlledReadableByteStream, pullIntoDescriptor);\n\n  ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n}\n\nfunction ReadableByteStreamControllerRespondInternal(controller: ReadableByteStreamController, bytesWritten: number) {\n  const firstDescriptor = controller._pendingPullIntos.peek();\n  assert(CanTransferArrayBuffer(firstDescriptor.buffer));\n\n  ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n\n  const state = controller._controlledReadableByteStream._state;\n  if (state === 'closed') {\n    assert(bytesWritten === 0);\n    ReadableByteStreamControllerRespondInClosedState(controller, firstDescriptor);\n  } else {\n    assert(state === 'readable');\n    assert(bytesWritten > 0);\n    ReadableByteStreamControllerRespondInReadableState(controller, bytesWritten, firstDescriptor);\n  }\n\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nfunction ReadableByteStreamControllerShiftPendingPullInto(\n  controller: ReadableByteStreamController\n): PullIntoDescriptor {\n  assert(controller._byobRequest === null);\n  const descriptor = controller._pendingPullIntos.shift()!;\n  return descriptor;\n}\n\nfunction ReadableByteStreamControllerShouldCallPull(controller: ReadableByteStreamController): boolean {\n  const stream = controller._controlledReadableByteStream;\n\n  if (stream._state !== 'readable') {\n    return false;\n  }\n\n  if (controller._closeRequested) {\n    return false;\n  }\n\n  if (!controller._started) {\n    return false;\n  }\n\n  if (ReadableStreamHasDefaultReader(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    return true;\n  }\n\n  if (ReadableStreamHasBYOBReader(stream) && ReadableStreamGetNumReadIntoRequests(stream) > 0) {\n    return true;\n  }\n\n  const desiredSize = ReadableByteStreamControllerGetDesiredSize(controller);\n  assert(desiredSize !== null);\n  if (desiredSize! > 0) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction ReadableByteStreamControllerClearAlgorithms(controller: ReadableByteStreamController) {\n  controller._pullAlgorithm = undefined!;\n  controller._cancelAlgorithm = undefined!;\n}\n\n// A client of ReadableByteStreamController may use these functions directly to bypass state check.\n\nexport function ReadableByteStreamControllerClose(controller: ReadableByteStreamController) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (controller._closeRequested || stream._state !== 'readable') {\n    return;\n  }\n\n  if (controller._queueTotalSize > 0) {\n    controller._closeRequested = true;\n\n    return;\n  }\n\n  if (controller._pendingPullIntos.length > 0) {\n    const firstPendingPullInto = controller._pendingPullIntos.peek();\n    if (firstPendingPullInto.bytesFilled > 0) {\n      const e = new TypeError('Insufficient bytes to fill elements in the given buffer');\n      ReadableByteStreamControllerError(controller, e);\n\n      throw e;\n    }\n  }\n\n  ReadableByteStreamControllerClearAlgorithms(controller);\n  ReadableStreamClose(stream);\n}\n\nexport function ReadableByteStreamControllerEnqueue(controller: ReadableByteStreamController, chunk: ArrayBufferView) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (controller._closeRequested || stream._state !== 'readable') {\n    return;\n  }\n\n  const buffer = chunk.buffer;\n  const byteOffset = chunk.byteOffset;\n  const byteLength = chunk.byteLength;\n  if (IsDetachedBuffer(buffer)) {\n    throw new TypeError('chunk\\'s buffer is detached and so cannot be enqueued');\n  }\n  const transferredBuffer = TransferArrayBuffer(buffer);\n\n  if (controller._pendingPullIntos.length > 0) {\n    const firstPendingPullInto = controller._pendingPullIntos.peek();\n    if (IsDetachedBuffer(firstPendingPullInto.buffer)) {\n      throw new TypeError(\n        'The BYOB request\\'s buffer has been detached and so cannot be filled with an enqueued chunk'\n      );\n    }\n    firstPendingPullInto.buffer = TransferArrayBuffer(firstPendingPullInto.buffer);\n  }\n\n  ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n\n  if (ReadableStreamHasDefaultReader(stream)) {\n    if (ReadableStreamGetNumReadRequests(stream) === 0) {\n      assert(controller._pendingPullIntos.length === 0);\n      ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n    } else {\n      assert(controller._queue.length === 0);\n      if (controller._pendingPullIntos.length > 0) {\n        assert(controller._pendingPullIntos.peek().readerType === 'default');\n        ReadableByteStreamControllerShiftPendingPullInto(controller);\n      }\n      const transferredView = new Uint8Array(transferredBuffer, byteOffset, byteLength);\n      ReadableStreamFulfillReadRequest(stream, transferredView, false);\n    }\n  } else if (ReadableStreamHasBYOBReader(stream)) {\n    // TODO: Ideally in this branch detaching should happen only if the buffer is not consumed fully.\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n    ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n  } else {\n    assert(!IsReadableStreamLocked(stream));\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n  }\n\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nexport function ReadableByteStreamControllerError(controller: ReadableByteStreamController, e: any) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (stream._state !== 'readable') {\n    return;\n  }\n\n  ReadableByteStreamControllerClearPendingPullIntos(controller);\n\n  ResetQueue(controller);\n  ReadableByteStreamControllerClearAlgorithms(controller);\n  ReadableStreamError(stream, e);\n}\n\nexport function ReadableByteStreamControllerGetBYOBRequest(\n  controller: ReadableByteStreamController\n): ReadableStreamBYOBRequest | null {\n  if (controller._byobRequest === null && controller._pendingPullIntos.length > 0) {\n    const firstDescriptor = controller._pendingPullIntos.peek();\n    const view = new Uint8Array(firstDescriptor.buffer,\n                                firstDescriptor.byteOffset + firstDescriptor.bytesFilled,\n                                firstDescriptor.byteLength - firstDescriptor.bytesFilled);\n\n    const byobRequest: ReadableStreamBYOBRequest = Object.create(ReadableStreamBYOBRequest.prototype);\n    SetUpReadableStreamBYOBRequest(byobRequest, controller, view);\n    controller._byobRequest = byobRequest;\n  }\n  return controller._byobRequest;\n}\n\nfunction ReadableByteStreamControllerGetDesiredSize(controller: ReadableByteStreamController): number | null {\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'errored') {\n    return null;\n  }\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\nexport function ReadableByteStreamControllerRespond(controller: ReadableByteStreamController, bytesWritten: number) {\n  assert(controller._pendingPullIntos.length > 0);\n\n  const firstDescriptor = controller._pendingPullIntos.peek();\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'closed') {\n    if (bytesWritten !== 0) {\n      throw new TypeError('bytesWritten must be 0 when calling respond() on a closed stream');\n    }\n  } else {\n    assert(state === 'readable');\n    if (bytesWritten === 0) {\n      throw new TypeError('bytesWritten must be greater than 0 when calling respond() on a readable stream');\n    }\n    if (firstDescriptor.bytesFilled + bytesWritten > firstDescriptor.byteLength) {\n      throw new RangeError('bytesWritten out of range');\n    }\n  }\n\n  firstDescriptor.buffer = TransferArrayBuffer(firstDescriptor.buffer);\n\n  ReadableByteStreamControllerRespondInternal(controller, bytesWritten);\n}\n\nexport function ReadableByteStreamControllerRespondWithNewView(controller: ReadableByteStreamController,\n                                                               view: ArrayBufferView) {\n  assert(controller._pendingPullIntos.length > 0);\n  assert(!IsDetachedBuffer(view.buffer));\n\n  const firstDescriptor = controller._pendingPullIntos.peek();\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'closed') {\n    if (view.byteLength !== 0) {\n      throw new TypeError('The view\\'s length must be 0 when calling respondWithNewView() on a closed stream');\n    }\n  } else {\n    assert(state === 'readable');\n    if (view.byteLength === 0) {\n      throw new TypeError(\n        'The view\\'s length must be greater than 0 when calling respondWithNewView() on a readable stream'\n      );\n    }\n  }\n\n  if (firstDescriptor.byteOffset + firstDescriptor.bytesFilled !== view.byteOffset) {\n    throw new RangeError('The region specified by view does not match byobRequest');\n  }\n  if (firstDescriptor.bufferByteLength !== view.buffer.byteLength) {\n    throw new RangeError('The buffer of view has different capacity than byobRequest');\n  }\n  if (firstDescriptor.bytesFilled + view.byteLength > firstDescriptor.byteLength) {\n    throw new RangeError('The region specified by view is larger than byobRequest');\n  }\n\n  const viewByteLength = view.byteLength;\n  firstDescriptor.buffer = TransferArrayBuffer(view.buffer);\n  ReadableByteStreamControllerRespondInternal(controller, viewByteLength);\n}\n\nexport function SetUpReadableByteStreamController(stream: ReadableByteStream,\n                                                  controller: ReadableByteStreamController,\n                                                  startAlgorithm: () => void | PromiseLike<void>,\n                                                  pullAlgorithm: () => Promise<void>,\n                                                  cancelAlgorithm: (reason: any) => Promise<void>,\n                                                  highWaterMark: number,\n                                                  autoAllocateChunkSize: number | undefined) {\n  assert(stream._readableStreamController === undefined);\n  if (autoAllocateChunkSize !== undefined) {\n    assert(NumberIsInteger(autoAllocateChunkSize));\n    assert(autoAllocateChunkSize > 0);\n  }\n\n  controller._controlledReadableByteStream = stream;\n\n  controller._pullAgain = false;\n  controller._pulling = false;\n\n  controller._byobRequest = null;\n\n  // Need to set the slots so that the assert doesn't fire. In the spec the slots already exist implicitly.\n  controller._queue = controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._closeRequested = false;\n  controller._started = false;\n\n  controller._strategyHWM = highWaterMark;\n\n  controller._pullAlgorithm = pullAlgorithm;\n  controller._cancelAlgorithm = cancelAlgorithm;\n\n  controller._autoAllocateChunkSize = autoAllocateChunkSize;\n\n  controller._pendingPullIntos = new SimpleQueue();\n\n  stream._readableStreamController = controller;\n\n  const startResult = startAlgorithm();\n  uponPromise(\n    promiseResolvedWith(startResult),\n    () => {\n      controller._started = true;\n\n      assert(!controller._pulling);\n      assert(!controller._pullAgain);\n\n      ReadableByteStreamControllerCallPullIfNeeded(controller);\n    },\n    r => {\n      ReadableByteStreamControllerError(controller, r);\n    }\n  );\n}\n\nexport function SetUpReadableByteStreamControllerFromUnderlyingSource(\n  stream: ReadableByteStream,\n  underlyingByteSource: ValidatedUnderlyingByteSource,\n  highWaterMark: number\n) {\n  const controller: ReadableByteStreamController = Object.create(ReadableByteStreamController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void> = () => undefined;\n  let pullAlgorithm: () => Promise<void> = () => promiseResolvedWith(undefined);\n  let cancelAlgorithm: (reason: any) => Promise<void> = () => promiseResolvedWith(undefined);\n\n  if (underlyingByteSource.start !== undefined) {\n    startAlgorithm = () => underlyingByteSource.start!(controller);\n  }\n  if (underlyingByteSource.pull !== undefined) {\n    pullAlgorithm = () => underlyingByteSource.pull!(controller);\n  }\n  if (underlyingByteSource.cancel !== undefined) {\n    cancelAlgorithm = reason => underlyingByteSource.cancel!(reason);\n  }\n\n  const autoAllocateChunkSize = underlyingByteSource.autoAllocateChunkSize;\n  if (autoAllocateChunkSize === 0) {\n    throw new TypeError('autoAllocateChunkSize must be greater than 0');\n  }\n\n  SetUpReadableByteStreamController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, autoAllocateChunkSize\n  );\n}\n\nfunction SetUpReadableStreamBYOBRequest(request: ReadableStreamBYOBRequest,\n                                        controller: ReadableByteStreamController,\n                                        view: ArrayBufferView) {\n  assert(IsReadableByteStreamController(controller));\n  assert(typeof view === 'object');\n  assert(ArrayBuffer.isView(view));\n  assert(!IsDetachedBuffer(view.buffer));\n  request._associatedReadableByteStreamController = controller;\n  request._view = view;\n}\n\n// Helper functions for the ReadableStreamBYOBRequest.\n\nfunction byobRequestBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamBYOBRequest.prototype.${name} can only be used on a ReadableStreamBYOBRequest`);\n}\n\n// Helper functions for the ReadableByteStreamController.\n\nfunction byteStreamControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableByteStreamController.prototype.${name} can only be used on a ReadableByteStreamController`);\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport {\n  ReadableStreamReaderGenericCancel,\n  ReadableStreamReaderGenericInitialize,\n  ReadableStreamReaderGenericRelease,\n  readerLockException\n} from './generic-reader';\nimport { IsReadableStreamLocked, ReadableByteStream, ReadableStream } from '../readable-stream';\nimport {\n  IsReadableByteStreamController,\n  ReadableByteStreamController,\n  ReadableByteStreamControllerPullInto\n} from './byte-stream-controller';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport { newPromise, promiseRejectedWith } from '../helpers/webidl';\nimport { assertRequiredArgument } from '../validators/basic';\nimport { assertReadableStream } from '../validators/readable-stream';\nimport { IsDetachedBuffer } from '../abstract-ops/ecmascript';\n\n/**\n * A result returned by {@link ReadableStreamBYOBReader.read}.\n *\n * @public\n */\nexport type ReadableStreamBYOBReadResult<T extends ArrayBufferView> = {\n  done: false;\n  value: T;\n} | {\n  done: true;\n  value: T | undefined;\n};\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamBYOBReader(stream: ReadableByteStream): ReadableStreamBYOBReader {\n  return new ReadableStreamBYOBReader(stream);\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamAddReadIntoRequest<T extends ArrayBufferView>(stream: ReadableByteStream,\n                                                                            readIntoRequest: ReadIntoRequest<T>): void {\n  assert(IsReadableStreamBYOBReader(stream._reader));\n  assert(stream._state === 'readable' || stream._state === 'closed');\n\n  (stream._reader! as ReadableStreamBYOBReader)._readIntoRequests.push(readIntoRequest);\n}\n\nexport function ReadableStreamFulfillReadIntoRequest(stream: ReadableByteStream,\n                                                     chunk: ArrayBufferView,\n                                                     done: boolean) {\n  const reader = stream._reader as ReadableStreamBYOBReader;\n\n  assert(reader._readIntoRequests.length > 0);\n\n  const readIntoRequest = reader._readIntoRequests.shift()!;\n  if (done) {\n    readIntoRequest._closeSteps(chunk);\n  } else {\n    readIntoRequest._chunkSteps(chunk);\n  }\n}\n\nexport function ReadableStreamGetNumReadIntoRequests(stream: ReadableByteStream): number {\n  return (stream._reader as ReadableStreamBYOBReader)._readIntoRequests.length;\n}\n\nexport function ReadableStreamHasBYOBReader(stream: ReadableByteStream): boolean {\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return false;\n  }\n\n  if (!IsReadableStreamBYOBReader(reader)) {\n    return false;\n  }\n\n  return true;\n}\n\n// Readers\n\nexport interface ReadIntoRequest<T extends ArrayBufferView> {\n  _chunkSteps(chunk: T): void;\n\n  _closeSteps(chunk: T | undefined): void;\n\n  _errorSteps(e: any): void;\n}\n\n/**\n * A BYOB reader vended by a {@link ReadableStream}.\n *\n * @public\n */\nexport class ReadableStreamBYOBReader {\n  /** @internal */\n  _ownerReadableStream!: ReadableByteStream;\n  /** @internal */\n  _closedPromise!: Promise<undefined>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readIntoRequests: SimpleQueue<ReadIntoRequest<any>>;\n\n  constructor(stream: ReadableByteStream) {\n    assertRequiredArgument(stream, 1, 'ReadableStreamBYOBReader');\n    assertReadableStream(stream, 'First parameter');\n\n    if (IsReadableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive reading by another reader');\n    }\n\n    if (!IsReadableByteStreamController(stream._readableStreamController)) {\n      throw new TypeError('Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte ' +\n        'source');\n    }\n\n    ReadableStreamReaderGenericInitialize(this, stream);\n\n    this._readIntoRequests = new SimpleQueue();\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or\n   * the reader's lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<undefined> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('cancel'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('cancel'));\n    }\n\n    return ReadableStreamReaderGenericCancel(this, reason);\n  }\n\n  /**\n   * Attempts to reads bytes into view, and returns a promise resolved with the result.\n   *\n   * If reading a chunk causes the queue to become empty, more data will be pulled from the underlying source.\n   */\n  read<T extends ArrayBufferView>(view: T): Promise<ReadableStreamBYOBReadResult<T>> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('read'));\n    }\n\n    if (!ArrayBuffer.isView(view)) {\n      return promiseRejectedWith(new TypeError('view must be an array buffer view'));\n    }\n    if (view.byteLength === 0) {\n      return promiseRejectedWith(new TypeError('view must have non-zero byteLength'));\n    }\n    if (view.buffer.byteLength === 0) {\n      return promiseRejectedWith(new TypeError(`view's buffer must have non-zero byteLength`));\n    }\n    if (IsDetachedBuffer(view.buffer)) {\n      return promiseRejectedWith(new TypeError('view\\'s buffer has been detached'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('read from'));\n    }\n\n    let resolvePromise!: (result: ReadableStreamBYOBReadResult<T>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamBYOBReadResult<T>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readIntoRequest: ReadIntoRequest<T> = {\n      _chunkSteps: chunk => resolvePromise({ value: chunk, done: false }),\n      _closeSteps: chunk => resolvePromise({ value: chunk, done: true }),\n      _errorSteps: e => rejectPromise(e)\n    };\n    ReadableStreamBYOBReaderRead(this, view, readIntoRequest);\n    return promise;\n  }\n\n  /**\n   * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.\n   * If the associated stream is errored when the lock is released, the reader will appear errored in the same way\n   * from now on; otherwise, the reader will appear closed.\n   *\n   * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by\n   * the reader's {@link ReadableStreamBYOBReader.read | read()} method has not yet been settled. Attempting to\n   * do so will throw a `TypeError` and leave the reader locked to the stream.\n   */\n  releaseLock(): void {\n    if (!IsReadableStreamBYOBReader(this)) {\n      throw byobReaderBrandCheckException('releaseLock');\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return;\n    }\n\n    if (this._readIntoRequests.length > 0) {\n      throw new TypeError('Tried to release a reader lock when that reader has pending read() calls un-settled');\n    }\n\n    ReadableStreamReaderGenericRelease(this);\n  }\n}\n\nObject.defineProperties(ReadableStreamBYOBReader.prototype, {\n  cancel: { enumerable: true },\n  read: { enumerable: true },\n  releaseLock: { enumerable: true },\n  closed: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamBYOBReader.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamBYOBReader',\n    configurable: true\n  });\n}\n\n// Abstract operations for the readers.\n\nexport function IsReadableStreamBYOBReader(x: any): x is ReadableStreamBYOBReader {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readIntoRequests')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamBYOBReader;\n}\n\nexport function ReadableStreamBYOBReaderRead<T extends ArrayBufferView>(\n  reader: ReadableStreamBYOBReader,\n  view: T,\n  readIntoRequest: ReadIntoRequest<T>\n): void {\n  const stream = reader._ownerReadableStream;\n\n  assert(stream !== undefined);\n\n  stream._disturbed = true;\n\n  if (stream._state === 'errored') {\n    readIntoRequest._errorSteps(stream._storedError);\n  } else {\n    ReadableByteStreamControllerPullInto(\n      stream._readableStreamController as ReadableByteStreamController,\n      view,\n      readIntoRequest\n    );\n  }\n}\n\n// Helper functions for the ReadableStreamBYOBReader.\n\nfunction byobReaderBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamBYOBReader.prototype.${name} can only be used on a ReadableStreamBYOBReader`);\n}\n", "import { QueuingStrategy, QueuingStrategySizeCallback } from '../queuing-strategy';\nimport NumberIsNaN from '../../stub/number-isnan';\n\nexport function ExtractHighWaterMark(strategy: QueuingStrategy, defaultHWM: number): number {\n  const { highWaterMark } = strategy;\n\n  if (highWaterMark === undefined) {\n    return defaultHWM;\n  }\n\n  if (NumberIsNaN(highWaterMark) || highWaterMark < 0) {\n    throw new RangeError('Invalid highWaterMark');\n  }\n\n  return highWaterMark;\n}\n\nexport function ExtractSizeAlgorithm<T>(strategy: QueuingStrategy<T>): QueuingStrategySizeCallback<T> {\n  const { size } = strategy;\n\n  if (!size) {\n    return () => 1;\n  }\n\n  return size;\n}\n", "import { QueuingStrategy, QueuingStrategySizeCallback } from '../queuing-strategy';\nimport { assertDictionary, assertFunction, convertUnrestrictedDouble } from './basic';\n\nexport function convertQueuingStrategy<T>(init: QueuingStrategy<T> | null | undefined,\n                                          context: string): QueuingStrategy<T> {\n  assertDictionary(init, context);\n  const highWaterMark = init?.highWaterMark;\n  const size = init?.size;\n  return {\n    highWaterMark: highWaterMark === undefined ? undefined : convertUnrestrictedDouble(highWaterMark),\n    size: size === undefined ? undefined : convertQueuingStrategySize(size, `${context} has member 'size' that`)\n  };\n}\n\nfunction convertQueuingStrategySize<T>(fn: QueuingStrategySizeCallback<T>,\n                                       context: string): QueuingStrategySizeCallback<T> {\n  assertFunction(fn, context);\n  return chunk => convertUnrestrictedDouble(fn(chunk));\n}\n", "import { assertDictionary, assertFunction } from './basic';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\nimport {\n  UnderlyingSink,\n  UnderlyingSinkAbortCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  ValidatedUnderlyingSink\n} from '../writable-stream/underlying-sink';\nimport { WritableStreamDefaultController } from '../writable-stream';\n\nexport function convertUnderlyingSink<W>(original: UnderlyingSink<W> | null,\n                                         context: string): ValidatedUnderlyingSink<W> {\n  assertDictionary(original, context);\n  const abort = original?.abort;\n  const close = original?.close;\n  const start = original?.start;\n  const type = original?.type;\n  const write = original?.write;\n  return {\n    abort: abort === undefined ?\n      undefined :\n      convertUnderlyingSinkAbortCallback(abort, original!, `${context} has member 'abort' that`),\n    close: close === undefined ?\n      undefined :\n      convertUnderlyingSinkCloseCallback(close, original!, `${context} has member 'close' that`),\n    start: start === undefined ?\n      undefined :\n      convertUnderlyingSinkStartCallback(start, original!, `${context} has member 'start' that`),\n    write: write === undefined ?\n      undefined :\n      convertUnderlyingSinkWriteCallback(write, original!, `${context} has member 'write' that`),\n    type\n  };\n}\n\nfunction convertUnderlyingSinkAbortCallback(\n  fn: UnderlyingSinkAbortCallback,\n  original: UnderlyingSink,\n  context: string\n): (reason: any) => Promise<void> {\n  assertFunction(fn, context);\n  return (reason: any) => promiseCall(fn, original, [reason]);\n}\n\nfunction convertUnderlyingSinkCloseCallback(\n  fn: UnderlyingSinkCloseCallback,\n  original: UnderlyingSink,\n  context: string\n): () => Promise<void> {\n  assertFunction(fn, context);\n  return () => promiseCall(fn, original, []);\n}\n\nfunction convertUnderlyingSinkStartCallback(\n  fn: UnderlyingSinkStartCallback,\n  original: UnderlyingSink,\n  context: string\n): UnderlyingSinkStartCallback {\n  assertFunction(fn, context);\n  return (controller: WritableStreamDefaultController) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertUnderlyingSinkWriteCallback<W>(\n  fn: UnderlyingSinkWriteCallback<W>,\n  original: UnderlyingSink<W>,\n  context: string\n): (chunk: W, controller: WritableStreamDefaultController) => Promise<void> {\n  assertFunction(fn, context);\n  return (chunk: W, controller: WritableStreamDefaultController) => promiseCall(fn, original, [chunk, controller]);\n}\n", "import { IsWritableStream, WritableStream } from '../writable-stream';\n\nexport function assertWritableStream(x: unknown, context: string): asserts x is WritableStream {\n  if (!IsWritableStream(x)) {\n    throw new TypeError(`${context} is not a WritableStream.`);\n  }\n}\n", "/**\n * A signal object that allows you to communicate with a request and abort it if required\n * via its associated `AbortController` object.\n *\n * @remarks\n *   This interface is compatible with the `AbortSignal` interface defined in TypeScript's DOM types.\n *   It is redefined here, so it can be polyfilled without a DOM, for example with\n *   {@link https://www.npmjs.com/package/abortcontroller-polyfill | abortcontroller-polyfill} in a Node environment.\n *\n * @public\n */\nexport interface AbortSignal {\n  /**\n   * Whether the request is aborted.\n   */\n  readonly aborted: boolean;\n\n  /**\n   * Add an event listener to be triggered when this signal becomes aborted.\n   */\n  addEventListener(type: 'abort', listener: () => void): void;\n\n  /**\n   * Remove an event listener that was previously added with {@link AbortSignal.addEventListener}.\n   */\n  removeEventListener(type: 'abort', listener: () => void): void;\n}\n\nexport function isAbortSignal(value: unknown): value is AbortSignal {\n  if (typeof value !== 'object' || value === null) {\n    return false;\n  }\n  try {\n    return typeof (value as AbortSignal).aborted === 'boolean';\n  } catch {\n    // AbortSignal.prototype.aborted throws if its brand check fails\n    return false;\n  }\n}\n\n/**\n * A controller object that allows you to abort an `AbortSignal` when desired.\n *\n * @remarks\n *   This interface is compatible with the `AbortController` interface defined in TypeScript's DOM types.\n *   It is redefined here, so it can be polyfilled without a DOM, for example with\n *   {@link https://www.npmjs.com/package/abortcontroller-polyfill | abortcontroller-polyfill} in a Node environment.\n *\n * @internal\n */\nexport interface AbortController {\n  readonly signal: AbortSignal;\n\n  abort(): void;\n}\n\ninterface AbortControllerConstructor {\n  new(): AbortController;\n}\n\nconst supportsAbortController = typeof (AbortController as any) === 'function';\n\n/**\n * Construct a new AbortController, if supported by the platform.\n *\n * @internal\n */\nexport function createAbortController(): AbortController | undefined {\n  if (supportsAbortController) {\n    return new (AbortController as AbortControllerConstructor)();\n  }\n  return undefined;\n}\n", "import assert from '../stub/assert';\nimport {\n  newPromise,\n  promiseRejectedWith,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  uponPromise\n} from './helpers/webidl';\nimport {\n  DequeueValue,\n  EnqueueValueWithSize,\n  PeekQueueValue,\n  QueuePair,\n  ResetQueue\n} from './abstract-ops/queue-with-sizes';\nimport { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { SimpleQueue } from './simple-queue';\nimport { typeIsObject } from './helpers/miscellaneous';\nimport { AbortSteps, ErrorSteps } from './abstract-ops/internal-methods';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport {\n  UnderlyingSink,\n  UnderlyingSinkAbortCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  ValidatedUnderlyingSink\n} from './writable-stream/underlying-sink';\nimport { assertObject, assertRequiredArgument } from './validators/basic';\nimport { convertUnderlyingSink } from './validators/underlying-sink';\nimport { assertWritableStream } from './validators/writable-stream';\nimport { AbortController, AbortSignal, createAbortController } from './abort-signal';\n\ntype WritableStreamState = 'writable' | 'closed' | 'erroring' | 'errored';\n\ninterface WriteOrCloseRequest {\n  _resolve: (value?: undefined) => void;\n  _reject: (reason: any) => void;\n}\n\ntype WriteRequest = WriteOrCloseRequest;\ntype CloseRequest = WriteOrCloseRequest;\n\ninterface PendingAbortRequest {\n  _promise: Promise<undefined>;\n  _resolve: (value?: undefined) => void;\n  _reject: (reason: any) => void;\n  _reason: any;\n  _wasAlreadyErroring: boolean;\n}\n\n/**\n * A writable stream represents a destination for data, into which you can write.\n *\n * @public\n */\nclass WritableStream<W = any> {\n  /** @internal */\n  _state!: WritableStreamState;\n  /** @internal */\n  _storedError: any;\n  /** @internal */\n  _writer: WritableStreamDefaultWriter<W> | undefined;\n  /** @internal */\n  _writableStreamController!: WritableStreamDefaultController<W>;\n  /** @internal */\n  _writeRequests!: SimpleQueue<WriteRequest>;\n  /** @internal */\n  _inFlightWriteRequest: WriteRequest | undefined;\n  /** @internal */\n  _closeRequest: CloseRequest | undefined;\n  /** @internal */\n  _inFlightCloseRequest: CloseRequest | undefined;\n  /** @internal */\n  _pendingAbortRequest: PendingAbortRequest | undefined;\n  /** @internal */\n  _backpressure!: boolean;\n\n  constructor(underlyingSink?: UnderlyingSink<W>, strategy?: QueuingStrategy<W>);\n  constructor(rawUnderlyingSink: UnderlyingSink<W> | null | undefined = {},\n              rawStrategy: QueuingStrategy<W> | null | undefined = {}) {\n    if (rawUnderlyingSink === undefined) {\n      rawUnderlyingSink = null;\n    } else {\n      assertObject(rawUnderlyingSink, 'First parameter');\n    }\n\n    const strategy = convertQueuingStrategy(rawStrategy, 'Second parameter');\n    const underlyingSink = convertUnderlyingSink(rawUnderlyingSink, 'First parameter');\n\n    InitializeWritableStream(this);\n\n    const type = underlyingSink.type;\n    if (type !== undefined) {\n      throw new RangeError('Invalid type is specified');\n    }\n\n    const sizeAlgorithm = ExtractSizeAlgorithm(strategy);\n    const highWaterMark = ExtractHighWaterMark(strategy, 1);\n\n    SetUpWritableStreamDefaultControllerFromUnderlyingSink(this, underlyingSink, highWaterMark, sizeAlgorithm);\n  }\n\n  /**\n   * Returns whether or not the writable stream is locked to a writer.\n   */\n  get locked(): boolean {\n    if (!IsWritableStream(this)) {\n      throw streamBrandCheckException('locked');\n    }\n\n    return IsWritableStreamLocked(this);\n  }\n\n  /**\n   * Aborts the stream, signaling that the producer can no longer successfully write to the stream and it is to be\n   * immediately moved to an errored state, with any queued-up writes discarded. This will also execute any abort\n   * mechanism of the underlying sink.\n   *\n   * The returned promise will fulfill if the stream shuts down successfully, or reject if the underlying sink signaled\n   * that there was an error doing so. Additionally, it will reject with a `TypeError` (without attempting to cancel\n   * the stream) if the stream is currently locked.\n   */\n  abort(reason: any = undefined): Promise<void> {\n    if (!IsWritableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('abort'));\n    }\n\n    if (IsWritableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot abort a stream that already has a writer'));\n    }\n\n    return WritableStreamAbort(this, reason);\n  }\n\n  /**\n   * Closes the stream. The underlying sink will finish processing any previously-written chunks, before invoking its\n   * close behavior. During this time any further attempts to write will fail (without erroring the stream).\n   *\n   * The method returns a promise that will fulfill if all remaining chunks are successfully written and the stream\n   * successfully closes, or rejects if an error is encountered during this process. Additionally, it will reject with\n   * a `TypeError` (without attempting to cancel the stream) if the stream is currently locked.\n   */\n  close() {\n    if (!IsWritableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('close'));\n    }\n\n    if (IsWritableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot close a stream that already has a writer'));\n    }\n\n    if (WritableStreamCloseQueuedOrInFlight(this)) {\n      return promiseRejectedWith(new TypeError('Cannot close an already-closing stream'));\n    }\n\n    return WritableStreamClose(this);\n  }\n\n  /**\n   * Creates a {@link WritableStreamDefaultWriter | writer} and locks the stream to the new writer. While the stream\n   * is locked, no other writer can be acquired until this one is released.\n   *\n   * This functionality is especially useful for creating abstractions that desire the ability to write to a stream\n   * without interruption or interleaving. By getting a writer for the stream, you can ensure nobody else can write at\n   * the same time, which would cause the resulting written data to be unpredictable and probably useless.\n   */\n  getWriter(): WritableStreamDefaultWriter<W> {\n    if (!IsWritableStream(this)) {\n      throw streamBrandCheckException('getWriter');\n    }\n\n    return AcquireWritableStreamDefaultWriter(this);\n  }\n}\n\nObject.defineProperties(WritableStream.prototype, {\n  abort: { enumerable: true },\n  close: { enumerable: true },\n  getWriter: { enumerable: true },\n  locked: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStream.prototype, Symbol.toStringTag, {\n    value: 'WritableStream',\n    configurable: true\n  });\n}\n\nexport {\n  AcquireWritableStreamDefaultWriter,\n  CreateWritableStream,\n  IsWritableStream,\n  IsWritableStreamLocked,\n  WritableStream,\n  WritableStreamAbort,\n  WritableStreamDefaultControllerErrorIfNeeded,\n  WritableStreamDefaultWriterCloseWithErrorPropagation,\n  WritableStreamDefaultWriterRelease,\n  WritableStreamDefaultWriterWrite,\n  WritableStreamCloseQueuedOrInFlight,\n  UnderlyingSink,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkAbortCallback\n};\n\n// Abstract operations for the WritableStream.\n\nfunction AcquireWritableStreamDefaultWriter<W>(stream: WritableStream<W>): WritableStreamDefaultWriter<W> {\n  return new WritableStreamDefaultWriter(stream);\n}\n\n// Throws if and only if startAlgorithm throws.\nfunction CreateWritableStream<W>(startAlgorithm: () => void | PromiseLike<void>,\n                                 writeAlgorithm: (chunk: W) => Promise<void>,\n                                 closeAlgorithm: () => Promise<void>,\n                                 abortAlgorithm: (reason: any) => Promise<void>,\n                                 highWaterMark = 1,\n                                 sizeAlgorithm: QueuingStrategySizeCallback<W> = () => 1) {\n  assert(IsNonNegativeNumber(highWaterMark));\n\n  const stream: WritableStream<W> = Object.create(WritableStream.prototype);\n  InitializeWritableStream(stream);\n\n  const controller: WritableStreamDefaultController<W> = Object.create(WritableStreamDefaultController.prototype);\n\n  SetUpWritableStreamDefaultController(stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm,\n                                       abortAlgorithm, highWaterMark, sizeAlgorithm);\n  return stream;\n}\n\nfunction InitializeWritableStream<W>(stream: WritableStream<W>) {\n  stream._state = 'writable';\n\n  // The error that will be reported by new method calls once the state becomes errored. Only set when [[state]] is\n  // 'erroring' or 'errored'. May be set to an undefined value.\n  stream._storedError = undefined;\n\n  stream._writer = undefined;\n\n  // Initialize to undefined first because the constructor of the controller checks this\n  // variable to validate the caller.\n  stream._writableStreamController = undefined!;\n\n  // This queue is placed here instead of the writer class in order to allow for passing a writer to the next data\n  // producer without waiting for the queued writes to finish.\n  stream._writeRequests = new SimpleQueue();\n\n  // Write requests are removed from _writeRequests when write() is called on the underlying sink. This prevents\n  // them from being erroneously rejected on error. If a write() call is in-flight, the request is stored here.\n  stream._inFlightWriteRequest = undefined;\n\n  // The promise that was returned from writer.close(). Stored here because it may be fulfilled after the writer\n  // has been detached.\n  stream._closeRequest = undefined;\n\n  // Close request is removed from _closeRequest when close() is called on the underlying sink. This prevents it\n  // from being erroneously rejected on error. If a close() call is in-flight, the request is stored here.\n  stream._inFlightCloseRequest = undefined;\n\n  // The promise that was returned from writer.abort(). This may also be fulfilled after the writer has detached.\n  stream._pendingAbortRequest = undefined;\n\n  // The backpressure signal set by the controller.\n  stream._backpressure = false;\n}\n\nfunction IsWritableStream(x: unknown): x is WritableStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_writableStreamController')) {\n    return false;\n  }\n\n  return x instanceof WritableStream;\n}\n\nfunction IsWritableStreamLocked(stream: WritableStream): boolean {\n  assert(IsWritableStream(stream));\n\n  if (stream._writer === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamAbort(stream: WritableStream, reason: any): Promise<undefined> {\n  if (stream._state === 'closed' || stream._state === 'errored') {\n    return promiseResolvedWith(undefined);\n  }\n  stream._writableStreamController._abortReason = reason;\n  stream._writableStreamController._abortController?.abort();\n\n  // TypeScript narrows the type of `stream._state` down to 'writable' | 'erroring',\n  // but it doesn't know that signaling abort runs author code that might have changed the state.\n  // Widen the type again by casting to WritableStreamState.\n  const state = stream._state as WritableStreamState;\n\n  if (state === 'closed' || state === 'errored') {\n    return promiseResolvedWith(undefined);\n  }\n  if (stream._pendingAbortRequest !== undefined) {\n    return stream._pendingAbortRequest._promise;\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n\n  let wasAlreadyErroring = false;\n  if (state === 'erroring') {\n    wasAlreadyErroring = true;\n    // reason will not be used, so don't keep a reference to it.\n    reason = undefined;\n  }\n\n  const promise = newPromise<undefined>((resolve, reject) => {\n    stream._pendingAbortRequest = {\n      _promise: undefined!,\n      _resolve: resolve,\n      _reject: reject,\n      _reason: reason,\n      _wasAlreadyErroring: wasAlreadyErroring\n    };\n  });\n  stream._pendingAbortRequest!._promise = promise;\n\n  if (!wasAlreadyErroring) {\n    WritableStreamStartErroring(stream, reason);\n  }\n\n  return promise;\n}\n\nfunction WritableStreamClose(stream: WritableStream<any>): Promise<undefined> {\n  const state = stream._state;\n  if (state === 'closed' || state === 'errored') {\n    return promiseRejectedWith(new TypeError(\n      `The stream (in ${state} state) is not in the writable state and cannot be closed`));\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n  assert(!WritableStreamCloseQueuedOrInFlight(stream));\n\n  const promise = newPromise<undefined>((resolve, reject) => {\n    const closeRequest: CloseRequest = {\n      _resolve: resolve,\n      _reject: reject\n    };\n\n    stream._closeRequest = closeRequest;\n  });\n\n  const writer = stream._writer;\n  if (writer !== undefined && stream._backpressure && state === 'writable') {\n    defaultWriterReadyPromiseResolve(writer);\n  }\n\n  WritableStreamDefaultControllerClose(stream._writableStreamController);\n\n  return promise;\n}\n\n// WritableStream API exposed for controllers.\n\nfunction WritableStreamAddWriteRequest(stream: WritableStream): Promise<undefined> {\n  assert(IsWritableStreamLocked(stream));\n  assert(stream._state === 'writable');\n\n  const promise = newPromise<undefined>((resolve, reject) => {\n    const writeRequest: WriteRequest = {\n      _resolve: resolve,\n      _reject: reject\n    };\n\n    stream._writeRequests.push(writeRequest);\n  });\n\n  return promise;\n}\n\nfunction WritableStreamDealWithRejection(stream: WritableStream, error: any) {\n  const state = stream._state;\n\n  if (state === 'writable') {\n    WritableStreamStartErroring(stream, error);\n    return;\n  }\n\n  assert(state === 'erroring');\n  WritableStreamFinishErroring(stream);\n}\n\nfunction WritableStreamStartErroring(stream: WritableStream, reason: any) {\n  assert(stream._storedError === undefined);\n  assert(stream._state === 'writable');\n\n  const controller = stream._writableStreamController;\n  assert(controller !== undefined);\n\n  stream._state = 'erroring';\n  stream._storedError = reason;\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, reason);\n  }\n\n  if (!WritableStreamHasOperationMarkedInFlight(stream) && controller._started) {\n    WritableStreamFinishErroring(stream);\n  }\n}\n\nfunction WritableStreamFinishErroring(stream: WritableStream) {\n  assert(stream._state === 'erroring');\n  assert(!WritableStreamHasOperationMarkedInFlight(stream));\n  stream._state = 'errored';\n  stream._writableStreamController[ErrorSteps]();\n\n  const storedError = stream._storedError;\n  stream._writeRequests.forEach(writeRequest => {\n    writeRequest._reject(storedError);\n  });\n  stream._writeRequests = new SimpleQueue();\n\n  if (stream._pendingAbortRequest === undefined) {\n    WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    return;\n  }\n\n  const abortRequest = stream._pendingAbortRequest;\n  stream._pendingAbortRequest = undefined;\n\n  if (abortRequest._wasAlreadyErroring) {\n    abortRequest._reject(storedError);\n    WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    return;\n  }\n\n  const promise = stream._writableStreamController[AbortSteps](abortRequest._reason);\n  uponPromise(\n    promise,\n    () => {\n      abortRequest._resolve();\n      WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    },\n    (reason: any) => {\n      abortRequest._reject(reason);\n      WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    });\n}\n\nfunction WritableStreamFinishInFlightWrite(stream: WritableStream) {\n  assert(stream._inFlightWriteRequest !== undefined);\n  stream._inFlightWriteRequest!._resolve(undefined);\n  stream._inFlightWriteRequest = undefined;\n}\n\nfunction WritableStreamFinishInFlightWriteWithError(stream: WritableStream, error: any) {\n  assert(stream._inFlightWriteRequest !== undefined);\n  stream._inFlightWriteRequest!._reject(error);\n  stream._inFlightWriteRequest = undefined;\n\n  assert(stream._state === 'writable' || stream._state === 'erroring');\n\n  WritableStreamDealWithRejection(stream, error);\n}\n\nfunction WritableStreamFinishInFlightClose(stream: WritableStream) {\n  assert(stream._inFlightCloseRequest !== undefined);\n  stream._inFlightCloseRequest!._resolve(undefined);\n  stream._inFlightCloseRequest = undefined;\n\n  const state = stream._state;\n\n  assert(state === 'writable' || state === 'erroring');\n\n  if (state === 'erroring') {\n    // The error was too late to do anything, so it is ignored.\n    stream._storedError = undefined;\n    if (stream._pendingAbortRequest !== undefined) {\n      stream._pendingAbortRequest._resolve();\n      stream._pendingAbortRequest = undefined;\n    }\n  }\n\n  stream._state = 'closed';\n\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    defaultWriterClosedPromiseResolve(writer);\n  }\n\n  assert(stream._pendingAbortRequest === undefined);\n  assert(stream._storedError === undefined);\n}\n\nfunction WritableStreamFinishInFlightCloseWithError(stream: WritableStream, error: any) {\n  assert(stream._inFlightCloseRequest !== undefined);\n  stream._inFlightCloseRequest!._reject(error);\n  stream._inFlightCloseRequest = undefined;\n\n  assert(stream._state === 'writable' || stream._state === 'erroring');\n\n  // Never execute sink abort() after sink close().\n  if (stream._pendingAbortRequest !== undefined) {\n    stream._pendingAbortRequest._reject(error);\n    stream._pendingAbortRequest = undefined;\n  }\n  WritableStreamDealWithRejection(stream, error);\n}\n\n// TODO(ricea): Fix alphabetical order.\nfunction WritableStreamCloseQueuedOrInFlight(stream: WritableStream): boolean {\n  if (stream._closeRequest === undefined && stream._inFlightCloseRequest === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamHasOperationMarkedInFlight(stream: WritableStream): boolean {\n  if (stream._inFlightWriteRequest === undefined && stream._inFlightCloseRequest === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamMarkCloseRequestInFlight(stream: WritableStream) {\n  assert(stream._inFlightCloseRequest === undefined);\n  assert(stream._closeRequest !== undefined);\n  stream._inFlightCloseRequest = stream._closeRequest;\n  stream._closeRequest = undefined;\n}\n\nfunction WritableStreamMarkFirstWriteRequestInFlight(stream: WritableStream) {\n  assert(stream._inFlightWriteRequest === undefined);\n  assert(stream._writeRequests.length !== 0);\n  stream._inFlightWriteRequest = stream._writeRequests.shift();\n}\n\nfunction WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream: WritableStream) {\n  assert(stream._state === 'errored');\n  if (stream._closeRequest !== undefined) {\n    assert(stream._inFlightCloseRequest === undefined);\n\n    stream._closeRequest._reject(stream._storedError);\n    stream._closeRequest = undefined;\n  }\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    defaultWriterClosedPromiseReject(writer, stream._storedError);\n  }\n}\n\nfunction WritableStreamUpdateBackpressure(stream: WritableStream, backpressure: boolean) {\n  assert(stream._state === 'writable');\n  assert(!WritableStreamCloseQueuedOrInFlight(stream));\n\n  const writer = stream._writer;\n  if (writer !== undefined && backpressure !== stream._backpressure) {\n    if (backpressure) {\n      defaultWriterReadyPromiseReset(writer);\n    } else {\n      assert(!backpressure);\n\n      defaultWriterReadyPromiseResolve(writer);\n    }\n  }\n\n  stream._backpressure = backpressure;\n}\n\n/**\n * A default writer vended by a {@link WritableStream}.\n *\n * @public\n */\nexport class WritableStreamDefaultWriter<W = any> {\n  /** @internal */\n  _ownerWritableStream: WritableStream<W>;\n  /** @internal */\n  _closedPromise!: Promise<undefined>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _closedPromiseState!: 'pending' | 'resolved' | 'rejected';\n  /** @internal */\n  _readyPromise!: Promise<undefined>;\n  /** @internal */\n  _readyPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _readyPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readyPromiseState!: 'pending' | 'fulfilled' | 'rejected';\n\n  constructor(stream: WritableStream<W>) {\n    assertRequiredArgument(stream, 1, 'WritableStreamDefaultWriter');\n    assertWritableStream(stream, 'First parameter');\n\n    if (IsWritableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive writing by another writer');\n    }\n\n    this._ownerWritableStream = stream;\n    stream._writer = this;\n\n    const state = stream._state;\n\n    if (state === 'writable') {\n      if (!WritableStreamCloseQueuedOrInFlight(stream) && stream._backpressure) {\n        defaultWriterReadyPromiseInitialize(this);\n      } else {\n        defaultWriterReadyPromiseInitializeAsResolved(this);\n      }\n\n      defaultWriterClosedPromiseInitialize(this);\n    } else if (state === 'erroring') {\n      defaultWriterReadyPromiseInitializeAsRejected(this, stream._storedError);\n      defaultWriterClosedPromiseInitialize(this);\n    } else if (state === 'closed') {\n      defaultWriterReadyPromiseInitializeAsResolved(this);\n      defaultWriterClosedPromiseInitializeAsResolved(this);\n    } else {\n      assert(state === 'errored');\n\n      const storedError = stream._storedError;\n      defaultWriterReadyPromiseInitializeAsRejected(this, storedError);\n      defaultWriterClosedPromiseInitializeAsRejected(this, storedError);\n    }\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or\n   * the writer’s lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<undefined> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * Returns the desired size to fill the stream’s internal queue. It can be negative, if the queue is over-full.\n   * A producer can use this information to determine the right amount of data to write.\n   *\n   * It will be `null` if the stream cannot be successfully written to (due to either being errored, or having an abort\n   * queued up). It will return zero if the stream is closed. And the getter will throw an exception if invoked when\n   * the writer’s lock is released.\n   */\n  get desiredSize(): number | null {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      throw defaultWriterBrandCheckException('desiredSize');\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      throw defaultWriterLockException('desiredSize');\n    }\n\n    return WritableStreamDefaultWriterGetDesiredSize(this);\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the desired size to fill the stream’s internal queue transitions\n   * from non-positive to positive, signaling that it is no longer applying backpressure. Once the desired size dips\n   * back to zero or below, the getter will return a new promise that stays pending until the next transition.\n   *\n   * If the stream becomes errored or aborted, or the writer’s lock is released, the returned promise will become\n   * rejected.\n   */\n  get ready(): Promise<undefined> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('ready'));\n    }\n\n    return this._readyPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link WritableStream.abort | stream.abort(reason)}.\n   */\n  abort(reason: any = undefined): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('abort'));\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('abort'));\n    }\n\n    return WritableStreamDefaultWriterAbort(this, reason);\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link WritableStream.close | stream.close()}.\n   */\n  close(): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('close'));\n    }\n\n    const stream = this._ownerWritableStream;\n\n    if (stream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('close'));\n    }\n\n    if (WritableStreamCloseQueuedOrInFlight(stream)) {\n      return promiseRejectedWith(new TypeError('Cannot close an already-closing stream'));\n    }\n\n    return WritableStreamDefaultWriterClose(this);\n  }\n\n  /**\n   * Releases the writer’s lock on the corresponding stream. After the lock is released, the writer is no longer active.\n   * If the associated stream is errored when the lock is released, the writer will appear errored in the same way from\n   * now on; otherwise, the writer will appear closed.\n   *\n   * Note that the lock can still be released even if some ongoing writes have not yet finished (i.e. even if the\n   * promises returned from previous calls to {@link WritableStreamDefaultWriter.write | write()} have not yet settled).\n   * It’s not necessary to hold the lock on the writer for the duration of the write; the lock instead simply prevents\n   * other producers from writing in an interleaved manner.\n   */\n  releaseLock(): void {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      throw defaultWriterBrandCheckException('releaseLock');\n    }\n\n    const stream = this._ownerWritableStream;\n\n    if (stream === undefined) {\n      return;\n    }\n\n    assert(stream._writer !== undefined);\n\n    WritableStreamDefaultWriterRelease(this);\n  }\n\n  /**\n   * Writes the given chunk to the writable stream, by waiting until any previous writes have finished successfully,\n   * and then sending the chunk to the underlying sink's {@link UnderlyingSink.write | write()} method. It will return\n   * a promise that fulfills with undefined upon a successful write, or rejects if the write fails or stream becomes\n   * errored before the writing process is initiated.\n   *\n   * Note that what \"success\" means is up to the underlying sink; it might indicate simply that the chunk has been\n   * accepted, and not necessarily that it is safely saved to its ultimate destination.\n   */\n  write(chunk: W): Promise<void>;\n  write(chunk: W = undefined!): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('write'));\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('write to'));\n    }\n\n    return WritableStreamDefaultWriterWrite(this, chunk);\n  }\n}\n\nObject.defineProperties(WritableStreamDefaultWriter.prototype, {\n  abort: { enumerable: true },\n  close: { enumerable: true },\n  releaseLock: { enumerable: true },\n  write: { enumerable: true },\n  closed: { enumerable: true },\n  desiredSize: { enumerable: true },\n  ready: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStreamDefaultWriter.prototype, Symbol.toStringTag, {\n    value: 'WritableStreamDefaultWriter',\n    configurable: true\n  });\n}\n\n// Abstract operations for the WritableStreamDefaultWriter.\n\nfunction IsWritableStreamDefaultWriter<W = any>(x: any): x is WritableStreamDefaultWriter<W> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_ownerWritableStream')) {\n    return false;\n  }\n\n  return x instanceof WritableStreamDefaultWriter;\n}\n\n// A client of WritableStreamDefaultWriter may use these functions directly to bypass state check.\n\nfunction WritableStreamDefaultWriterAbort(writer: WritableStreamDefaultWriter, reason: any) {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  return WritableStreamAbort(stream, reason);\n}\n\nfunction WritableStreamDefaultWriterClose(writer: WritableStreamDefaultWriter): Promise<undefined> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  return WritableStreamClose(stream);\n}\n\nfunction WritableStreamDefaultWriterCloseWithErrorPropagation(writer: WritableStreamDefaultWriter): Promise<undefined> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  const state = stream._state;\n  if (WritableStreamCloseQueuedOrInFlight(stream) || state === 'closed') {\n    return promiseResolvedWith(undefined);\n  }\n\n  if (state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n\n  return WritableStreamDefaultWriterClose(writer);\n}\n\nfunction WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer: WritableStreamDefaultWriter, error: any) {\n  if (writer._closedPromiseState === 'pending') {\n    defaultWriterClosedPromiseReject(writer, error);\n  } else {\n    defaultWriterClosedPromiseResetToRejected(writer, error);\n  }\n}\n\nfunction WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer: WritableStreamDefaultWriter, error: any) {\n  if (writer._readyPromiseState === 'pending') {\n    defaultWriterReadyPromiseReject(writer, error);\n  } else {\n    defaultWriterReadyPromiseResetToRejected(writer, error);\n  }\n}\n\nfunction WritableStreamDefaultWriterGetDesiredSize(writer: WritableStreamDefaultWriter): number | null {\n  const stream = writer._ownerWritableStream;\n  const state = stream._state;\n\n  if (state === 'errored' || state === 'erroring') {\n    return null;\n  }\n\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return WritableStreamDefaultControllerGetDesiredSize(stream._writableStreamController);\n}\n\nfunction WritableStreamDefaultWriterRelease(writer: WritableStreamDefaultWriter) {\n  const stream = writer._ownerWritableStream;\n  assert(stream !== undefined);\n  assert(stream._writer === writer);\n\n  const releasedError = new TypeError(\n    `Writer was released and can no longer be used to monitor the stream's closedness`);\n\n  WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, releasedError);\n\n  // The state transitions to \"errored\" before the sink abort() method runs, but the writer.closed promise is not\n  // rejected until afterwards. This means that simply testing state will not work.\n  WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer, releasedError);\n\n  stream._writer = undefined;\n  writer._ownerWritableStream = undefined!;\n}\n\nfunction WritableStreamDefaultWriterWrite<W>(writer: WritableStreamDefaultWriter<W>, chunk: W): Promise<undefined> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  const controller = stream._writableStreamController;\n\n  const chunkSize = WritableStreamDefaultControllerGetChunkSize(controller, chunk);\n\n  if (stream !== writer._ownerWritableStream) {\n    return promiseRejectedWith(defaultWriterLockException('write to'));\n  }\n\n  const state = stream._state;\n  if (state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n  if (WritableStreamCloseQueuedOrInFlight(stream) || state === 'closed') {\n    return promiseRejectedWith(new TypeError('The stream is closing or closed and cannot be written to'));\n  }\n  if (state === 'erroring') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  assert(state === 'writable');\n\n  const promise = WritableStreamAddWriteRequest(stream);\n\n  WritableStreamDefaultControllerWrite(controller, chunk, chunkSize);\n\n  return promise;\n}\n\nconst closeSentinel: unique symbol = {} as any;\n\ntype QueueRecord<W> = W | typeof closeSentinel;\n\n/**\n * Allows control of a {@link WritableStream | writable stream}'s state and internal queue.\n *\n * @public\n */\nexport class WritableStreamDefaultController<W = any> {\n  /** @internal */\n  _controlledWritableStream!: WritableStream<W>;\n  /** @internal */\n  _queue!: SimpleQueue<QueuePair<QueueRecord<W>>>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _abortReason: any;\n  /** @internal */\n  _abortController: AbortController | undefined;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _strategySizeAlgorithm!: QueuingStrategySizeCallback<W>;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _writeAlgorithm!: (chunk: W) => Promise<void>;\n  /** @internal */\n  _closeAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _abortAlgorithm!: (reason: any) => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * The reason which was passed to `WritableStream.abort(reason)` when the stream was aborted.\n   *\n   * @deprecated\n   *  This property has been removed from the specification, see https://github.com/whatwg/streams/pull/1177.\n   *  Use {@link WritableStreamDefaultController.signal}'s `reason` instead.\n   */\n  get abortReason(): any {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('abortReason');\n    }\n    return this._abortReason;\n  }\n\n  /**\n   * An `AbortSignal` that can be used to abort the pending write or close operation when the stream is aborted.\n   */\n  get signal(): AbortSignal {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('signal');\n    }\n    if (this._abortController === undefined) {\n      // Older browsers or older Node versions may not support `AbortController` or `AbortSignal`.\n      // We don't want to bundle and ship an `AbortController` polyfill together with our polyfill,\n      // so instead we only implement support for `signal` if we find a global `AbortController` constructor.\n      throw new TypeError('WritableStreamDefaultController.prototype.signal is not supported');\n    }\n    return this._abortController.signal;\n  }\n\n  /**\n   * Closes the controlled writable stream, making all future interactions with it fail with the given error `e`.\n   *\n   * This method is rarely used, since usually it suffices to return a rejected promise from one of the underlying\n   * sink's methods. However, it can be useful for suddenly shutting down a stream in response to an event outside the\n   * normal lifecycle of interactions with the underlying sink.\n   */\n  error(e: any = undefined): void {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n    const state = this._controlledWritableStream._state;\n    if (state !== 'writable') {\n      // The stream is closed, errored or will be soon. The sink can't do anything useful if it gets an error here, so\n      // just treat it as a no-op.\n      return;\n    }\n\n    WritableStreamDefaultControllerError(this, e);\n  }\n\n  /** @internal */\n  [AbortSteps](reason: any): Promise<void> {\n    const result = this._abortAlgorithm(reason);\n    WritableStreamDefaultControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [ErrorSteps]() {\n    ResetQueue(this);\n  }\n}\n\nObject.defineProperties(WritableStreamDefaultController.prototype, {\n  abortReason: { enumerable: true },\n  signal: { enumerable: true },\n  error: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'WritableStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Abstract operations implementing interface required by the WritableStream.\n\nfunction IsWritableStreamDefaultController(x: any): x is WritableStreamDefaultController<any> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledWritableStream')) {\n    return false;\n  }\n\n  return x instanceof WritableStreamDefaultController;\n}\n\nfunction SetUpWritableStreamDefaultController<W>(stream: WritableStream<W>,\n                                                 controller: WritableStreamDefaultController<W>,\n                                                 startAlgorithm: () => void | PromiseLike<void>,\n                                                 writeAlgorithm: (chunk: W) => Promise<void>,\n                                                 closeAlgorithm: () => Promise<void>,\n                                                 abortAlgorithm: (reason: any) => Promise<void>,\n                                                 highWaterMark: number,\n                                                 sizeAlgorithm: QueuingStrategySizeCallback<W>) {\n  assert(IsWritableStream(stream));\n  assert(stream._writableStreamController === undefined);\n\n  controller._controlledWritableStream = stream;\n  stream._writableStreamController = controller;\n\n  // Need to set the slots so that the assert doesn't fire. In the spec the slots already exist implicitly.\n  controller._queue = undefined!;\n  controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._abortReason = undefined;\n  controller._abortController = createAbortController();\n  controller._started = false;\n\n  controller._strategySizeAlgorithm = sizeAlgorithm;\n  controller._strategyHWM = highWaterMark;\n\n  controller._writeAlgorithm = writeAlgorithm;\n  controller._closeAlgorithm = closeAlgorithm;\n  controller._abortAlgorithm = abortAlgorithm;\n\n  const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n  WritableStreamUpdateBackpressure(stream, backpressure);\n\n  const startResult = startAlgorithm();\n  const startPromise = promiseResolvedWith(startResult);\n  uponPromise(\n    startPromise,\n    () => {\n      assert(stream._state === 'writable' || stream._state === 'erroring');\n      controller._started = true;\n      WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n    },\n    r => {\n      assert(stream._state === 'writable' || stream._state === 'erroring');\n      controller._started = true;\n      WritableStreamDealWithRejection(stream, r);\n    }\n  );\n}\n\nfunction SetUpWritableStreamDefaultControllerFromUnderlyingSink<W>(stream: WritableStream<W>,\n                                                                   underlyingSink: ValidatedUnderlyingSink<W>,\n                                                                   highWaterMark: number,\n                                                                   sizeAlgorithm: QueuingStrategySizeCallback<W>) {\n  const controller = Object.create(WritableStreamDefaultController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void> = () => undefined;\n  let writeAlgorithm: (chunk: W) => Promise<void> = () => promiseResolvedWith(undefined);\n  let closeAlgorithm: () => Promise<void> = () => promiseResolvedWith(undefined);\n  let abortAlgorithm: (reason: any) => Promise<void> = () => promiseResolvedWith(undefined);\n\n  if (underlyingSink.start !== undefined) {\n    startAlgorithm = () => underlyingSink.start!(controller);\n  }\n  if (underlyingSink.write !== undefined) {\n    writeAlgorithm = chunk => underlyingSink.write!(chunk, controller);\n  }\n  if (underlyingSink.close !== undefined) {\n    closeAlgorithm = () => underlyingSink.close!();\n  }\n  if (underlyingSink.abort !== undefined) {\n    abortAlgorithm = reason => underlyingSink.abort!(reason);\n  }\n\n  SetUpWritableStreamDefaultController(\n    stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, highWaterMark, sizeAlgorithm\n  );\n}\n\n// ClearAlgorithms may be called twice. Erroring the same stream in multiple ways will often result in redundant calls.\nfunction WritableStreamDefaultControllerClearAlgorithms(controller: WritableStreamDefaultController<any>) {\n  controller._writeAlgorithm = undefined!;\n  controller._closeAlgorithm = undefined!;\n  controller._abortAlgorithm = undefined!;\n  controller._strategySizeAlgorithm = undefined!;\n}\n\nfunction WritableStreamDefaultControllerClose<W>(controller: WritableStreamDefaultController<W>) {\n  EnqueueValueWithSize(controller, closeSentinel, 0);\n  WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n}\n\nfunction WritableStreamDefaultControllerGetChunkSize<W>(controller: WritableStreamDefaultController<W>,\n                                                        chunk: W): number {\n  try {\n    return controller._strategySizeAlgorithm(chunk);\n  } catch (chunkSizeE) {\n    WritableStreamDefaultControllerErrorIfNeeded(controller, chunkSizeE);\n    return 1;\n  }\n}\n\nfunction WritableStreamDefaultControllerGetDesiredSize(controller: WritableStreamDefaultController<any>): number {\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\nfunction WritableStreamDefaultControllerWrite<W>(controller: WritableStreamDefaultController<W>,\n                                                 chunk: W,\n                                                 chunkSize: number) {\n  try {\n    EnqueueValueWithSize(controller, chunk, chunkSize);\n  } catch (enqueueE) {\n    WritableStreamDefaultControllerErrorIfNeeded(controller, enqueueE);\n    return;\n  }\n\n  const stream = controller._controlledWritableStream;\n  if (!WritableStreamCloseQueuedOrInFlight(stream) && stream._state === 'writable') {\n    const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n    WritableStreamUpdateBackpressure(stream, backpressure);\n  }\n\n  WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n}\n\n// Abstract operations for the WritableStreamDefaultController.\n\nfunction WritableStreamDefaultControllerAdvanceQueueIfNeeded<W>(controller: WritableStreamDefaultController<W>) {\n  const stream = controller._controlledWritableStream;\n\n  if (!controller._started) {\n    return;\n  }\n\n  if (stream._inFlightWriteRequest !== undefined) {\n    return;\n  }\n\n  const state = stream._state;\n  assert(state !== 'closed' && state !== 'errored');\n  if (state === 'erroring') {\n    WritableStreamFinishErroring(stream);\n    return;\n  }\n\n  if (controller._queue.length === 0) {\n    return;\n  }\n\n  const value = PeekQueueValue(controller);\n  if (value === closeSentinel) {\n    WritableStreamDefaultControllerProcessClose(controller);\n  } else {\n    WritableStreamDefaultControllerProcessWrite(controller, value);\n  }\n}\n\nfunction WritableStreamDefaultControllerErrorIfNeeded(controller: WritableStreamDefaultController<any>, error: any) {\n  if (controller._controlledWritableStream._state === 'writable') {\n    WritableStreamDefaultControllerError(controller, error);\n  }\n}\n\nfunction WritableStreamDefaultControllerProcessClose(controller: WritableStreamDefaultController<any>) {\n  const stream = controller._controlledWritableStream;\n\n  WritableStreamMarkCloseRequestInFlight(stream);\n\n  DequeueValue(controller);\n  assert(controller._queue.length === 0);\n\n  const sinkClosePromise = controller._closeAlgorithm();\n  WritableStreamDefaultControllerClearAlgorithms(controller);\n  uponPromise(\n    sinkClosePromise,\n    () => {\n      WritableStreamFinishInFlightClose(stream);\n    },\n    reason => {\n      WritableStreamFinishInFlightCloseWithError(stream, reason);\n    }\n  );\n}\n\nfunction WritableStreamDefaultControllerProcessWrite<W>(controller: WritableStreamDefaultController<W>, chunk: W) {\n  const stream = controller._controlledWritableStream;\n\n  WritableStreamMarkFirstWriteRequestInFlight(stream);\n\n  const sinkWritePromise = controller._writeAlgorithm(chunk);\n  uponPromise(\n    sinkWritePromise,\n    () => {\n      WritableStreamFinishInFlightWrite(stream);\n\n      const state = stream._state;\n      assert(state === 'writable' || state === 'erroring');\n\n      DequeueValue(controller);\n\n      if (!WritableStreamCloseQueuedOrInFlight(stream) && state === 'writable') {\n        const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n        WritableStreamUpdateBackpressure(stream, backpressure);\n      }\n\n      WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n    },\n    reason => {\n      if (stream._state === 'writable') {\n        WritableStreamDefaultControllerClearAlgorithms(controller);\n      }\n      WritableStreamFinishInFlightWriteWithError(stream, reason);\n    }\n  );\n}\n\nfunction WritableStreamDefaultControllerGetBackpressure(controller: WritableStreamDefaultController<any>): boolean {\n  const desiredSize = WritableStreamDefaultControllerGetDesiredSize(controller);\n  return desiredSize <= 0;\n}\n\n// A client of WritableStreamDefaultController may use these functions directly to bypass state check.\n\nfunction WritableStreamDefaultControllerError(controller: WritableStreamDefaultController<any>, error: any) {\n  const stream = controller._controlledWritableStream;\n\n  assert(stream._state === 'writable');\n\n  WritableStreamDefaultControllerClearAlgorithms(controller);\n  WritableStreamStartErroring(stream, error);\n}\n\n// Helper functions for the WritableStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(`WritableStream.prototype.${name} can only be used on a WritableStream`);\n}\n\n// Helper functions for the WritableStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `WritableStreamDefaultController.prototype.${name} can only be used on a WritableStreamDefaultController`);\n}\n\n\n// Helper functions for the WritableStreamDefaultWriter.\n\nfunction defaultWriterBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `WritableStreamDefaultWriter.prototype.${name} can only be used on a WritableStreamDefaultWriter`);\n}\n\nfunction defaultWriterLockException(name: string): TypeError {\n  return new TypeError('Cannot ' + name + ' a stream using a released writer');\n}\n\nfunction defaultWriterClosedPromiseInitialize(writer: WritableStreamDefaultWriter) {\n  writer._closedPromise = newPromise((resolve, reject) => {\n    writer._closedPromise_resolve = resolve;\n    writer._closedPromise_reject = reject;\n    writer._closedPromiseState = 'pending';\n  });\n}\n\nfunction defaultWriterClosedPromiseInitializeAsRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  defaultWriterClosedPromiseInitialize(writer);\n  defaultWriterClosedPromiseReject(writer, reason);\n}\n\nfunction defaultWriterClosedPromiseInitializeAsResolved(writer: WritableStreamDefaultWriter) {\n  defaultWriterClosedPromiseInitialize(writer);\n  defaultWriterClosedPromiseResolve(writer);\n}\n\nfunction defaultWriterClosedPromiseReject(writer: WritableStreamDefaultWriter, reason: any) {\n  if (writer._closedPromise_reject === undefined) {\n    return;\n  }\n  assert(writer._closedPromiseState === 'pending');\n\n  setPromiseIsHandledToTrue(writer._closedPromise);\n  writer._closedPromise_reject(reason);\n  writer._closedPromise_resolve = undefined;\n  writer._closedPromise_reject = undefined;\n  writer._closedPromiseState = 'rejected';\n}\n\nfunction defaultWriterClosedPromiseResetToRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  assert(writer._closedPromise_resolve === undefined);\n  assert(writer._closedPromise_reject === undefined);\n  assert(writer._closedPromiseState !== 'pending');\n\n  defaultWriterClosedPromiseInitializeAsRejected(writer, reason);\n}\n\nfunction defaultWriterClosedPromiseResolve(writer: WritableStreamDefaultWriter) {\n  if (writer._closedPromise_resolve === undefined) {\n    return;\n  }\n  assert(writer._closedPromiseState === 'pending');\n\n  writer._closedPromise_resolve(undefined);\n  writer._closedPromise_resolve = undefined;\n  writer._closedPromise_reject = undefined;\n  writer._closedPromiseState = 'resolved';\n}\n\nfunction defaultWriterReadyPromiseInitialize(writer: WritableStreamDefaultWriter) {\n  writer._readyPromise = newPromise((resolve, reject) => {\n    writer._readyPromise_resolve = resolve;\n    writer._readyPromise_reject = reject;\n  });\n  writer._readyPromiseState = 'pending';\n}\n\nfunction defaultWriterReadyPromiseInitializeAsRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  defaultWriterReadyPromiseInitialize(writer);\n  defaultWriterReadyPromiseReject(writer, reason);\n}\n\nfunction defaultWriterReadyPromiseInitializeAsResolved(writer: WritableStreamDefaultWriter) {\n  defaultWriterReadyPromiseInitialize(writer);\n  defaultWriterReadyPromiseResolve(writer);\n}\n\nfunction defaultWriterReadyPromiseReject(writer: WritableStreamDefaultWriter, reason: any) {\n  if (writer._readyPromise_reject === undefined) {\n    return;\n  }\n\n  setPromiseIsHandledToTrue(writer._readyPromise);\n  writer._readyPromise_reject(reason);\n  writer._readyPromise_resolve = undefined;\n  writer._readyPromise_reject = undefined;\n  writer._readyPromiseState = 'rejected';\n}\n\nfunction defaultWriterReadyPromiseReset(writer: WritableStreamDefaultWriter) {\n  assert(writer._readyPromise_resolve === undefined);\n  assert(writer._readyPromise_reject === undefined);\n\n  defaultWriterReadyPromiseInitialize(writer);\n}\n\nfunction defaultWriterReadyPromiseResetToRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  assert(writer._readyPromise_resolve === undefined);\n  assert(writer._readyPromise_reject === undefined);\n\n  defaultWriterReadyPromiseInitializeAsRejected(writer, reason);\n}\n\nfunction defaultWriterReadyPromiseResolve(writer: WritableStreamDefaultWriter) {\n  if (writer._readyPromise_resolve === undefined) {\n    return;\n  }\n\n  writer._readyPromise_resolve(undefined);\n  writer._readyPromise_resolve = undefined;\n  writer._readyPromise_reject = undefined;\n  writer._readyPromiseState = 'fulfilled';\n}\n", "/// <reference lib=\"dom\" />\nexport const NativeDOMException: typeof DOMException | undefined =\n  typeof DOMException !== 'undefined' ? DOMException : undefined;\n", "/// <reference types=\"node\" />\nimport { NativeDOMException } from './native';\n\ndeclare class DOMExceptionClass extends Error {\n  constructor(message?: string, name?: string);\n\n  name: string;\n  message: string;\n}\n\ntype DOMException = DOMExceptionClass;\ntype DOMExceptionConstructor = typeof DOMExceptionClass;\n\nfunction isDOMExceptionConstructor(ctor: unknown): ctor is DOMExceptionConstructor {\n  if (!(typeof ctor === 'function' || typeof ctor === 'object')) {\n    return false;\n  }\n  try {\n    new (ctor as DOMExceptionConstructor)();\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nfunction createDOMExceptionPolyfill(): DOMExceptionConstructor {\n  // eslint-disable-next-line no-shadow\n  const ctor = function DOMException(this: DOMException, message?: string, name?: string) {\n    this.message = message || '';\n    this.name = name || 'Error';\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n  } as any;\n  ctor.prototype = Object.create(Error.prototype);\n  Object.defineProperty(ctor.prototype, 'constructor', { value: ctor, writable: true, configurable: true });\n  return ctor;\n}\n\n// eslint-disable-next-line no-redeclare\nconst DOMException: DOMExceptionConstructor =\n  isDOMExceptionConstructor(NativeDOMException) ? NativeDOMException : createDOMExceptionPolyfill();\n\nexport { DOMException };\n", "import { IsReadableStream, IsReadableStreamLocked, ReadableStream, ReadableStreamCancel } from '../readable-stream';\nimport { AcquireReadableStreamDefaultReader, ReadableStreamDefaultReaderRead } from './default-reader';\nimport { ReadableStreamReaderGenericRelease } from './generic-reader';\nimport {\n  AcquireWritableStreamDefaultWriter,\n  IsWritableStream,\n  IsWritableStreamLocked,\n  WritableStream,\n  WritableStreamAbort,\n  WritableStreamCloseQueuedOrInFlight,\n  WritableStreamDefaultWriterCloseWithErrorPropagation,\n  WritableStreamDefaultWriterRelease,\n  WritableStreamDefaultWriterWrite\n} from '../writable-stream';\nimport assert from '../../stub/assert';\nimport {\n  newPromise,\n  PerformPromiseThen,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  uponFulfillment,\n  uponPromise,\n  uponRejection\n} from '../helpers/webidl';\nimport { noop } from '../../utils';\nimport { AbortSignal, isAbortSignal } from '../abort-signal';\nimport { DOMException } from '../../stub/dom-exception';\n\nexport function ReadableStreamPipeTo<T>(source: ReadableStream<T>,\n                                        dest: WritableStream<T>,\n                                        preventClose: boolean,\n                                        preventAbort: boolean,\n                                        preventCancel: boolean,\n                                        signal: AbortSignal | undefined): Promise<undefined> {\n  assert(IsReadableStream(source));\n  assert(IsWritableStream(dest));\n  assert(typeof preventClose === 'boolean');\n  assert(typeof preventAbort === 'boolean');\n  assert(typeof preventCancel === 'boolean');\n  assert(signal === undefined || isAbortSignal(signal));\n  assert(!IsReadableStreamLocked(source));\n  assert(!IsWritableStreamLocked(dest));\n\n  const reader = AcquireReadableStreamDefaultReader<T>(source);\n  const writer = AcquireWritableStreamDefaultWriter<T>(dest);\n\n  source._disturbed = true;\n\n  let shuttingDown = false;\n\n  // This is used to keep track of the spec's requirement that we wait for ongoing writes during shutdown.\n  let currentWrite = promiseResolvedWith<void>(undefined);\n\n  return newPromise((resolve, reject) => {\n    let abortAlgorithm: () => void;\n    if (signal !== undefined) {\n      abortAlgorithm = () => {\n        const error = new DOMException('Aborted', 'AbortError');\n        const actions: Array<() => Promise<void>> = [];\n        if (!preventAbort) {\n          actions.push(() => {\n            if (dest._state === 'writable') {\n              return WritableStreamAbort(dest, error);\n            }\n            return promiseResolvedWith(undefined);\n          });\n        }\n        if (!preventCancel) {\n          actions.push(() => {\n            if (source._state === 'readable') {\n              return ReadableStreamCancel(source, error);\n            }\n            return promiseResolvedWith(undefined);\n          });\n        }\n        shutdownWithAction(() => Promise.all(actions.map(action => action())), true, error);\n      };\n\n      if (signal.aborted) {\n        abortAlgorithm();\n        return;\n      }\n\n      signal.addEventListener('abort', abortAlgorithm);\n    }\n\n    // Using reader and writer, read all chunks from this and write them to dest\n    // - Backpressure must be enforced\n    // - Shutdown must stop all activity\n    function pipeLoop() {\n      return newPromise<void>((resolveLoop, rejectLoop) => {\n        function next(done: boolean) {\n          if (done) {\n            resolveLoop();\n          } else {\n            // Use `PerformPromiseThen` instead of `uponPromise` to avoid\n            // adding unnecessary `.catch(rethrowAssertionErrorRejection)` handlers\n            PerformPromiseThen(pipeStep(), next, rejectLoop);\n          }\n        }\n\n        next(false);\n      });\n    }\n\n    function pipeStep(): Promise<boolean> {\n      if (shuttingDown) {\n        return promiseResolvedWith(true);\n      }\n\n      return PerformPromiseThen(writer._readyPromise, () => {\n        return newPromise<boolean>((resolveRead, rejectRead) => {\n          ReadableStreamDefaultReaderRead(\n            reader,\n            {\n              _chunkSteps: chunk => {\n                currentWrite = PerformPromiseThen(WritableStreamDefaultWriterWrite(writer, chunk), undefined, noop);\n                resolveRead(false);\n              },\n              _closeSteps: () => resolveRead(true),\n              _errorSteps: rejectRead\n            }\n          );\n        });\n      });\n    }\n\n    // Errors must be propagated forward\n    isOrBecomesErrored(source, reader._closedPromise, storedError => {\n      if (!preventAbort) {\n        shutdownWithAction(() => WritableStreamAbort(dest, storedError), true, storedError);\n      } else {\n        shutdown(true, storedError);\n      }\n    });\n\n    // Errors must be propagated backward\n    isOrBecomesErrored(dest, writer._closedPromise, storedError => {\n      if (!preventCancel) {\n        shutdownWithAction(() => ReadableStreamCancel(source, storedError), true, storedError);\n      } else {\n        shutdown(true, storedError);\n      }\n    });\n\n    // Closing must be propagated forward\n    isOrBecomesClosed(source, reader._closedPromise, () => {\n      if (!preventClose) {\n        shutdownWithAction(() => WritableStreamDefaultWriterCloseWithErrorPropagation(writer));\n      } else {\n        shutdown();\n      }\n    });\n\n    // Closing must be propagated backward\n    if (WritableStreamCloseQueuedOrInFlight(dest) || dest._state === 'closed') {\n      const destClosed = new TypeError('the destination writable stream closed before all data could be piped to it');\n\n      if (!preventCancel) {\n        shutdownWithAction(() => ReadableStreamCancel(source, destClosed), true, destClosed);\n      } else {\n        shutdown(true, destClosed);\n      }\n    }\n\n    setPromiseIsHandledToTrue(pipeLoop());\n\n    function waitForWritesToFinish(): Promise<void> {\n      // Another write may have started while we were waiting on this currentWrite, so we have to be sure to wait\n      // for that too.\n      const oldCurrentWrite = currentWrite;\n      return PerformPromiseThen(\n        currentWrite,\n        () => oldCurrentWrite !== currentWrite ? waitForWritesToFinish() : undefined\n      );\n    }\n\n    function isOrBecomesErrored(stream: ReadableStream | WritableStream,\n                                promise: Promise<void>,\n                                action: (reason: any) => void) {\n      if (stream._state === 'errored') {\n        action(stream._storedError);\n      } else {\n        uponRejection(promise, action);\n      }\n    }\n\n    function isOrBecomesClosed(stream: ReadableStream | WritableStream, promise: Promise<void>, action: () => void) {\n      if (stream._state === 'closed') {\n        action();\n      } else {\n        uponFulfillment(promise, action);\n      }\n    }\n\n    function shutdownWithAction(action: () => Promise<unknown>, originalIsError?: boolean, originalError?: any) {\n      if (shuttingDown) {\n        return;\n      }\n      shuttingDown = true;\n\n      if (dest._state === 'writable' && !WritableStreamCloseQueuedOrInFlight(dest)) {\n        uponFulfillment(waitForWritesToFinish(), doTheRest);\n      } else {\n        doTheRest();\n      }\n\n      function doTheRest() {\n        uponPromise(\n          action(),\n          () => finalize(originalIsError, originalError),\n          newError => finalize(true, newError)\n        );\n      }\n    }\n\n    function shutdown(isError?: boolean, error?: any) {\n      if (shuttingDown) {\n        return;\n      }\n      shuttingDown = true;\n\n      if (dest._state === 'writable' && !WritableStreamCloseQueuedOrInFlight(dest)) {\n        uponFulfillment(waitForWritesToFinish(), () => finalize(isError, error));\n      } else {\n        finalize(isError, error);\n      }\n    }\n\n    function finalize(isError?: boolean, error?: any) {\n      WritableStreamDefaultWriterRelease(writer);\n      ReadableStreamReaderGenericRelease(reader);\n\n      if (signal !== undefined) {\n        signal.removeEventListener('abort', abortAlgorithm);\n      }\n      if (isError) {\n        reject(error);\n      } else {\n        resolve(undefined);\n      }\n    }\n  });\n}\n", "import { QueuingStrategySizeCallback } from '../queuing-strategy';\nimport assert from '../../stub/assert';\nimport { DequeueValue, EnqueueValueWithSize, QueuePair, ResetQueue } from '../abstract-ops/queue-with-sizes';\nimport {\n  ReadableStreamAddReadRequest,\n  ReadableStreamFulfillReadRequest,\n  ReadableStreamGetNumReadRequests,\n  ReadRequest\n} from './default-reader';\nimport { SimpleQueue } from '../simple-queue';\nimport { IsReadableStreamLocked, ReadableStream, ReadableStreamClose, ReadableStreamError } from '../readable-stream';\nimport { ValidatedUnderlyingSource } from './underlying-source';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport { CancelSteps, PullSteps } from '../abstract-ops/internal-methods';\nimport { promiseResolvedWith, uponPromise } from '../helpers/webidl';\n\n/**\n * Allows control of a {@link ReadableStream | readable stream}'s state and internal queue.\n *\n * @public\n */\nexport class ReadableStreamDefaultController<R> {\n  /** @internal */\n  _controlledReadableStream!: ReadableStream<R>;\n  /** @internal */\n  _queue!: SimpleQueue<QueuePair<R>>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _closeRequested!: boolean;\n  /** @internal */\n  _pullAgain!: boolean;\n  /** @internal */\n  _pulling !: boolean;\n  /** @internal */\n  _strategySizeAlgorithm!: QueuingStrategySizeCallback<R>;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _pullAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _cancelAlgorithm!: (reason: any) => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is\n   * over-full. An underlying source ought to use this information to determine when and how to apply backpressure.\n   */\n  get desiredSize(): number | null {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('desiredSize');\n    }\n\n    return ReadableStreamDefaultControllerGetDesiredSize(this);\n  }\n\n  /**\n   * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from\n   * the stream, but once those are read, the stream will become closed.\n   */\n  close(): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('close');\n    }\n\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this)) {\n      throw new TypeError('The stream is not in a state that permits close');\n    }\n\n    ReadableStreamDefaultControllerClose(this);\n  }\n\n  /**\n   * Enqueues the given chunk `chunk` in the controlled readable stream.\n   */\n  enqueue(chunk: R): void;\n  enqueue(chunk: R = undefined!): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('enqueue');\n    }\n\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this)) {\n      throw new TypeError('The stream is not in a state that permits enqueue');\n    }\n\n    return ReadableStreamDefaultControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.\n   */\n  error(e: any = undefined): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n\n    ReadableStreamDefaultControllerError(this, e);\n  }\n\n  /** @internal */\n  [CancelSteps](reason: any): Promise<void> {\n    ResetQueue(this);\n    const result = this._cancelAlgorithm(reason);\n    ReadableStreamDefaultControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [PullSteps](readRequest: ReadRequest<R>): void {\n    const stream = this._controlledReadableStream;\n\n    if (this._queue.length > 0) {\n      const chunk = DequeueValue(this);\n\n      if (this._closeRequested && this._queue.length === 0) {\n        ReadableStreamDefaultControllerClearAlgorithms(this);\n        ReadableStreamClose(stream);\n      } else {\n        ReadableStreamDefaultControllerCallPullIfNeeded(this);\n      }\n\n      readRequest._chunkSteps(chunk);\n    } else {\n      ReadableStreamAddReadRequest(stream, readRequest);\n      ReadableStreamDefaultControllerCallPullIfNeeded(this);\n    }\n  }\n}\n\nObject.defineProperties(ReadableStreamDefaultController.prototype, {\n  close: { enumerable: true },\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Abstract operations for the ReadableStreamDefaultController.\n\nfunction IsReadableStreamDefaultController<R = any>(x: any): x is ReadableStreamDefaultController<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledReadableStream')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamDefaultController;\n}\n\nfunction ReadableStreamDefaultControllerCallPullIfNeeded(controller: ReadableStreamDefaultController<any>): void {\n  const shouldPull = ReadableStreamDefaultControllerShouldCallPull(controller);\n  if (!shouldPull) {\n    return;\n  }\n\n  if (controller._pulling) {\n    controller._pullAgain = true;\n    return;\n  }\n\n  assert(!controller._pullAgain);\n\n  controller._pulling = true;\n\n  const pullPromise = controller._pullAlgorithm();\n  uponPromise(\n    pullPromise,\n    () => {\n      controller._pulling = false;\n\n      if (controller._pullAgain) {\n        controller._pullAgain = false;\n        ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n      }\n    },\n    e => {\n      ReadableStreamDefaultControllerError(controller, e);\n    }\n  );\n}\n\nfunction ReadableStreamDefaultControllerShouldCallPull(controller: ReadableStreamDefaultController<any>): boolean {\n  const stream = controller._controlledReadableStream;\n\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return false;\n  }\n\n  if (!controller._started) {\n    return false;\n  }\n\n  if (IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    return true;\n  }\n\n  const desiredSize = ReadableStreamDefaultControllerGetDesiredSize(controller);\n  assert(desiredSize !== null);\n  if (desiredSize! > 0) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction ReadableStreamDefaultControllerClearAlgorithms(controller: ReadableStreamDefaultController<any>) {\n  controller._pullAlgorithm = undefined!;\n  controller._cancelAlgorithm = undefined!;\n  controller._strategySizeAlgorithm = undefined!;\n}\n\n// A client of ReadableStreamDefaultController may use these functions directly to bypass state check.\n\nexport function ReadableStreamDefaultControllerClose(controller: ReadableStreamDefaultController<any>) {\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return;\n  }\n\n  const stream = controller._controlledReadableStream;\n\n  controller._closeRequested = true;\n\n  if (controller._queue.length === 0) {\n    ReadableStreamDefaultControllerClearAlgorithms(controller);\n    ReadableStreamClose(stream);\n  }\n}\n\nexport function ReadableStreamDefaultControllerEnqueue<R>(\n  controller: ReadableStreamDefaultController<R>,\n  chunk: R\n): void {\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return;\n  }\n\n  const stream = controller._controlledReadableStream;\n\n  if (IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    ReadableStreamFulfillReadRequest(stream, chunk, false);\n  } else {\n    let chunkSize;\n    try {\n      chunkSize = controller._strategySizeAlgorithm(chunk);\n    } catch (chunkSizeE) {\n      ReadableStreamDefaultControllerError(controller, chunkSizeE);\n      throw chunkSizeE;\n    }\n\n    try {\n      EnqueueValueWithSize(controller, chunk, chunkSize);\n    } catch (enqueueE) {\n      ReadableStreamDefaultControllerError(controller, enqueueE);\n      throw enqueueE;\n    }\n  }\n\n  ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n}\n\nexport function ReadableStreamDefaultControllerError(controller: ReadableStreamDefaultController<any>, e: any) {\n  const stream = controller._controlledReadableStream;\n\n  if (stream._state !== 'readable') {\n    return;\n  }\n\n  ResetQueue(controller);\n\n  ReadableStreamDefaultControllerClearAlgorithms(controller);\n  ReadableStreamError(stream, e);\n}\n\nexport function ReadableStreamDefaultControllerGetDesiredSize(\n  controller: ReadableStreamDefaultController<any>\n): number | null {\n  const state = controller._controlledReadableStream._state;\n\n  if (state === 'errored') {\n    return null;\n  }\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\n// This is used in the implementation of TransformStream.\nexport function ReadableStreamDefaultControllerHasBackpressure(\n  controller: ReadableStreamDefaultController<any>\n): boolean {\n  if (ReadableStreamDefaultControllerShouldCallPull(controller)) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function ReadableStreamDefaultControllerCanCloseOrEnqueue(\n  controller: ReadableStreamDefaultController<any>\n): boolean {\n  const state = controller._controlledReadableStream._state;\n\n  if (!controller._closeRequested && state === 'readable') {\n    return true;\n  }\n\n  return false;\n}\n\nexport function SetUpReadableStreamDefaultController<R>(stream: ReadableStream<R>,\n                                                        controller: ReadableStreamDefaultController<R>,\n                                                        startAlgorithm: () => void | PromiseLike<void>,\n                                                        pullAlgorithm: () => Promise<void>,\n                                                        cancelAlgorithm: (reason: any) => Promise<void>,\n                                                        highWaterMark: number,\n                                                        sizeAlgorithm: QueuingStrategySizeCallback<R>) {\n  assert(stream._readableStreamController === undefined);\n\n  controller._controlledReadableStream = stream;\n\n  controller._queue = undefined!;\n  controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._started = false;\n  controller._closeRequested = false;\n  controller._pullAgain = false;\n  controller._pulling = false;\n\n  controller._strategySizeAlgorithm = sizeAlgorithm;\n  controller._strategyHWM = highWaterMark;\n\n  controller._pullAlgorithm = pullAlgorithm;\n  controller._cancelAlgorithm = cancelAlgorithm;\n\n  stream._readableStreamController = controller;\n\n  const startResult = startAlgorithm();\n  uponPromise(\n    promiseResolvedWith(startResult),\n    () => {\n      controller._started = true;\n\n      assert(!controller._pulling);\n      assert(!controller._pullAgain);\n\n      ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n    },\n    r => {\n      ReadableStreamDefaultControllerError(controller, r);\n    }\n  );\n}\n\nexport function SetUpReadableStreamDefaultControllerFromUnderlyingSource<R>(\n  stream: ReadableStream<R>,\n  underlyingSource: ValidatedUnderlyingSource<R>,\n  highWaterMark: number,\n  sizeAlgorithm: QueuingStrategySizeCallback<R>\n) {\n  const controller: ReadableStreamDefaultController<R> = Object.create(ReadableStreamDefaultController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void> = () => undefined;\n  let pullAlgorithm: () => Promise<void> = () => promiseResolvedWith(undefined);\n  let cancelAlgorithm: (reason: any) => Promise<void> = () => promiseResolvedWith(undefined);\n\n  if (underlyingSource.start !== undefined) {\n    startAlgorithm = () => underlyingSource.start!(controller);\n  }\n  if (underlyingSource.pull !== undefined) {\n    pullAlgorithm = () => underlyingSource.pull!(controller);\n  }\n  if (underlyingSource.cancel !== undefined) {\n    cancelAlgorithm = reason => underlyingSource.cancel!(reason);\n  }\n\n  SetUpReadableStreamDefaultController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm\n  );\n}\n\n// Helper functions for the ReadableStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamDefaultController.prototype.${name} can only be used on a ReadableStreamDefaultController`);\n}\n", "import {\n  CreateReadableByteStream,\n  CreateReadableStream,\n  IsReadableStream,\n  ReadableByteStream,\n  ReadableStream,\n  ReadableStreamCancel,\n  ReadableStreamReader\n} from '../readable-stream';\nimport { ReadableStreamReaderGenericRelease } from './generic-reader';\nimport {\n  AcquireReadableStreamDefaultReader,\n  IsReadableStreamDefaultReader,\n  ReadableStreamDefaultReaderRead,\n  ReadRequest\n} from './default-reader';\nimport {\n  AcquireReadableStreamBYOBReader,\n  IsReadableStreamBYOBReader,\n  ReadableStreamBYOBReaderRead,\n  ReadIntoRequest\n} from './byob-reader';\nimport assert from '../../stub/assert';\nimport { newPromise, promiseResolvedWith, queueMicrotask, uponRejection } from '../helpers/webidl';\nimport {\n  ReadableStreamDefaultController,\n  ReadableStreamDefaultControllerClose,\n  ReadableStreamDefaultControllerEnqueue,\n  ReadableStreamDefaultControllerError\n} from './default-controller';\nimport {\n  IsReadableByteStreamController,\n  ReadableByteStreamControllerClose,\n  ReadableByteStreamControllerEnqueue,\n  ReadableByteStreamControllerError,\n  ReadableByteStreamControllerGetBYOBRequest,\n  ReadableByteStreamControllerRespond,\n  ReadableByteStreamControllerRespondWithNewView\n} from './byte-stream-controller';\nimport { CreateArrayFromList } from '../abstract-ops/ecmascript';\nimport { CloneAsUint8Array } from '../abstract-ops/miscellaneous';\n\nexport function ReadableStreamTee<R>(stream: ReadableStream<R>,\n                                     cloneForBranch2: boolean): [ReadableStream<R>, ReadableStream<R>] {\n  assert(IsReadableStream(stream));\n  assert(typeof cloneForBranch2 === 'boolean');\n  if (IsReadableByteStreamController(stream._readableStreamController)) {\n    return ReadableByteStreamTee(stream as unknown as ReadableByteStream) as\n      unknown as [ReadableStream<R>, ReadableStream<R>];\n  }\n  return ReadableStreamDefaultTee(stream, cloneForBranch2);\n}\n\nexport function ReadableStreamDefaultTee<R>(stream: ReadableStream<R>,\n                                            cloneForBranch2: boolean): [ReadableStream<R>, ReadableStream<R>] {\n  assert(IsReadableStream(stream));\n  assert(typeof cloneForBranch2 === 'boolean');\n\n  const reader = AcquireReadableStreamDefaultReader<R>(stream);\n\n  let reading = false;\n  let readAgain = false;\n  let canceled1 = false;\n  let canceled2 = false;\n  let reason1: any;\n  let reason2: any;\n  let branch1: ReadableStream<R>;\n  let branch2: ReadableStream<R>;\n\n  let resolveCancelPromise: (value: undefined | Promise<undefined>) => void;\n  const cancelPromise = newPromise<undefined>(resolve => {\n    resolveCancelPromise = resolve;\n  });\n\n  function pullAlgorithm(): Promise<void> {\n    if (reading) {\n      readAgain = true;\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          readAgain = false;\n          const chunk1 = chunk;\n          const chunk2 = chunk;\n\n          // There is no way to access the cloning code right now in the reference implementation.\n          // If we add one then we'll need an implementation for serializable objects.\n          // if (!canceled2 && cloneForBranch2) {\n          //   chunk2 = StructuredDeserialize(StructuredSerialize(chunk2));\n          // }\n\n          if (!canceled1) {\n            ReadableStreamDefaultControllerEnqueue(\n              branch1._readableStreamController as ReadableStreamDefaultController<R>,\n              chunk1\n            );\n          }\n          if (!canceled2) {\n            ReadableStreamDefaultControllerEnqueue(\n              branch2._readableStreamController as ReadableStreamDefaultController<R>,\n              chunk2\n            );\n          }\n\n          reading = false;\n          if (readAgain) {\n            pullAlgorithm();\n          }\n        });\n      },\n      _closeSteps: () => {\n        reading = false;\n        if (!canceled1) {\n          ReadableStreamDefaultControllerClose(branch1._readableStreamController as ReadableStreamDefaultController<R>);\n        }\n        if (!canceled2) {\n          ReadableStreamDefaultControllerClose(branch2._readableStreamController as ReadableStreamDefaultController<R>);\n        }\n\n        if (!canceled1 || !canceled2) {\n          resolveCancelPromise(undefined);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function cancel1Algorithm(reason: any): Promise<void> {\n    canceled1 = true;\n    reason1 = reason;\n    if (canceled2) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function cancel2Algorithm(reason: any): Promise<void> {\n    canceled2 = true;\n    reason2 = reason;\n    if (canceled1) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function startAlgorithm() {\n    // do nothing\n  }\n\n  branch1 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel1Algorithm);\n  branch2 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel2Algorithm);\n\n  uponRejection(reader._closedPromise, (r: any) => {\n    ReadableStreamDefaultControllerError(branch1._readableStreamController as ReadableStreamDefaultController<R>, r);\n    ReadableStreamDefaultControllerError(branch2._readableStreamController as ReadableStreamDefaultController<R>, r);\n    if (!canceled1 || !canceled2) {\n      resolveCancelPromise(undefined);\n    }\n  });\n\n  return [branch1, branch2];\n}\n\nexport function ReadableByteStreamTee(stream: ReadableByteStream): [ReadableByteStream, ReadableByteStream] {\n  assert(IsReadableStream(stream));\n  assert(IsReadableByteStreamController(stream._readableStreamController));\n\n  let reader: ReadableStreamReader<Uint8Array> = AcquireReadableStreamDefaultReader(stream);\n  let reading = false;\n  let readAgainForBranch1 = false;\n  let readAgainForBranch2 = false;\n  let canceled1 = false;\n  let canceled2 = false;\n  let reason1: any;\n  let reason2: any;\n  let branch1: ReadableByteStream;\n  let branch2: ReadableByteStream;\n\n  let resolveCancelPromise: (value: undefined | Promise<undefined>) => void;\n  const cancelPromise = newPromise<void>(resolve => {\n    resolveCancelPromise = resolve;\n  });\n\n  function forwardReaderError(thisReader: ReadableStreamReader<Uint8Array>) {\n    uponRejection(thisReader._closedPromise, r => {\n      if (thisReader !== reader) {\n        return;\n      }\n      ReadableByteStreamControllerError(branch1._readableStreamController, r);\n      ReadableByteStreamControllerError(branch2._readableStreamController, r);\n      if (!canceled1 || !canceled2) {\n        resolveCancelPromise(undefined);\n      }\n    });\n  }\n\n  function pullWithDefaultReader() {\n    if (IsReadableStreamBYOBReader(reader)) {\n      assert(reader._readIntoRequests.length === 0);\n      ReadableStreamReaderGenericRelease(reader);\n\n      reader = AcquireReadableStreamDefaultReader(stream);\n      forwardReaderError(reader);\n    }\n\n    const readRequest: ReadRequest<Uint8Array> = {\n      _chunkSteps: chunk => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          readAgainForBranch1 = false;\n          readAgainForBranch2 = false;\n\n          const chunk1 = chunk;\n          let chunk2 = chunk;\n          if (!canceled1 && !canceled2) {\n            try {\n              chunk2 = CloneAsUint8Array(chunk);\n            } catch (cloneE) {\n              ReadableByteStreamControllerError(branch1._readableStreamController, cloneE);\n              ReadableByteStreamControllerError(branch2._readableStreamController, cloneE);\n              resolveCancelPromise(ReadableStreamCancel(stream, cloneE));\n              return;\n            }\n          }\n\n          if (!canceled1) {\n            ReadableByteStreamControllerEnqueue(branch1._readableStreamController, chunk1);\n          }\n          if (!canceled2) {\n            ReadableByteStreamControllerEnqueue(branch2._readableStreamController, chunk2);\n          }\n\n          reading = false;\n          if (readAgainForBranch1) {\n            pull1Algorithm();\n          } else if (readAgainForBranch2) {\n            pull2Algorithm();\n          }\n        });\n      },\n      _closeSteps: () => {\n        reading = false;\n        if (!canceled1) {\n          ReadableByteStreamControllerClose(branch1._readableStreamController);\n        }\n        if (!canceled2) {\n          ReadableByteStreamControllerClose(branch2._readableStreamController);\n        }\n        if (branch1._readableStreamController._pendingPullIntos.length > 0) {\n          ReadableByteStreamControllerRespond(branch1._readableStreamController, 0);\n        }\n        if (branch2._readableStreamController._pendingPullIntos.length > 0) {\n          ReadableByteStreamControllerRespond(branch2._readableStreamController, 0);\n        }\n        if (!canceled1 || !canceled2) {\n          resolveCancelPromise(undefined);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n  }\n\n  function pullWithBYOBReader(view: ArrayBufferView, forBranch2: boolean) {\n    if (IsReadableStreamDefaultReader<Uint8Array>(reader)) {\n      assert(reader._readRequests.length === 0);\n      ReadableStreamReaderGenericRelease(reader);\n\n      reader = AcquireReadableStreamBYOBReader(stream);\n      forwardReaderError(reader);\n    }\n\n    const byobBranch = forBranch2 ? branch2 : branch1;\n    const otherBranch = forBranch2 ? branch1 : branch2;\n\n    const readIntoRequest: ReadIntoRequest<ArrayBufferView> = {\n      _chunkSteps: chunk => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          readAgainForBranch1 = false;\n          readAgainForBranch2 = false;\n\n          const byobCanceled = forBranch2 ? canceled2 : canceled1;\n          const otherCanceled = forBranch2 ? canceled1 : canceled2;\n\n          if (!otherCanceled) {\n            let clonedChunk;\n            try {\n              clonedChunk = CloneAsUint8Array(chunk);\n            } catch (cloneE) {\n              ReadableByteStreamControllerError(byobBranch._readableStreamController, cloneE);\n              ReadableByteStreamControllerError(otherBranch._readableStreamController, cloneE);\n              resolveCancelPromise(ReadableStreamCancel(stream, cloneE));\n              return;\n            }\n            if (!byobCanceled) {\n              ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n            }\n            ReadableByteStreamControllerEnqueue(otherBranch._readableStreamController, clonedChunk);\n          } else if (!byobCanceled) {\n            ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n          }\n\n          reading = false;\n          if (readAgainForBranch1) {\n            pull1Algorithm();\n          } else if (readAgainForBranch2) {\n            pull2Algorithm();\n          }\n        });\n      },\n      _closeSteps: chunk => {\n        reading = false;\n\n        const byobCanceled = forBranch2 ? canceled2 : canceled1;\n        const otherCanceled = forBranch2 ? canceled1 : canceled2;\n\n        if (!byobCanceled) {\n          ReadableByteStreamControllerClose(byobBranch._readableStreamController);\n        }\n        if (!otherCanceled) {\n          ReadableByteStreamControllerClose(otherBranch._readableStreamController);\n        }\n\n        if (chunk !== undefined) {\n          assert(chunk.byteLength === 0);\n\n          if (!byobCanceled) {\n            ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n          }\n          if (!otherCanceled && otherBranch._readableStreamController._pendingPullIntos.length > 0) {\n            ReadableByteStreamControllerRespond(otherBranch._readableStreamController, 0);\n          }\n        }\n\n        if (!byobCanceled || !otherCanceled) {\n          resolveCancelPromise(undefined);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamBYOBReaderRead(reader, view, readIntoRequest);\n  }\n\n  function pull1Algorithm(): Promise<void> {\n    if (reading) {\n      readAgainForBranch1 = true;\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const byobRequest = ReadableByteStreamControllerGetBYOBRequest(branch1._readableStreamController);\n    if (byobRequest === null) {\n      pullWithDefaultReader();\n    } else {\n      pullWithBYOBReader(byobRequest._view!, false);\n    }\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function pull2Algorithm(): Promise<void> {\n    if (reading) {\n      readAgainForBranch2 = true;\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const byobRequest = ReadableByteStreamControllerGetBYOBRequest(branch2._readableStreamController);\n    if (byobRequest === null) {\n      pullWithDefaultReader();\n    } else {\n      pullWithBYOBReader(byobRequest._view!, true);\n    }\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function cancel1Algorithm(reason: any): Promise<void> {\n    canceled1 = true;\n    reason1 = reason;\n    if (canceled2) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function cancel2Algorithm(reason: any): Promise<void> {\n    canceled2 = true;\n    reason2 = reason;\n    if (canceled1) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function startAlgorithm(): void {\n    return;\n  }\n\n  branch1 = CreateReadableByteStream(startAlgorithm, pull1Algorithm, cancel1Algorithm);\n  branch2 = CreateReadableByteStream(startAlgorithm, pull2Algorithm, cancel2Algorithm);\n\n  forwardReaderError(reader);\n\n  return [branch1, branch2];\n}\n", "import { assertDictionary, assertFunction, convertUnsignedLongLongWithEnforceRange } from './basic';\nimport {\n  ReadableStreamController,\n  UnderlyingByteSource,\n  UnderlyingDefaultOrByteSource,\n  UnderlyingDefaultOrByteSourcePullCallback,\n  UnderlyingDefaultOrByteSourceStartCallback,\n  UnderlyingSource,\n  UnderlyingSourceCancelCallback,\n  ValidatedUnderlyingDefaultOrByteSource\n} from '../readable-stream/underlying-source';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\n\nexport function convertUnderlyingDefaultOrByteSource<R>(\n  source: UnderlyingSource<R> | UnderlyingByteSource | null,\n  context: string\n): ValidatedUnderlyingDefaultOrByteSource<R> {\n  assertDictionary(source, context);\n  const original = source as (UnderlyingDefaultOrByteSource<R> | null);\n  const autoAllocateChunkSize = original?.autoAllocateChunkSize;\n  const cancel = original?.cancel;\n  const pull = original?.pull;\n  const start = original?.start;\n  const type = original?.type;\n  return {\n    autoAllocateChunkSize: autoAllocateChunkSize === undefined ?\n      undefined :\n      convertUnsignedLongLongWithEnforceRange(\n        autoAllocateChunkSize,\n        `${context} has member 'autoAllocateChunkSize' that`\n      ),\n    cancel: cancel === undefined ?\n      undefined :\n      convertUnderlyingSourceCancelCallback(cancel, original!, `${context} has member 'cancel' that`),\n    pull: pull === undefined ?\n      undefined :\n      convertUnderlyingSourcePullCallback(pull, original!, `${context} has member 'pull' that`),\n    start: start === undefined ?\n      undefined :\n      convertUnderlyingSourceStartCallback(start, original!, `${context} has member 'start' that`),\n    type: type === undefined ? undefined : convertReadableStreamType(type, `${context} has member 'type' that`)\n  };\n}\n\nfunction convertUnderlyingSourceCancelCallback(\n  fn: UnderlyingSourceCancelCallback,\n  original: UnderlyingDefaultOrByteSource,\n  context: string\n): (reason: any) => Promise<void> {\n  assertFunction(fn, context);\n  return (reason: any) => promiseCall(fn, original, [reason]);\n}\n\nfunction convertUnderlyingSourcePullCallback<R>(\n  fn: UnderlyingDefaultOrByteSourcePullCallback<R>,\n  original: UnderlyingDefaultOrByteSource<R>,\n  context: string\n): (controller: ReadableStreamController<R>) => Promise<void> {\n  assertFunction(fn, context);\n  return (controller: ReadableStreamController<R>) => promiseCall(fn, original, [controller]);\n}\n\nfunction convertUnderlyingSourceStartCallback<R>(\n  fn: UnderlyingDefaultOrByteSourceStartCallback<R>,\n  original: UnderlyingDefaultOrByteSource<R>,\n  context: string\n): UnderlyingDefaultOrByteSourceStartCallback<R> {\n  assertFunction(fn, context);\n  return (controller: ReadableStreamController<R>) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertReadableStreamType(type: string, context: string): 'bytes' {\n  type = `${type}`;\n  if (type !== 'bytes') {\n    throw new TypeError(`${context} '${type}' is not a valid enumeration value for ReadableStreamType`);\n  }\n  return type;\n}\n", "import { assertDictionary } from './basic';\nimport { ReadableStreamGetReaderOptions } from '../readable-stream/reader-options';\n\nexport function convertReaderOptions(options: ReadableStreamGetReaderOptions | null | undefined,\n                                     context: string): ReadableStreamGetReaderOptions {\n  assertDictionary(options, context);\n  const mode = options?.mode;\n  return {\n    mode: mode === undefined ? undefined : convertReadableStreamReaderMode(mode, `${context} has member 'mode' that`)\n  };\n}\n\nfunction convertReadableStreamReaderMode(mode: string, context: string): 'byob' {\n  mode = `${mode}`;\n  if (mode !== 'byob') {\n    throw new TypeError(`${context} '${mode}' is not a valid enumeration value for ReadableStreamReaderMode`);\n  }\n  return mode;\n}\n", "import { assertDictionary } from './basic';\nimport { StreamPipeOptions, ValidatedStreamPipeOptions } from '../readable-stream/pipe-options';\nimport { AbortSignal, isAbortSignal } from '../abort-signal';\n\nexport function convertPipeOptions(options: StreamPipeOptions | null | undefined,\n                                   context: string): ValidatedStreamPipeOptions {\n  assertDictionary(options, context);\n  const preventAbort = options?.preventAbort;\n  const preventCancel = options?.preventCancel;\n  const preventClose = options?.preventClose;\n  const signal = options?.signal;\n  if (signal !== undefined) {\n    assertAbortSignal(signal, `${context} has member 'signal' that`);\n  }\n  return {\n    preventAbort: <PERSON><PERSON><PERSON>(preventAbort),\n    preventCancel: <PERSON><PERSON>an(preventCancel),\n    preventClose: Boolean(preventClose),\n    signal\n  };\n}\n\nfunction assertAbortSignal(signal: unknown, context: string): asserts signal is AbortSignal {\n  if (!isAbortSignal(signal)) {\n    throw new TypeError(`${context} is not an AbortSignal.`);\n  }\n}\n", "import assert from '../stub/assert';\nimport {\n  promiseRejectedWith,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  transformPromiseWith\n} from './helpers/webidl';\nimport { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { AcquireReadableStreamAsyncIterator, ReadableStreamAsyncIterator } from './readable-stream/async-iterator';\nimport { defaultReaderClosedPromiseReject, defaultReaderClosedPromiseResolve } from './readable-stream/generic-reader';\nimport {\n  AcquireReadableStreamDefaultReader,\n  IsReadableStreamDefaultReader,\n  ReadableStreamDefaultReader,\n  ReadableStreamDefaultReadResult\n} from './readable-stream/default-reader';\nimport {\n  AcquireReadableStreamBYOBReader,\n  IsReadableStreamBYOBReader,\n  ReadableStreamBYOBReader,\n  ReadableStreamBYOBReadResult\n} from './readable-stream/byob-reader';\nimport { ReadableStreamPipeTo } from './readable-stream/pipe';\nimport { ReadableStreamTee } from './readable-stream/tee';\nimport { IsWritableStream, IsWritableStreamLocked, WritableStream } from './writable-stream';\nimport { SimpleQueue } from './simple-queue';\nimport {\n  ReadableByteStreamController,\n  ReadableStreamBYOBRequest,\n  SetUpReadableByteStreamController,\n  SetUpReadableByteStreamControllerFromUnderlyingSource\n} from './readable-stream/byte-stream-controller';\nimport {\n  ReadableStreamDefaultController,\n  SetUpReadableStreamDefaultController,\n  SetUpReadableStreamDefaultControllerFromUnderlyingSource\n} from './readable-stream/default-controller';\nimport {\n  UnderlyingByteSource,\n  UnderlyingByteSourcePullCallback,\n  UnderlyingByteSourceStartCallback,\n  UnderlyingSource,\n  UnderlyingSourceCancelCallback,\n  UnderlyingSourcePullCallback,\n  UnderlyingSourceStartCallback\n} from './readable-stream/underlying-source';\nimport { noop } from '../utils';\nimport { typeIsObject } from './helpers/miscellaneous';\nimport { CreateArrayFromList } from './abstract-ops/ecmascript';\nimport { CancelSteps } from './abstract-ops/internal-methods';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { assertObject, assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport { convertUnderlyingDefaultOrByteSource } from './validators/underlying-source';\nimport { ReadableStreamGetReaderOptions } from './readable-stream/reader-options';\nimport { convertReaderOptions } from './validators/reader-options';\nimport { StreamPipeOptions, ValidatedStreamPipeOptions } from './readable-stream/pipe-options';\nimport { ReadableStreamIteratorOptions } from './readable-stream/iterator-options';\nimport { convertIteratorOptions } from './validators/iterator-options';\nimport { convertPipeOptions } from './validators/pipe-options';\nimport { ReadableWritablePair } from './readable-stream/readable-writable-pair';\nimport { convertReadableWritablePair } from './validators/readable-writable-pair';\n\nexport type ReadableByteStream = ReadableStream<Uint8Array> & {\n  _readableStreamController: ReadableByteStreamController\n};\n\ntype ReadableStreamState = 'readable' | 'closed' | 'errored';\n\n/**\n * A readable stream represents a source of data, from which you can read.\n *\n * @public\n */\nexport class ReadableStream<R = any> {\n  /** @internal */\n  _state!: ReadableStreamState;\n  /** @internal */\n  _reader: ReadableStreamReader<R> | undefined;\n  /** @internal */\n  _storedError: any;\n  /** @internal */\n  _disturbed!: boolean;\n  /** @internal */\n  _readableStreamController!: ReadableStreamDefaultController<R> | ReadableByteStreamController;\n\n  constructor(underlyingSource: UnderlyingByteSource, strategy?: { highWaterMark?: number; size?: undefined });\n  constructor(underlyingSource?: UnderlyingSource<R>, strategy?: QueuingStrategy<R>);\n  constructor(rawUnderlyingSource: UnderlyingSource<R> | UnderlyingByteSource | null | undefined = {},\n              rawStrategy: QueuingStrategy<R> | null | undefined = {}) {\n    if (rawUnderlyingSource === undefined) {\n      rawUnderlyingSource = null;\n    } else {\n      assertObject(rawUnderlyingSource, 'First parameter');\n    }\n\n    const strategy = convertQueuingStrategy(rawStrategy, 'Second parameter');\n    const underlyingSource = convertUnderlyingDefaultOrByteSource(rawUnderlyingSource, 'First parameter');\n\n    InitializeReadableStream(this);\n\n    if (underlyingSource.type === 'bytes') {\n      if (strategy.size !== undefined) {\n        throw new RangeError('The strategy for a byte stream cannot have a size function');\n      }\n      const highWaterMark = ExtractHighWaterMark(strategy, 0);\n      SetUpReadableByteStreamControllerFromUnderlyingSource(\n        this as unknown as ReadableByteStream,\n        underlyingSource,\n        highWaterMark\n      );\n    } else {\n      assert(underlyingSource.type === undefined);\n      const sizeAlgorithm = ExtractSizeAlgorithm(strategy);\n      const highWaterMark = ExtractHighWaterMark(strategy, 1);\n      SetUpReadableStreamDefaultControllerFromUnderlyingSource(\n        this,\n        underlyingSource,\n        highWaterMark,\n        sizeAlgorithm\n      );\n    }\n  }\n\n  /**\n   * Whether or not the readable stream is locked to a {@link ReadableStreamDefaultReader | reader}.\n   */\n  get locked(): boolean {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('locked');\n    }\n\n    return IsReadableStreamLocked(this);\n  }\n\n  /**\n   * Cancels the stream, signaling a loss of interest in the stream by a consumer.\n   *\n   * The supplied `reason` argument will be given to the underlying source's {@link UnderlyingSource.cancel | cancel()}\n   * method, which might or might not use it.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('cancel'));\n    }\n\n    if (IsReadableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot cancel a stream that already has a reader'));\n    }\n\n    return ReadableStreamCancel(this, reason);\n  }\n\n  /**\n   * Creates a {@link ReadableStreamBYOBReader} and locks the stream to the new reader.\n   *\n   * This call behaves the same way as the no-argument variant, except that it only works on readable byte streams,\n   * i.e. streams which were constructed specifically with the ability to handle \"bring your own buffer\" reading.\n   * The returned BYOB reader provides the ability to directly read individual chunks from the stream via its\n   * {@link ReadableStreamBYOBReader.read | read()} method, into developer-supplied buffers, allowing more precise\n   * control over allocation.\n   */\n  getReader({ mode }: { mode: 'byob' }): ReadableStreamBYOBReader;\n  /**\n   * Creates a {@link ReadableStreamDefaultReader} and locks the stream to the new reader.\n   * While the stream is locked, no other reader can be acquired until this one is released.\n   *\n   * This functionality is especially useful for creating abstractions that desire the ability to consume a stream\n   * in its entirety. By getting a reader for the stream, you can ensure nobody else can interleave reads with yours\n   * or cancel the stream, which would interfere with your abstraction.\n   */\n  getReader(): ReadableStreamDefaultReader<R>;\n  getReader(\n    rawOptions: ReadableStreamGetReaderOptions | null | undefined = undefined\n  ): ReadableStreamDefaultReader<R> | ReadableStreamBYOBReader {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('getReader');\n    }\n\n    const options = convertReaderOptions(rawOptions, 'First parameter');\n\n    if (options.mode === undefined) {\n      return AcquireReadableStreamDefaultReader(this);\n    }\n\n    assert(options.mode === 'byob');\n    return AcquireReadableStreamBYOBReader(this as unknown as ReadableByteStream);\n  }\n\n  /**\n   * Provides a convenient, chainable way of piping this readable stream through a transform stream\n   * (or any other `{ writable, readable }` pair). It simply {@link ReadableStream.pipeTo | pipes} the stream\n   * into the writable side of the supplied pair, and returns the readable side for further use.\n   *\n   * Piping a stream will lock it for the duration of the pipe, preventing any other consumer from acquiring a reader.\n   */\n  pipeThrough<RS extends ReadableStream>(\n    transform: { readable: RS; writable: WritableStream<R> },\n    options?: StreamPipeOptions\n  ): RS;\n  pipeThrough<RS extends ReadableStream>(\n    rawTransform: { readable: RS; writable: WritableStream<R> } | null | undefined,\n    rawOptions: StreamPipeOptions | null | undefined = {}\n  ): RS {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('pipeThrough');\n    }\n    assertRequiredArgument(rawTransform, 1, 'pipeThrough');\n\n    const transform = convertReadableWritablePair(rawTransform, 'First parameter');\n    const options = convertPipeOptions(rawOptions, 'Second parameter');\n\n    if (IsReadableStreamLocked(this)) {\n      throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream');\n    }\n    if (IsWritableStreamLocked(transform.writable)) {\n      throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream');\n    }\n\n    const promise = ReadableStreamPipeTo(\n      this, transform.writable, options.preventClose, options.preventAbort, options.preventCancel, options.signal\n    );\n\n    setPromiseIsHandledToTrue(promise);\n\n    return transform.readable;\n  }\n\n  /**\n   * Pipes this readable stream to a given writable stream. The way in which the piping process behaves under\n   * various error conditions can be customized with a number of passed options. It returns a promise that fulfills\n   * when the piping process completes successfully, or rejects if any errors were encountered.\n   *\n   * Piping a stream will lock it for the duration of the pipe, preventing any other consumer from acquiring a reader.\n   */\n  pipeTo(destination: WritableStream<R>, options?: StreamPipeOptions): Promise<void>;\n  pipeTo(destination: WritableStream<R> | null | undefined,\n         rawOptions: StreamPipeOptions | null | undefined = {}): Promise<void> {\n    if (!IsReadableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('pipeTo'));\n    }\n\n    if (destination === undefined) {\n      return promiseRejectedWith(`Parameter 1 is required in 'pipeTo'.`);\n    }\n    if (!IsWritableStream(destination)) {\n      return promiseRejectedWith(\n        new TypeError(`ReadableStream.prototype.pipeTo's first argument must be a WritableStream`)\n      );\n    }\n\n    let options: ValidatedStreamPipeOptions;\n    try {\n      options = convertPipeOptions(rawOptions, 'Second parameter');\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n\n    if (IsReadableStreamLocked(this)) {\n      return promiseRejectedWith(\n        new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream')\n      );\n    }\n    if (IsWritableStreamLocked(destination)) {\n      return promiseRejectedWith(\n        new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream')\n      );\n    }\n\n    return ReadableStreamPipeTo<R>(\n      this, destination, options.preventClose, options.preventAbort, options.preventCancel, options.signal\n    );\n  }\n\n  /**\n   * Tees this readable stream, returning a two-element array containing the two resulting branches as\n   * new {@link ReadableStream} instances.\n   *\n   * Teeing a stream will lock it, preventing any other consumer from acquiring a reader.\n   * To cancel the stream, cancel both of the resulting branches; a composite cancellation reason will then be\n   * propagated to the stream's underlying source.\n   *\n   * Note that the chunks seen in each branch will be the same object. If the chunks are not immutable,\n   * this could allow interference between the two branches.\n   */\n  tee(): [ReadableStream<R>, ReadableStream<R>] {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('tee');\n    }\n\n    const branches = ReadableStreamTee(this, false);\n    return CreateArrayFromList(branches);\n  }\n\n  /**\n   * Asynchronously iterates over the chunks in the stream's internal queue.\n   *\n   * Asynchronously iterating over the stream will lock it, preventing any other consumer from acquiring a reader.\n   * The lock will be released if the async iterator's {@link ReadableStreamAsyncIterator.return | return()} method\n   * is called, e.g. by breaking out of the loop.\n   *\n   * By default, calling the async iterator's {@link ReadableStreamAsyncIterator.return | return()} method will also\n   * cancel the stream. To prevent this, use the stream's {@link ReadableStream.values | values()} method, passing\n   * `true` for the `preventCancel` option.\n   */\n  values(options?: ReadableStreamIteratorOptions): ReadableStreamAsyncIterator<R>;\n  values(rawOptions: ReadableStreamIteratorOptions | null | undefined = undefined): ReadableStreamAsyncIterator<R> {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('values');\n    }\n\n    const options = convertIteratorOptions(rawOptions, 'First parameter');\n    return AcquireReadableStreamAsyncIterator<R>(this, options.preventCancel);\n  }\n\n  /**\n   * {@inheritDoc ReadableStream.values}\n   */\n  [Symbol.asyncIterator]: (options?: ReadableStreamIteratorOptions) => ReadableStreamAsyncIterator<R>;\n}\n\nObject.defineProperties(ReadableStream.prototype, {\n  cancel: { enumerable: true },\n  getReader: { enumerable: true },\n  pipeThrough: { enumerable: true },\n  pipeTo: { enumerable: true },\n  tee: { enumerable: true },\n  values: { enumerable: true },\n  locked: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStream.prototype, Symbol.toStringTag, {\n    value: 'ReadableStream',\n    configurable: true\n  });\n}\nif (typeof Symbol.asyncIterator === 'symbol') {\n  Object.defineProperty(ReadableStream.prototype, Symbol.asyncIterator, {\n    value: ReadableStream.prototype.values,\n    writable: true,\n    configurable: true\n  });\n}\n\nexport {\n  ReadableStreamAsyncIterator,\n  ReadableStreamDefaultReadResult,\n  ReadableStreamBYOBReadResult,\n  UnderlyingByteSource,\n  UnderlyingSource,\n  UnderlyingSourceStartCallback,\n  UnderlyingSourcePullCallback,\n  UnderlyingSourceCancelCallback,\n  UnderlyingByteSourceStartCallback,\n  UnderlyingByteSourcePullCallback,\n  StreamPipeOptions,\n  ReadableWritablePair,\n  ReadableStreamIteratorOptions\n};\n\n// Abstract operations for the ReadableStream.\n\n// Throws if and only if startAlgorithm throws.\nexport function CreateReadableStream<R>(startAlgorithm: () => void | PromiseLike<void>,\n                                        pullAlgorithm: () => Promise<void>,\n                                        cancelAlgorithm: (reason: any) => Promise<void>,\n                                        highWaterMark = 1,\n                                        sizeAlgorithm: QueuingStrategySizeCallback<R> = () => 1): ReadableStream<R> {\n  assert(IsNonNegativeNumber(highWaterMark));\n\n  const stream: ReadableStream<R> = Object.create(ReadableStream.prototype);\n  InitializeReadableStream(stream);\n\n  const controller: ReadableStreamDefaultController<R> = Object.create(ReadableStreamDefaultController.prototype);\n  SetUpReadableStreamDefaultController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm\n  );\n\n  return stream;\n}\n\n// Throws if and only if startAlgorithm throws.\nexport function CreateReadableByteStream(\n  startAlgorithm: () => void | PromiseLike<void>,\n  pullAlgorithm: () => Promise<void>,\n  cancelAlgorithm: (reason: any) => Promise<void>\n): ReadableByteStream {\n  const stream: ReadableByteStream = Object.create(ReadableStream.prototype);\n  InitializeReadableStream(stream);\n\n  const controller: ReadableByteStreamController = Object.create(ReadableByteStreamController.prototype);\n  SetUpReadableByteStreamController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, 0, undefined);\n\n  return stream;\n}\n\nfunction InitializeReadableStream(stream: ReadableStream) {\n  stream._state = 'readable';\n  stream._reader = undefined;\n  stream._storedError = undefined;\n  stream._disturbed = false;\n}\n\nexport function IsReadableStream(x: unknown): x is ReadableStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readableStreamController')) {\n    return false;\n  }\n\n  return x instanceof ReadableStream;\n}\n\nexport function IsReadableStreamDisturbed(stream: ReadableStream): boolean {\n  assert(IsReadableStream(stream));\n\n  return stream._disturbed;\n}\n\nexport function IsReadableStreamLocked(stream: ReadableStream): boolean {\n  assert(IsReadableStream(stream));\n\n  if (stream._reader === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamCancel<R>(stream: ReadableStream<R>, reason: any): Promise<undefined> {\n  stream._disturbed = true;\n\n  if (stream._state === 'closed') {\n    return promiseResolvedWith(undefined);\n  }\n  if (stream._state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  ReadableStreamClose(stream);\n\n  const reader = stream._reader;\n  if (reader !== undefined && IsReadableStreamBYOBReader(reader)) {\n    reader._readIntoRequests.forEach(readIntoRequest => {\n      readIntoRequest._closeSteps(undefined);\n    });\n    reader._readIntoRequests = new SimpleQueue();\n  }\n\n  const sourceCancelPromise = stream._readableStreamController[CancelSteps](reason);\n  return transformPromiseWith(sourceCancelPromise, noop);\n}\n\nexport function ReadableStreamClose<R>(stream: ReadableStream<R>): void {\n  assert(stream._state === 'readable');\n\n  stream._state = 'closed';\n\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return;\n  }\n\n  defaultReaderClosedPromiseResolve(reader);\n\n  if (IsReadableStreamDefaultReader<R>(reader)) {\n    reader._readRequests.forEach(readRequest => {\n      readRequest._closeSteps();\n    });\n    reader._readRequests = new SimpleQueue();\n  }\n}\n\nexport function ReadableStreamError<R>(stream: ReadableStream<R>, e: any): void {\n  assert(IsReadableStream(stream));\n  assert(stream._state === 'readable');\n\n  stream._state = 'errored';\n  stream._storedError = e;\n\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return;\n  }\n\n  defaultReaderClosedPromiseReject(reader, e);\n\n  if (IsReadableStreamDefaultReader<R>(reader)) {\n    reader._readRequests.forEach(readRequest => {\n      readRequest._errorSteps(e);\n    });\n\n    reader._readRequests = new SimpleQueue();\n  } else {\n    assert(IsReadableStreamBYOBReader(reader));\n\n    reader._readIntoRequests.forEach(readIntoRequest => {\n      readIntoRequest._errorSteps(e);\n    });\n\n    reader._readIntoRequests = new SimpleQueue();\n  }\n}\n\n// Readers\n\nexport type ReadableStreamReader<R> = ReadableStreamDefaultReader<R> | ReadableStreamBYOBReader;\n\nexport {\n  ReadableStreamDefaultReader,\n  ReadableStreamBYOBReader\n};\n\n// Controllers\n\nexport {\n  ReadableStreamDefaultController,\n  ReadableStreamBYOBRequest,\n  ReadableByteStreamController\n};\n\n// Helper functions for the ReadableStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(`ReadableStream.prototype.${name} can only be used on a ReadableStream`);\n}\n", "import { assertDictionary, assertRequiredField } from './basic';\nimport { ReadableStream } from '../readable-stream';\nimport { WritableStream } from '../writable-stream';\nimport { assertReadableStream } from './readable-stream';\nimport { assertWritableStream } from './writable-stream';\n\nexport function convertReadableWritablePair<RS extends ReadableStream, WS extends WritableStream>(\n  pair: { readable: RS; writable: WS } | null | undefined,\n  context: string\n): { readable: RS; writable: WS } {\n  assertDictionary(pair, context);\n\n  const readable = pair?.readable;\n  assertRequiredField(readable, 'readable', 'ReadableWritablePair');\n  assertReadableStream(readable, `${context} has member 'readable' that`);\n\n  const writable = pair?.writable;\n  assertRequiredField(writable, 'writable', 'ReadableWritablePair');\n  assertWritableStream(writable, `${context} has member 'writable' that`);\n\n  return { readable, writable };\n}\n", "import { assertDictionary } from './basic';\nimport {\n  ReadableStreamIteratorOptions,\n  ValidatedReadableStreamIteratorOptions\n} from '../readable-stream/iterator-options';\n\nexport function convertIteratorOptions(options: ReadableStreamIteratorOptions | null | undefined,\n                                       context: string): ValidatedReadableStreamIteratorOptions {\n  assertDictionary(options, context);\n  const preventCancel = options?.preventCancel;\n  return { preventCancel: Boolean(preventCancel) };\n}\n", "import { QueuingStrategyInit } from '../queuing-strategy';\nimport { assertDictionary, assertRequiredField, convertUnrestrictedDouble } from './basic';\n\nexport function convertQueuingStrategyInit(init: QueuingStrategyInit | null | undefined,\n                                           context: string): QueuingStrategyInit {\n  assertDictionary(init, context);\n  const highWaterMark = init?.highWaterMark;\n  assertRequiredField(highWaterMark, 'highWaterMark', 'QueuingStrategyInit');\n  return {\n    highWaterMark: convertUnrestrictedDouble(highWaterMark)\n  };\n}\n", "import { QueuingStrategy, QueuingStrategyInit } from './queuing-strategy';\nimport { typeIsObject } from './helpers/miscellaneous';\nimport { assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategyInit } from './validators/queuing-strategy-init';\n\n// The size function must not have a prototype property nor be a constructor\nconst byteLengthSizeFunction = (chunk: ArrayBufferView): number => {\n  return chunk.byteLength;\n};\nObject.defineProperty(byteLengthSizeFunction, 'name', {\n  value: 'size',\n  configurable: true\n});\n\n/**\n * A queuing strategy that counts the number of bytes in each chunk.\n *\n * @public\n */\nexport default class ByteLengthQueuingStrategy implements QueuingStrategy<ArrayBufferView> {\n  /** @internal */\n  readonly _byteLengthQueuingStrategyHighWaterMark: number;\n\n  constructor(options: QueuingStrategyInit) {\n    assertRequiredArgument(options, 1, 'ByteLengthQueuingStrategy');\n    options = convertQueuingStrategyInit(options, 'First parameter');\n    this._byteLengthQueuingStrategyHighWaterMark = options.highWaterMark;\n  }\n\n  /**\n   * Returns the high water mark provided to the constructor.\n   */\n  get highWaterMark(): number {\n    if (!IsByteLengthQueuingStrategy(this)) {\n      throw byteLengthBrandCheckException('highWaterMark');\n    }\n    return this._byteLengthQueuingStrategyHighWaterMark;\n  }\n\n  /**\n   * Measures the size of `chunk` by returning the value of its `byteLength` property.\n   */\n  get size(): (chunk: ArrayBufferView) => number {\n    if (!IsByteLengthQueuingStrategy(this)) {\n      throw byteLengthBrandCheckException('size');\n    }\n    return byteLengthSizeFunction;\n  }\n}\n\nObject.defineProperties(ByteLengthQueuingStrategy.prototype, {\n  highWaterMark: { enumerable: true },\n  size: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ByteLengthQueuingStrategy.prototype, Symbol.toStringTag, {\n    value: 'ByteLengthQueuingStrategy',\n    configurable: true\n  });\n}\n\n// Helper functions for the ByteLengthQueuingStrategy.\n\nfunction byteLengthBrandCheckException(name: string): TypeError {\n  return new TypeError(`ByteLengthQueuingStrategy.prototype.${name} can only be used on a ByteLengthQueuingStrategy`);\n}\n\nexport function IsByteLengthQueuingStrategy(x: any): x is ByteLengthQueuingStrategy {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_byteLengthQueuingStrategyHighWaterMark')) {\n    return false;\n  }\n\n  return x instanceof ByteLengthQueuingStrategy;\n}\n", "import { QueuingStrategy, QueuingStrategyInit } from './queuing-strategy';\nimport { typeIsObject } from './helpers/miscellaneous';\nimport { assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategyInit } from './validators/queuing-strategy-init';\n\n// The size function must not have a prototype property nor be a constructor\nconst countSizeFunction = (): 1 => {\n  return 1;\n};\nObject.defineProperty(countSizeFunction, 'name', {\n  value: 'size',\n  configurable: true\n});\n\n/**\n * A queuing strategy that counts the number of chunks.\n *\n * @public\n */\nexport default class CountQueuingStrategy implements QueuingStrategy<any> {\n  /** @internal */\n  readonly _countQueuingStrategyHighWaterMark!: number;\n\n  constructor(options: QueuingStrategyInit) {\n    assertRequiredArgument(options, 1, 'CountQueuingStrategy');\n    options = convertQueuingStrategyInit(options, 'First parameter');\n    this._countQueuingStrategyHighWaterMark = options.highWaterMark;\n  }\n\n  /**\n   * Returns the high water mark provided to the constructor.\n   */\n  get highWaterMark(): number {\n    if (!IsCountQueuingStrategy(this)) {\n      throw countBrandCheckException('highWaterMark');\n    }\n    return this._countQueuingStrategyHighWaterMark;\n  }\n\n  /**\n   * Measures the size of `chunk` by always returning 1.\n   * This ensures that the total queue size is a count of the number of chunks in the queue.\n   */\n  get size(): (chunk: any) => 1 {\n    if (!IsCountQueuingStrategy(this)) {\n      throw countBrandCheckException('size');\n    }\n    return countSizeFunction;\n  }\n}\n\nObject.defineProperties(CountQueuingStrategy.prototype, {\n  highWaterMark: { enumerable: true },\n  size: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(CountQueuingStrategy.prototype, Symbol.toStringTag, {\n    value: 'CountQueuingStrategy',\n    configurable: true\n  });\n}\n\n// Helper functions for the CountQueuingStrategy.\n\nfunction countBrandCheckException(name: string): TypeError {\n  return new TypeError(`CountQueuingStrategy.prototype.${name} can only be used on a CountQueuingStrategy`);\n}\n\nexport function IsCountQueuingStrategy(x: any): x is CountQueuingStrategy {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_countQueuingStrategyHighWaterMark')) {\n    return false;\n  }\n\n  return x instanceof CountQueuingStrategy;\n}\n", "import { assertDictionary, assertFunction } from './basic';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\nimport {\n  Transformer,\n  TransformerFlushCallback,\n  TransformerStartCallback,\n  TransformerTransformCallback,\n  ValidatedTransformer\n} from '../transform-stream/transformer';\nimport { TransformStreamDefaultController } from '../transform-stream';\n\nexport function convertTransformer<I, O>(original: Transformer<I, O> | null,\n                                         context: string): ValidatedTransformer<I, O> {\n  assertDictionary(original, context);\n  const flush = original?.flush;\n  const readableType = original?.readableType;\n  const start = original?.start;\n  const transform = original?.transform;\n  const writableType = original?.writableType;\n  return {\n    flush: flush === undefined ?\n      undefined :\n      convertTransformerFlushCallback(flush, original!, `${context} has member 'flush' that`),\n    readableType,\n    start: start === undefined ?\n      undefined :\n      convertTransformerStartCallback(start, original!, `${context} has member 'start' that`),\n    transform: transform === undefined ?\n      undefined :\n      convertTransformerTransformCallback(transform, original!, `${context} has member 'transform' that`),\n    writableType\n  };\n}\n\nfunction convertTransformerFlushCallback<I, O>(\n  fn: TransformerFlushCallback<O>,\n  original: Transformer<I, O>,\n  context: string\n): (controller: TransformStreamDefaultController<O>) => Promise<void> {\n  assertFunction(fn, context);\n  return (controller: TransformStreamDefaultController<O>) => promiseCall(fn, original, [controller]);\n}\n\nfunction convertTransformerStartCallback<I, O>(\n  fn: TransformerStartCallback<O>,\n  original: Transformer<I, O>,\n  context: string\n): TransformerStartCallback<O> {\n  assertFunction(fn, context);\n  return (controller: TransformStreamDefaultController<O>) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertTransformerTransformCallback<I, O>(\n  fn: TransformerTransformCallback<I, O>,\n  original: Transformer<I, O>,\n  context: string\n): (chunk: I, controller: TransformStreamDefaultController<O>) => Promise<void> {\n  assertFunction(fn, context);\n  return (chunk: I, controller: TransformStreamDefaultController<O>) => promiseCall(fn, original, [chunk, controller]);\n}\n", "import assert from '../stub/assert';\nimport { newPromise, promiseRejectedWith, promiseResolvedWith, transformPromiseWith } from './helpers/webidl';\nimport { CreateReadableStream, ReadableStream, ReadableStreamDefaultController } from './readable-stream';\nimport {\n  ReadableStreamDefaultControllerCanCloseOrEnqueue,\n  ReadableStreamDefaultControllerClose,\n  ReadableStreamDefaultControllerEnqueue,\n  ReadableStreamDefaultControllerError,\n  ReadableStreamDefaultControllerGetDesiredSize,\n  ReadableStreamDefaultControllerHasBackpressure\n} from './readable-stream/default-controller';\nimport { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { CreateWritableStream, WritableStream, WritableStreamDefaultControllerErrorIfNeeded } from './writable-stream';\nimport { typeIsObject } from './helpers/miscellaneous';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport {\n  Transformer,\n  TransformerFlushCallback,\n  TransformerStartCallback,\n  TransformerTransformCallback,\n  ValidatedTransformer\n} from './transform-stream/transformer';\nimport { convertTransformer } from './validators/transformer';\n\n// Class TransformStream\n\n/**\n * A transform stream consists of a pair of streams: a {@link WritableStream | writable stream},\n * known as its writable side, and a {@link ReadableStream | readable stream}, known as its readable side.\n * In a manner specific to the transform stream in question, writes to the writable side result in new data being\n * made available for reading from the readable side.\n *\n * @public\n */\nexport class TransformStream<I = any, O = any> {\n  /** @internal */\n  _writable!: WritableStream<I>;\n  /** @internal */\n  _readable!: ReadableStream<O>;\n  /** @internal */\n  _backpressure!: boolean;\n  /** @internal */\n  _backpressureChangePromise!: Promise<void>;\n  /** @internal */\n  _backpressureChangePromise_resolve!: () => void;\n  /** @internal */\n  _transformStreamController!: TransformStreamDefaultController<O>;\n\n  constructor(\n    transformer?: Transformer<I, O>,\n    writableStrategy?: QueuingStrategy<I>,\n    readableStrategy?: QueuingStrategy<O>\n  );\n  constructor(rawTransformer: Transformer<I, O> | null | undefined = {},\n              rawWritableStrategy: QueuingStrategy<I> | null | undefined = {},\n              rawReadableStrategy: QueuingStrategy<O> | null | undefined = {}) {\n    if (rawTransformer === undefined) {\n      rawTransformer = null;\n    }\n\n    const writableStrategy = convertQueuingStrategy(rawWritableStrategy, 'Second parameter');\n    const readableStrategy = convertQueuingStrategy(rawReadableStrategy, 'Third parameter');\n\n    const transformer = convertTransformer(rawTransformer, 'First parameter');\n    if (transformer.readableType !== undefined) {\n      throw new RangeError('Invalid readableType specified');\n    }\n    if (transformer.writableType !== undefined) {\n      throw new RangeError('Invalid writableType specified');\n    }\n\n    const readableHighWaterMark = ExtractHighWaterMark(readableStrategy, 0);\n    const readableSizeAlgorithm = ExtractSizeAlgorithm(readableStrategy);\n    const writableHighWaterMark = ExtractHighWaterMark(writableStrategy, 1);\n    const writableSizeAlgorithm = ExtractSizeAlgorithm(writableStrategy);\n\n    let startPromise_resolve!: (value: void | PromiseLike<void>) => void;\n    const startPromise = newPromise<void>(resolve => {\n      startPromise_resolve = resolve;\n    });\n\n    InitializeTransformStream(\n      this, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark, readableSizeAlgorithm\n    );\n    SetUpTransformStreamDefaultControllerFromTransformer(this, transformer);\n\n    if (transformer.start !== undefined) {\n      startPromise_resolve(transformer.start(this._transformStreamController));\n    } else {\n      startPromise_resolve(undefined);\n    }\n  }\n\n  /**\n   * The readable side of the transform stream.\n   */\n  get readable(): ReadableStream<O> {\n    if (!IsTransformStream(this)) {\n      throw streamBrandCheckException('readable');\n    }\n\n    return this._readable;\n  }\n\n  /**\n   * The writable side of the transform stream.\n   */\n  get writable(): WritableStream<I> {\n    if (!IsTransformStream(this)) {\n      throw streamBrandCheckException('writable');\n    }\n\n    return this._writable;\n  }\n}\n\nObject.defineProperties(TransformStream.prototype, {\n  readable: { enumerable: true },\n  writable: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(TransformStream.prototype, Symbol.toStringTag, {\n    value: 'TransformStream',\n    configurable: true\n  });\n}\n\nexport {\n  Transformer,\n  TransformerStartCallback,\n  TransformerFlushCallback,\n  TransformerTransformCallback\n};\n\n// Transform Stream Abstract Operations\n\nexport function CreateTransformStream<I, O>(startAlgorithm: () => void | PromiseLike<void>,\n                                            transformAlgorithm: (chunk: I) => Promise<void>,\n                                            flushAlgorithm: () => Promise<void>,\n                                            writableHighWaterMark = 1,\n                                            writableSizeAlgorithm: QueuingStrategySizeCallback<I> = () => 1,\n                                            readableHighWaterMark = 0,\n                                            readableSizeAlgorithm: QueuingStrategySizeCallback<O> = () => 1) {\n  assert(IsNonNegativeNumber(writableHighWaterMark));\n  assert(IsNonNegativeNumber(readableHighWaterMark));\n\n  const stream: TransformStream<I, O> = Object.create(TransformStream.prototype);\n\n  let startPromise_resolve!: (value: void | PromiseLike<void>) => void;\n  const startPromise = newPromise<void>(resolve => {\n    startPromise_resolve = resolve;\n  });\n\n  InitializeTransformStream(stream, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark,\n                            readableSizeAlgorithm);\n\n  const controller: TransformStreamDefaultController<O> = Object.create(TransformStreamDefaultController.prototype);\n\n  SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm);\n\n  const startResult = startAlgorithm();\n  startPromise_resolve(startResult);\n  return stream;\n}\n\nfunction InitializeTransformStream<I, O>(stream: TransformStream<I, O>,\n                                         startPromise: Promise<void>,\n                                         writableHighWaterMark: number,\n                                         writableSizeAlgorithm: QueuingStrategySizeCallback<I>,\n                                         readableHighWaterMark: number,\n                                         readableSizeAlgorithm: QueuingStrategySizeCallback<O>) {\n  function startAlgorithm(): Promise<void> {\n    return startPromise;\n  }\n\n  function writeAlgorithm(chunk: I): Promise<void> {\n    return TransformStreamDefaultSinkWriteAlgorithm(stream, chunk);\n  }\n\n  function abortAlgorithm(reason: any): Promise<void> {\n    return TransformStreamDefaultSinkAbortAlgorithm(stream, reason);\n  }\n\n  function closeAlgorithm(): Promise<void> {\n    return TransformStreamDefaultSinkCloseAlgorithm(stream);\n  }\n\n  stream._writable = CreateWritableStream(startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm,\n                                          writableHighWaterMark, writableSizeAlgorithm);\n\n  function pullAlgorithm(): Promise<void> {\n    return TransformStreamDefaultSourcePullAlgorithm(stream);\n  }\n\n  function cancelAlgorithm(reason: any): Promise<void> {\n    TransformStreamErrorWritableAndUnblockWrite(stream, reason);\n    return promiseResolvedWith(undefined);\n  }\n\n  stream._readable = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, readableHighWaterMark,\n                                          readableSizeAlgorithm);\n\n  // The [[backpressure]] slot is set to undefined so that it can be initialised by TransformStreamSetBackpressure.\n  stream._backpressure = undefined!;\n  stream._backpressureChangePromise = undefined!;\n  stream._backpressureChangePromise_resolve = undefined!;\n  TransformStreamSetBackpressure(stream, true);\n\n  stream._transformStreamController = undefined!;\n}\n\nfunction IsTransformStream(x: unknown): x is TransformStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_transformStreamController')) {\n    return false;\n  }\n\n  return x instanceof TransformStream;\n}\n\n// This is a no-op if both sides are already errored.\nfunction TransformStreamError(stream: TransformStream, e: any) {\n  ReadableStreamDefaultControllerError(\n    stream._readable._readableStreamController as ReadableStreamDefaultController<any>,\n    e\n  );\n  TransformStreamErrorWritableAndUnblockWrite(stream, e);\n}\n\nfunction TransformStreamErrorWritableAndUnblockWrite(stream: TransformStream, e: any) {\n  TransformStreamDefaultControllerClearAlgorithms(stream._transformStreamController);\n  WritableStreamDefaultControllerErrorIfNeeded(stream._writable._writableStreamController, e);\n  if (stream._backpressure) {\n    // Pretend that pull() was called to permit any pending write() calls to complete. TransformStreamSetBackpressure()\n    // cannot be called from enqueue() or pull() once the ReadableStream is errored, so this will will be the final time\n    // _backpressure is set.\n    TransformStreamSetBackpressure(stream, false);\n  }\n}\n\nfunction TransformStreamSetBackpressure(stream: TransformStream, backpressure: boolean) {\n  // Passes also when called during construction.\n  assert(stream._backpressure !== backpressure);\n\n  if (stream._backpressureChangePromise !== undefined) {\n    stream._backpressureChangePromise_resolve();\n  }\n\n  stream._backpressureChangePromise = newPromise(resolve => {\n    stream._backpressureChangePromise_resolve = resolve;\n  });\n\n  stream._backpressure = backpressure;\n}\n\n// Class TransformStreamDefaultController\n\n/**\n * Allows control of the {@link ReadableStream} and {@link WritableStream} of the associated {@link TransformStream}.\n *\n * @public\n */\nexport class TransformStreamDefaultController<O> {\n  /** @internal */\n  _controlledTransformStream: TransformStream<any, O>;\n  /** @internal */\n  _transformAlgorithm: (chunk: any) => Promise<void>;\n  /** @internal */\n  _flushAlgorithm: () => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the desired size to fill the readable side’s internal queue. It can be negative, if the queue is over-full.\n   */\n  get desiredSize(): number | null {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('desiredSize');\n    }\n\n    const readableController = this._controlledTransformStream._readable._readableStreamController;\n    return ReadableStreamDefaultControllerGetDesiredSize(readableController as ReadableStreamDefaultController<O>);\n  }\n\n  /**\n   * Enqueues the given chunk `chunk` in the readable side of the controlled transform stream.\n   */\n  enqueue(chunk: O): void;\n  enqueue(chunk: O = undefined!): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('enqueue');\n    }\n\n    TransformStreamDefaultControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors both the readable side and the writable side of the controlled transform stream, making all future\n   * interactions with it fail with the given error `e`. Any chunks queued for transformation will be discarded.\n   */\n  error(reason: any = undefined): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n\n    TransformStreamDefaultControllerError(this, reason);\n  }\n\n  /**\n   * Closes the readable side and errors the writable side of the controlled transform stream. This is useful when the\n   * transformer only needs to consume a portion of the chunks written to the writable side.\n   */\n  terminate(): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('terminate');\n    }\n\n    TransformStreamDefaultControllerTerminate(this);\n  }\n}\n\nObject.defineProperties(TransformStreamDefaultController.prototype, {\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  terminate: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(TransformStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'TransformStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Transform Stream Default Controller Abstract Operations\n\nfunction IsTransformStreamDefaultController<O = any>(x: any): x is TransformStreamDefaultController<O> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledTransformStream')) {\n    return false;\n  }\n\n  return x instanceof TransformStreamDefaultController;\n}\n\nfunction SetUpTransformStreamDefaultController<I, O>(stream: TransformStream<I, O>,\n                                                     controller: TransformStreamDefaultController<O>,\n                                                     transformAlgorithm: (chunk: I) => Promise<void>,\n                                                     flushAlgorithm: () => Promise<void>) {\n  assert(IsTransformStream(stream));\n  assert(stream._transformStreamController === undefined);\n\n  controller._controlledTransformStream = stream;\n  stream._transformStreamController = controller;\n\n  controller._transformAlgorithm = transformAlgorithm;\n  controller._flushAlgorithm = flushAlgorithm;\n}\n\nfunction SetUpTransformStreamDefaultControllerFromTransformer<I, O>(stream: TransformStream<I, O>,\n                                                                    transformer: ValidatedTransformer<I, O>) {\n  const controller: TransformStreamDefaultController<O> = Object.create(TransformStreamDefaultController.prototype);\n\n  let transformAlgorithm = (chunk: I): Promise<void> => {\n    try {\n      TransformStreamDefaultControllerEnqueue(controller, chunk as unknown as O);\n      return promiseResolvedWith(undefined);\n    } catch (transformResultE) {\n      return promiseRejectedWith(transformResultE);\n    }\n  };\n\n  let flushAlgorithm: () => Promise<void> = () => promiseResolvedWith(undefined);\n\n  if (transformer.transform !== undefined) {\n    transformAlgorithm = chunk => transformer.transform!(chunk, controller);\n  }\n  if (transformer.flush !== undefined) {\n    flushAlgorithm = () => transformer.flush!(controller);\n  }\n\n  SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm);\n}\n\nfunction TransformStreamDefaultControllerClearAlgorithms(controller: TransformStreamDefaultController<any>) {\n  controller._transformAlgorithm = undefined!;\n  controller._flushAlgorithm = undefined!;\n}\n\nfunction TransformStreamDefaultControllerEnqueue<O>(controller: TransformStreamDefaultController<O>, chunk: O) {\n  const stream = controller._controlledTransformStream;\n  const readableController = stream._readable._readableStreamController as ReadableStreamDefaultController<O>;\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(readableController)) {\n    throw new TypeError('Readable side is not in a state that permits enqueue');\n  }\n\n  // We throttle transform invocations based on the backpressure of the ReadableStream, but we still\n  // accept TransformStreamDefaultControllerEnqueue() calls.\n\n  try {\n    ReadableStreamDefaultControllerEnqueue(readableController, chunk);\n  } catch (e) {\n    // This happens when readableStrategy.size() throws.\n    TransformStreamErrorWritableAndUnblockWrite(stream, e);\n\n    throw stream._readable._storedError;\n  }\n\n  const backpressure = ReadableStreamDefaultControllerHasBackpressure(readableController);\n  if (backpressure !== stream._backpressure) {\n    assert(backpressure);\n    TransformStreamSetBackpressure(stream, true);\n  }\n}\n\nfunction TransformStreamDefaultControllerError(controller: TransformStreamDefaultController<any>, e: any) {\n  TransformStreamError(controller._controlledTransformStream, e);\n}\n\nfunction TransformStreamDefaultControllerPerformTransform<I, O>(controller: TransformStreamDefaultController<O>,\n                                                                chunk: I) {\n  const transformPromise = controller._transformAlgorithm(chunk);\n  return transformPromiseWith(transformPromise, undefined, r => {\n    TransformStreamError(controller._controlledTransformStream, r);\n    throw r;\n  });\n}\n\nfunction TransformStreamDefaultControllerTerminate<O>(controller: TransformStreamDefaultController<O>) {\n  const stream = controller._controlledTransformStream;\n  const readableController = stream._readable._readableStreamController as ReadableStreamDefaultController<O>;\n\n  ReadableStreamDefaultControllerClose(readableController);\n\n  const error = new TypeError('TransformStream terminated');\n  TransformStreamErrorWritableAndUnblockWrite(stream, error);\n}\n\n// TransformStreamDefaultSink Algorithms\n\nfunction TransformStreamDefaultSinkWriteAlgorithm<I, O>(stream: TransformStream<I, O>, chunk: I): Promise<void> {\n  assert(stream._writable._state === 'writable');\n\n  const controller = stream._transformStreamController;\n\n  if (stream._backpressure) {\n    const backpressureChangePromise = stream._backpressureChangePromise;\n    assert(backpressureChangePromise !== undefined);\n    return transformPromiseWith(backpressureChangePromise, () => {\n      const writable = stream._writable;\n      const state = writable._state;\n      if (state === 'erroring') {\n        throw writable._storedError;\n      }\n      assert(state === 'writable');\n      return TransformStreamDefaultControllerPerformTransform<I, O>(controller, chunk);\n    });\n  }\n\n  return TransformStreamDefaultControllerPerformTransform<I, O>(controller, chunk);\n}\n\nfunction TransformStreamDefaultSinkAbortAlgorithm(stream: TransformStream, reason: any): Promise<void> {\n  // abort() is not called synchronously, so it is possible for abort() to be called when the stream is already\n  // errored.\n  TransformStreamError(stream, reason);\n  return promiseResolvedWith(undefined);\n}\n\nfunction TransformStreamDefaultSinkCloseAlgorithm<I, O>(stream: TransformStream<I, O>): Promise<void> {\n  // stream._readable cannot change after construction, so caching it across a call to user code is safe.\n  const readable = stream._readable;\n\n  const controller = stream._transformStreamController;\n  const flushPromise = controller._flushAlgorithm();\n  TransformStreamDefaultControllerClearAlgorithms(controller);\n\n  // Return a promise that is fulfilled with undefined on success.\n  return transformPromiseWith(flushPromise, () => {\n    if (readable._state === 'errored') {\n      throw readable._storedError;\n    }\n    ReadableStreamDefaultControllerClose(readable._readableStreamController as ReadableStreamDefaultController<O>);\n  }, r => {\n    TransformStreamError(stream, r);\n    throw readable._storedError;\n  });\n}\n\n// TransformStreamDefaultSource Algorithms\n\nfunction TransformStreamDefaultSourcePullAlgorithm(stream: TransformStream): Promise<void> {\n  // Invariant. Enforced by the promises returned by start() and pull().\n  assert(stream._backpressure);\n\n  assert(stream._backpressureChangePromise !== undefined);\n\n  TransformStreamSetBackpressure(stream, false);\n\n  // Prevent the next pull() call until there is backpressure.\n  return stream._backpressureChangePromise;\n}\n\n// Helper functions for the TransformStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `TransformStreamDefaultController.prototype.${name} can only be used on a TransformStreamDefaultController`);\n}\n\n// Helper functions for the TransformStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `TransformStream.prototype.${name} can only be used on a TransformStream`);\n}\n", "import {\n  ByteLengthQueuingStrategy,\n  CountQueuingStrategy,\n  ReadableByteStreamController,\n  ReadableStream,\n  ReadableStreamBYOBReader,\n  ReadableStreamBYOBRequest,\n  ReadableStreamDefaultController,\n  ReadableStreamDefaultReader,\n  TransformStream,\n  TransformStreamDefaultController,\n  WritableStream,\n  WritableStreamDefaultController,\n  WritableStreamDefaultWriter\n} from './ponyfill';\nimport { globals } from './utils';\n\n// Export\nexport * from './ponyfill';\n\nconst exports = {\n  ReadableStream,\n  ReadableStreamDefaultController,\n  ReadableByteStreamController,\n  ReadableStreamBYOBRequest,\n  ReadableStreamDefaultReader,\n  ReadableStreamBYOBReader,\n\n  WritableStream,\n  WritableStreamDefaultController,\n  WritableStreamDefaultWriter,\n\n  ByteLengthQueuingStrategy,\n  CountQueuingStrategy,\n\n  TransformStream,\n  TransformStreamDefaultController\n};\n\n// Add classes to global scope\nif (typeof globals !== 'undefined') {\n  for (const prop in exports) {\n    if (Object.prototype.hasOwnProperty.call(exports, prop)) {\n      Object.defineProperty(globals, prop, {\n        value: exports[prop as (keyof typeof exports)],\n        writable: true,\n        configurable: true\n      });\n    }\n  }\n}\n"], "names": ["SymbolPolyfill", "Symbol", "iterator", "description", "noop", "globals", "self", "window", "global", "typeIsObject", "x", "rethrowAssertionErrorRejection", "originalPromise", "Promise", "originalPromiseThen", "prototype", "then", "originalPromiseResolve", "resolve", "bind", "originalPromiseReject", "reject", "newPromise", "executor", "promiseResolvedWith", "value", "promiseRejectedWith", "reason", "PerformPromiseThen", "promise", "onFulfilled", "onRejected", "call", "uponPromise", "undefined", "uponFulfillment", "uponRejection", "transformPromiseWith", "fulfillmentH<PERSON>ler", "<PERSON><PERSON><PERSON><PERSON>", "setPromiseIsHandledToTrue", "queueMicrotask", "globalQueueMicrotask", "resolvedPromise", "fn", "reflectCall", "F", "V", "args", "TypeError", "Function", "apply", "promiseCall", "SimpleQueue", "[object Object]", "this", "_front", "_elements", "_next", "_back", "_cursor", "_size", "length", "element", "oldBack", "newBack", "QUEUE_MAX_ARRAY_SIZE", "push", "oldFront", "newFront", "old<PERSON>ursor", "newCursor", "elements", "callback", "i", "node", "front", "cursor", "ReadableStreamReaderGenericInitialize", "reader", "stream", "_ownerReadableStream", "_reader", "_state", "defaultReaderClosedPromiseInitialize", "defaultReaderClosedPromiseResolve", "defaultReaderClosedPromiseInitializeAsResolved", "defaultReaderClosedPromiseInitializeAsRejected", "_storedError", "ReadableStreamReaderGenericCancel", "ReadableStreamCancel", "ReadableStreamReaderGenericRelease", "defaultReaderClosedPromiseReject", "defaultReaderClosedPromiseResetToRejected", "readerLockException", "name", "_closedPromise", "_closedPromise_resolve", "_closedPromise_reject", "AbortSteps", "ErrorSteps", "CancelSteps", "PullSteps", "NumberIsFinite", "Number", "isFinite", "MathTrunc", "Math", "trunc", "v", "ceil", "floor", "assertDictionary", "obj", "context", "assertFunction", "assertObject", "isObject", "assertRequiredArgument", "position", "assertRequiredField", "field", "convertUnrestrictedDouble", "censorNegativeZero", "convertUnsignedLongLongWithEnforceRange", "upperBound", "MAX_SAFE_INTEGER", "integerPart", "assertReadableStream", "IsReadableStream", "AcquireReadableStreamDefaultReader", "ReadableStreamDefaultReader", "ReadableStreamAddReadRequest", "readRequest", "_readRequests", "ReadableStreamFulfillReadRequest", "chunk", "done", "shift", "_closeSteps", "_chunkSteps", "ReadableStreamGetNumReadRequests", "ReadableStreamHasDefaultReader", "IsReadableStreamDefaultReader", "IsReadableStreamLocked", "closed", "defaultReaderBrandCheckException", "resolvePromise", "rejectPromise", "ReadableStreamDefaultReaderRead", "_errorSteps", "e", "Object", "hasOwnProperty", "_disturbed", "_readableStreamController", "AsyncIteratorPrototype", "defineProperties", "cancel", "enumerable", "read", "releaseLock", "toStringTag", "defineProperty", "configurable", "asyncIterator", "ReadableStreamAsyncIteratorImpl", "preventCancel", "_preventCancel", "nextSteps", "_nextSteps", "_ongoingPromise", "returnSteps", "_returnSteps", "_isFinished", "result", "ReadableStreamAsyncIteratorPrototype", "IsReadableStreamAsyncIterator", "_asyncIteratorImpl", "next", "streamAsyncIteratorBrandCheckException", "return", "setPrototypeOf", "NumberIsNaN", "isNaN", "CreateArrayFromList", "slice", "CopyDataBlockBytes", "dest", "destOffset", "src", "srcOffset", "n", "Uint8Array", "set", "ArrayBufferSlice", "buffer", "begin", "end", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CloneAsUint8Array", "O", "byteOffset", "byteLength", "DequeueValue", "container", "pair", "_queue", "_queueTotalSize", "size", "EnqueueValueWithSize", "Infinity", "RangeError", "ResetQueue", "ReadableStreamBYOBRequest", "view", "IsReadableStreamBYOBRequest", "byobRequestBrandCheckException", "_view", "bytes<PERSON>ritten", "_associatedReadableByteStreamController", "ReadableByteStreamControllerRespond", "<PERSON><PERSON><PERSON><PERSON>", "ReadableByteStreamControllerRespondWithNewView", "respond", "respondWithNewView", "ReadableByteStreamController", "byobRequest", "IsReadableByteStreamController", "byteStreamControllerBrandCheckException", "ReadableByteStreamControllerGetBYOBRequest", "desiredSize", "ReadableByteStreamControllerGetDesiredSize", "_closeRequested", "state", "_controlledReadableByteStream", "ReadableByteStreamControllerClose", "ReadableByteStreamControllerEnqueue", "ReadableByteStreamControllerError", "ReadableByteStreamControllerClearPendingPullIntos", "_cancelAlgorithm", "ReadableByteStreamControllerClearAlgorithms", "entry", "ReadableByteStreamControllerHandleQueueDrain", "autoAllocateChunkSize", "_autoAllocateChunkSize", "bufferE", "pullIntoDescriptor", "bufferByteLength", "bytesFilled", "elementSize", "viewConstructor", "readerType", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ReadableByteStreamControllerCallPullIfNeeded", "controller", "_started", "ReadableStreamHasBYOBReader", "ReadableStreamGetNumReadIntoRequests", "ReadableByteStreamControllerShouldCallPull", "_pulling", "_pullAgain", "_pullAlgorithm", "ReadableByteStreamControllerInvalidateBYOBRequest", "ReadableByteStreamControllerCommitPullIntoDescriptor", "<PERSON><PERSON><PERSON><PERSON>", "ReadableByteStreamControllerConvertPullIntoDescriptor", "readIntoRequest", "_readIntoRequests", "ReadableStreamFulfillReadIntoRequest", "ReadableByteStreamControllerEnqueueChunkToQueue", "ReadableByteStreamControllerFillPullIntoDescriptorFromQueue", "currentAlignedBytes", "maxBytesToCopy", "min", "maxBytesFilled", "maxAlignedBytes", "totalBytesToCopyRemaining", "ready", "queue", "headOfQueue", "peek", "bytesToCopy", "destStart", "ReadableByteStreamControllerFillHeadPullIntoDescriptor", "ReadableStreamClose", "_byobRequest", "ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue", "ReadableByteStreamControllerShiftPendingPullInto", "ReadableByteStreamControllerRespondInternal", "firstDescriptor", "ReadableByteStreamControllerRespondInClosedState", "remainderSize", "remainder", "ReadableByteStreamControllerRespondInReadableState", "<PERSON><PERSON><PERSON><PERSON>", "firstPendingPullInto", "ReadableStreamError", "create", "request", "SetUpReadableStreamBYOBRequest", "_strategyHWM", "viewByteLength", "SetUpReadableByteStreamController", "startAlgorithm", "pullAlgorithm", "cancelAlgorithm", "highWaterMark", "r", "AcquireReadableStreamBYOBReader", "ReadableStreamBYOBReader", "ReadableStreamAddReadIntoRequest", "IsReadableStreamBYOBReader", "close", "enqueue", "error", "byobReaderBrandCheckException", "ReadableStreamBYOBReaderRead", "constructor", "DataView", "BYTES_PER_ELEMENT", "ctor", "emptyView", "ReadableByteStreamControllerPullInto", "ExtractHighWaterMark", "strategy", "defaultHWM", "ExtractSizeAlgorithm", "convertQueuingStrategy", "init", "convertQueuingStrategySize", "convertUnderlyingSinkAbortCallback", "original", "convertUnderlyingSinkCloseCallback", "convertUnderlyingSinkStartCallback", "convertUnderlyingSinkWriteCallback", "assertWritableStream", "IsWritableStream", "supportsAbortController", "AbortController", "WritableStream", "rawUnderlyingSink", "rawStrategy", "underlyingSink", "abort", "start", "type", "write", "convertUnderlyingSink", "InitializeWritableStream", "sizeAlgorithm", "WritableStreamDefaultController", "writeAlgorithm", "closeAlgorithm", "abortAlgorithm", "SetUpWritableStreamDefaultController", "SetUpWritableStreamDefaultControllerFromUnderlyingSink", "locked", "streamBrandCheckException", "IsWritableStreamLocked", "WritableStreamAbort", "WritableStreamCloseQueuedOrInFlight", "WritableStreamClose", "AcquireWritableStreamDefaultWriter", "WritableStreamDefaultWriter", "_writer", "_writableStreamController", "_writeRequests", "_inFlightWriteRequest", "_closeRequest", "_inFlightCloseRequest", "_pendingAbortRequest", "_backpressure", "_abortReason", "_abortController", "_promise", "wasAlreadyErroring", "_resolve", "_reject", "_reason", "_wasAlreadyErroring", "WritableStreamStartErroring", "closeRequest", "writer", "defaultWriterReadyPromiseResolve", "closeSentinel", "WritableStreamDefaultControllerAdvanceQueueIfNeeded", "WritableStreamDealWithRejection", "WritableStreamFinishErroring", "WritableStreamDefaultWriterEnsureReadyPromiseRejected", "WritableStreamHasOperationMarkedInFlight", "storedError", "for<PERSON>ach", "writeRequest", "WritableStreamRejectCloseAndClosedPromiseIfNeeded", "abortRequest", "defaultWriterClosedPromiseReject", "WritableStreamUpdateBackpressure", "backpressure", "defaultWriterReadyPromiseInitialize", "defaultWriterReadyPromiseReset", "getWriter", "_ownerWritableStream", "defaultWriterReadyPromiseInitializeAsResolved", "defaultWriterClosedPromiseInitialize", "defaultWriterReadyPromiseInitializeAsRejected", "defaultWriterClosedPromiseResolve", "defaultWriterClosedPromiseInitializeAsRejected", "IsWritableStreamDefaultWriter", "defaultWriterBrandCheckException", "defaultWriterLockException", "WritableStreamDefaultControllerGetDesiredSize", "WritableStreamDefaultWriterGetDesiredSize", "_readyPromise", "WritableStreamDefaultWriterAbort", "WritableStreamDefaultWriterClose", "WritableStreamDefaultWriterRelease", "WritableStreamDefaultWriterWrite", "WritableStreamDefaultWriterEnsureClosedPromiseRejected", "_closedPromiseState", "defaultWriterClosedPromiseResetToRejected", "_readyPromiseState", "defaultWriterReadyPromiseReject", "defaultWriterReadyPromiseResetToRejected", "releasedError", "chunkSize", "_strategySizeAlgorithm", "chunkSizeE", "WritableStreamDefaultControllerErrorIfNeeded", "WritableStreamDefaultControllerGetChunkSize", "WritableStreamAddWriteRequest", "enqueueE", "_controlledWritableStream", "WritableStreamDefaultControllerGetBackpressure", "WritableStreamDefaultControllerWrite", "abortReason", "IsWritableStreamDefaultController", "defaultControllerBrandCheckException", "signal", "WritableStreamDefaultControllerError", "_abortAlgorithm", "WritableStreamDefaultControllerClearAlgorithms", "createAbortController", "_writeAlgorithm", "_closeAlgorithm", "WritableStreamMarkCloseRequestInFlight", "sinkClosePromise", "WritableStreamFinishInFlightClose", "WritableStreamFinishInFlightCloseWithError", "WritableStreamDefaultControllerProcessClose", "WritableStreamMarkFirstWriteRequestInFlight", "WritableStreamFinishInFlightWrite", "WritableStreamFinishInFlightWriteWithError", "WritableStreamDefaultControllerProcessWrite", "_readyPromise_resolve", "_readyPromise_reject", "NativeDOMException", "DOMException", "isDOMExceptionConstructor", "message", "Error", "captureStackTrace", "writable", "createDOMExceptionPolyfill", "ReadableStreamPipeTo", "source", "preventClose", "preventAbort", "shuttingDown", "currentWrite", "actions", "shutdownWithAction", "all", "map", "action", "aborted", "addEventListener", "isOrBecomesErrored", "shutdown", "WritableStreamDefaultWriterCloseWithErrorPropagation", "destClosed", "waitForWritesToFinish", "oldCurrentWrite", "originalIsError", "originalError", "doTheRest", "finalize", "newError", "isError", "removeEventListener", "resolveLoop", "rejectLoop", "resolveRead", "rejectRead", "ReadableStreamDefaultController", "IsReadableStreamDefaultController", "ReadableStreamDefaultControllerGetDesiredSize", "ReadableStreamDefaultControllerCanCloseOrEnqueue", "ReadableStreamDefaultControllerClose", "ReadableStreamDefaultControllerEnqueue", "ReadableStreamDefaultControllerError", "ReadableStreamDefaultControllerClearAlgorithms", "_controlledReadableStream", "ReadableStreamDefaultControllerCallPullIfNeeded", "ReadableStreamDefaultControllerShouldCallPull", "SetUpReadableStreamDefaultController", "ReadableStreamTee", "cloneForBranch2", "reason1", "reason2", "branch1", "branch2", "resolveCancelPromise", "reading", "readAgainForBranch1", "readAgainForBranch2", "canceled1", "canceled2", "cancelPromise", "forwardReaderError", "thisReader", "pullWithDefaultReader", "chunk1", "chunk2", "cloneE", "pull1Algorithm", "pull2Algorithm", "pullWithBYOBReader", "forBranch2", "byobBranch", "otherBranch", "byobCanceled", "clonedChunk", "otherCanceled", "cancel1Algorithm", "compositeReason", "cancelResult", "cancel2Algorithm", "CreateReadableByteStream", "ReadableByteStreamTee", "readAgain", "CreateReadableStream", "ReadableStreamDefaultTee", "convertUnderlyingSourceCancelCallback", "convertUnderlyingSourcePullCallback", "convertUnderlyingSourceStartCallback", "convertReadableStreamType", "convertReadableStreamReaderMode", "mode", "convertPipeOptions", "options", "isAbortSignal", "assertAbortSignal", "Boolean", "ReadableStream", "rawUnderlyingSource", "underlyingSource", "pull", "convertUnderlyingDefaultOrByteSource", "InitializeReadableStream", "underlyingByteSource", "SetUpReadableByteStreamControllerFromUnderlyingSource", "SetUpReadableStreamDefaultControllerFromUnderlyingSource", "rawOptions", "convertReaderOptions", "rawTransform", "transform", "readable", "convertReadableWritablePair", "destination", "impl", "AcquireReadableStreamAsyncIterator", "convertIteratorOptions", "convertQueuingStrategyInit", "<PERSON><PERSON><PERSON><PERSON>", "pipeThrough", "pipeTo", "tee", "values", "byteLengthSizeFunction", "ByteLengthQueuingStrategy", "_byteLengthQueuingStrategyHighWaterMark", "IsByteLengthQueuingStrategy", "byteLengthBrandCheckException", "countSizeFunction", "CountQueuingStrategy", "_countQueuingStrategyHighWaterMark", "IsCountQueuingStrategy", "countBrandCheckException", "convertTransformerFlushCallback", "convertTransformerStartCallback", "convertTransformerTransformCallback", "TransformStream", "rawTransformer", "rawWritableStrategy", "rawReadableStrategy", "writableStrategy", "readableStrategy", "transformer", "flush", "readableType", "writableType", "convertTransformer", "readableHighWaterMark", "readableSizeAlgorithm", "writableHighWaterMark", "writableSizeAlgorithm", "startPromise_resolve", "startPromise", "_transformStreamController", "_backpressureChangePromise", "_writable", "TransformStreamDefaultControllerPerformTransform", "TransformStreamDefaultSinkWriteAlgorithm", "TransformStreamError", "TransformStreamDefaultSinkAbortAlgorithm", "_readable", "flushPromise", "_flushAlgorithm", "TransformStreamDefaultControllerClearAlgorithms", "TransformStreamDefaultSinkCloseAlgorithm", "TransformStreamSetBackpressure", "TransformStreamDefaultSourcePullAlgorithm", "TransformStreamErrorWritableAndUnblockWrite", "CreateWritableStream", "_backpressureChangePromise_resolve", "InitializeTransformStream", "TransformStreamDefaultController", "transformAlgorithm", "TransformStreamDefaultControllerEnqueue", "transformResultE", "flushAlgorithm", "_controlledTransformStream", "_transformAlgorithm", "SetUpTransformStreamDefaultController", "SetUpTransformStreamDefaultControllerFromTransformer", "IsTransformStream", "IsTransformStreamDefaultController", "TransformStreamDefaultControllerTerminate", "readableController", "ReadableStreamDefaultControllerHasBackpressure", "terminate", "exports", "prop"], "mappings": "0PAEA,MAAMA,EACc,mBAAXC,QAAoD,iBAApBA,OAAOC,SAC5CD,OACAE,GAAe,UAAUA,cCHbC,KAeT,MAAMC,EAVS,oBAATC,KACFA,KACoB,oBAAXC,OACTA,OACoB,oBAAXC,OACTA,YADF,WCROC,EAAaC,GAC3B,MAAqB,iBAANA,GAAwB,OAANA,GAA4B,mBAANA,EAGlD,MAAMC,EAUPP,ECbAQ,EAAkBC,QAClBC,EAAsBD,QAAQE,UAAUC,KACxCC,EAAyBJ,QAAQK,QAAQC,KAAKP,GAC9CQ,EAAwBP,QAAQQ,OAAOF,KAAKP,YAElCU,EAAcC,GAI5B,OAAO,IAAIX,EAAgBW,YAGbC,EAAuBC,GACrC,OAAOR,EAAuBQ,YAGhBC,EAA+BC,GAC7C,OAAOP,EAAsBO,YAGfC,EACdC,EACAC,EACAC,GAGA,OAAOjB,EAAoBkB,KAAKH,EAASC,EAAaC,YAGxCE,EACdJ,EACAC,EACAC,GACAH,EACEA,EAAmBC,EAASC,EAAaC,QACzCG,EACAvB,YAIYwB,EAAmBN,EAAqBC,GACtDG,EAAYJ,EAASC,YAGPM,EAAcP,EAA2BE,GACvDE,EAAYJ,OAASK,EAAWH,YAGlBM,EACdR,EACAS,EACAC,GACA,OAAOX,EAAmBC,EAASS,EAAoBC,YAGzCC,EAA0BX,GACxCD,EAAmBC,OAASK,EAAWvB,GAGlC,MAAM8B,EAA2C,MACtD,MAAMC,EAAuBrC,GAAWA,EAAQoC,eAChD,GAAoC,mBAAzBC,EACT,OAAOA,EAGT,MAAMC,EAAkBnB,OAAoBU,GAC5C,OAAQU,GAAmBhB,EAAmBe,EAAiBC,IAPT,YAUxCC,EAAmCC,EAAiCC,EAAMC,GACxF,GAAiB,mBAANF,EACT,MAAM,IAAIG,UAAU,8BAEtB,OAAOC,SAASnC,UAAUoC,MAAMnB,KAAKc,EAAGC,EAAGC,YAG7BI,EAAmCN,EACAC,EACAC,GAIjD,IACE,OAAOxB,EAAoBqB,EAAYC,EAAGC,EAAGC,IAC7C,MAAOvB,GACP,OAAOC,EAAoBD,UCvElB4B,EAMXC,cAHQC,aAAU,EACVA,WAAQ,EAIdA,KAAKC,OAAS,CACZC,UAAW,GACXC,WAAOxB,GAETqB,KAAKI,MAAQJ,KAAKC,OAIlBD,KAAKK,QAAU,EAEfL,KAAKM,MAAQ,EAGfC,aACE,OAAOP,KAAKM,MAOdP,KAAKS,GACH,MAAMC,EAAUT,KAAKI,MACrB,IAAIM,EAAUD,EAEmBE,QAA7BF,EAAQP,UAAUK,SACpBG,EAAU,CACRR,UAAW,GACXC,WAAOxB,IAMX8B,EAAQP,UAAUU,KAAKJ,GACnBE,IAAYD,IACdT,KAAKI,MAAQM,EACbD,EAAQN,MAAQO,KAEhBV,KAAKM,MAKTP,QAGE,MAAMc,EAAWb,KAAKC,OACtB,IAAIa,EAAWD,EACf,MAAME,EAAYf,KAAKK,QACvB,IAAIW,EAAYD,EAAY,EAE5B,MAAME,EAAWJ,EAASX,UACpBM,EAAUS,EAASF,GAmBzB,OA7FyB,QA4ErBC,IAGFF,EAAWD,EAASV,MACpBa,EAAY,KAIZhB,KAAKM,MACPN,KAAKK,QAAUW,EACXH,IAAaC,IACfd,KAAKC,OAASa,GAIhBG,EAASF,QAAapC,EAEf6B,EAWTT,QAAQmB,GACN,IAAIC,EAAInB,KAAKK,QACTe,EAAOpB,KAAKC,OACZgB,EAAWG,EAAKlB,UACpB,OAAOiB,IAAMF,EAASV,aAAyB5B,IAAfyC,EAAKjB,OAC/BgB,IAAMF,EAASV,SAGjBa,EAAOA,EAAKjB,MACZc,EAAWG,EAAKlB,UAChBiB,EAAI,EACoB,IAApBF,EAASV,UAIfW,EAASD,EAASE,MAChBA,EAMNpB,OAGE,MAAMsB,EAAQrB,KAAKC,OACbqB,EAAStB,KAAKK,QACpB,OAAOgB,EAAMnB,UAAUoB,aCpIXC,EAAyCC,EAAiCC,GACxFD,EAAOE,qBAAuBD,EAC9BA,EAAOE,QAAUH,EAEK,aAAlBC,EAAOG,OACTC,EAAqCL,GACV,WAAlBC,EAAOG,gBAwD2CJ,GAC7DK,EAAqCL,GACrCM,EAAkCN,GAzDhCO,CAA+CP,GAI/CQ,EAA+CR,EAAQC,EAAOQ,uBAOlDC,EAAkCV,EAAmCpD,GAGnF,OAAO+D,GAFQX,EAAOE,qBAEctD,YAGtBgE,EAAmCZ,GAIN,aAAvCA,EAAOE,qBAAqBE,OAC9BS,EACEb,EACA,IAAI9B,UAAU,8FA+CsC8B,EAAmCpD,GAI3F4D,EAA+CR,EAAQpD,GAjDrDkE,CACEd,EACA,IAAI9B,UAAU,qFAGlB8B,EAAOE,qBAAqBC,aAAUhD,EACtC6C,EAAOE,0BAAuB/C,WAKhB4D,EAAoBC,GAClC,OAAO,IAAI9C,UAAU,UAAY8C,EAAO,8CAK1BX,EAAqCL,GACnDA,EAAOiB,eAAiB1E,GAAW,CAACJ,EAASG,KAC3C0D,EAAOkB,uBAAyB/E,EAChC6D,EAAOmB,sBAAwB7E,cAInBkE,EAA+CR,EAAmCpD,GAChGyD,EAAqCL,GACrCa,EAAiCb,EAAQpD,YAQ3BiE,EAAiCb,EAAmCpD,QAC7CO,IAAjC6C,EAAOmB,wBAIX1D,EAA0BuC,EAAOiB,gBACjCjB,EAAOmB,sBAAsBvE,GAC7BoD,EAAOkB,4BAAyB/D,EAChC6C,EAAOmB,2BAAwBhE,YAUjBmD,EAAkCN,QACV7C,IAAlC6C,EAAOkB,yBAIXlB,EAAOkB,4BAAuB/D,GAC9B6C,EAAOkB,4BAAyB/D,EAChC6C,EAAOmB,2BAAwBhE,GChG1B,MAAMiE,EAAalG,EAAO,kBACpBmG,EAAanG,EAAO,kBACpBoG,EAAcpG,EAAO,mBACrBqG,EAAYrG,EAAO,iBCA1BsG,EAAyCC,OAAOC,UAAY,SAAU/F,GAC1E,MAAoB,iBAANA,GAAkB+F,SAAS/F,ICDrCgG,EAA+BC,KAAKC,OAAS,SAAUC,GAC3D,OAAOA,EAAI,EAAIF,KAAKG,KAAKD,GAAKF,KAAKI,MAAMF,aCI3BG,EAAiBC,EACAC,GAC/B,QAAYhF,IAAR+E,IALgB,iBADOvG,EAMYuG,IALM,mBAANvG,GAMrC,MAAM,IAAIuC,UAAU,GAAGiE,2BAPExG,WAcbyG,EAAezG,EAAYwG,GACzC,GAAiB,mBAANxG,EACT,MAAM,IAAIuC,UAAU,GAAGiE,iCASXE,EAAa1G,EACAwG,GAC3B,aANuBxG,GACvB,MAAqB,iBAANA,GAAwB,OAANA,GAA4B,mBAANA,EAKlD2G,CAAS3G,GACZ,MAAM,IAAIuC,UAAU,GAAGiE,gCAIXI,EAAsC5G,EACA6G,EACAL,GACpD,QAAUhF,IAANxB,EACF,MAAM,IAAIuC,UAAU,aAAasE,qBAA4BL,gBAIjDM,EAAmC9G,EACA+G,EACAP,GACjD,QAAUhF,IAANxB,EACF,MAAM,IAAIuC,UAAU,GAAGwE,qBAAyBP,gBAKpCQ,EAA0BjG,GACxC,OAAO+E,OAAO/E,GAGhB,SAASkG,EAAmBjH,GAC1B,OAAa,IAANA,EAAU,EAAIA,WAQPkH,EAAwCnG,EAAgByF,GACtE,MACMW,EAAarB,OAAOsB,iBAE1B,IAAIpH,EAAI8F,OAAO/E,GAGf,GAFAf,EAAIiH,EAAmBjH,IAElB6F,EAAe7F,GAClB,MAAM,IAAIuC,UAAU,GAAGiE,4BAKzB,GAFAxG,EAhBF,SAAqBA,GACnB,OAAOiH,EAAmBjB,EAAUhG,IAehCqH,CAAYrH,GAEZA,EAZe,GAYGA,EAAImH,EACxB,MAAM,IAAI5E,UAAU,GAAGiE,2CAA6DW,gBAGtF,OAAKtB,EAAe7F,IAAY,IAANA,EASnBA,EARE,WClFKsH,EAAqBtH,EAAYwG,GAC/C,IAAKe,GAAiBvH,GACpB,MAAM,IAAIuC,UAAU,GAAGiE,uCC0BXgB,EAAsClD,GACpD,OAAO,IAAImD,4BAA4BnD,YAKzBoD,EAAgCpD,EACAqD,GAI7CrD,EAAOE,QAA4CoD,cAAcnE,KAAKkE,YAGzDE,EAAoCvD,EAA2BwD,EAAsBC,GACnG,MAIMJ,EAJSrD,EAAOE,QAIKoD,cAAcI,QACrCD,EACFJ,EAAYM,cAEZN,EAAYO,YAAYJ,YAIZK,EAAoC7D,GAClD,OAAQA,EAAOE,QAA2CoD,cAAcxE,gBAG1DgF,EAA+B9D,GAC7C,MAAMD,EAASC,EAAOE,QAEtB,YAAehD,IAAX6C,KAICgE,EAA8BhE,SAsBxBoD,4BAYX7E,YAAY0B,GAIV,GAHAsC,EAAuBtC,EAAQ,EAAG,+BAClCgD,EAAqBhD,EAAQ,mBAEzBgE,GAAuBhE,GACzB,MAAM,IAAI/B,UAAU,+EAGtB6B,EAAsCvB,KAAMyB,GAE5CzB,KAAK+E,cAAgB,IAAIjF,EAO3B4F,aACE,OAAKF,EAA8BxF,MAI5BA,KAAKyC,eAHHtE,EAAoBwH,EAAiC,WAShE5F,OAAO3B,GACL,OAAKoH,EAA8BxF,WAIDrB,IAA9BqB,KAAK0B,qBACAvD,EAAoBoE,EAAoB,WAG1CL,EAAkClC,KAAM5B,GAPtCD,EAAoBwH,EAAiC,WAehE5F,OACE,IAAKyF,EAA8BxF,MACjC,OAAO7B,EAAoBwH,EAAiC,SAG9D,QAAkChH,IAA9BqB,KAAK0B,qBACP,OAAOvD,EAAoBoE,EAAoB,cAGjD,IAAIqD,EACAC,EACJ,MAAMvH,EAAUP,GAA+C,CAACJ,EAASG,KACvE8H,EAAiBjI,EACjBkI,EAAgB/H,KAQlB,OADAgI,EAAgC9F,KALI,CAClCqF,YAAaJ,GAASW,EAAe,CAAE1H,MAAO+G,EAAOC,MAAM,IAC3DE,YAAa,IAAMQ,EAAe,CAAE1H,WAAOS,EAAWuG,MAAM,IAC5Da,YAAaC,GAAKH,EAAcG,KAG3B1H,EAYTyB,cACE,IAAKyF,EAA8BxF,MACjC,MAAM2F,EAAiC,eAGzC,QAAkChH,IAA9BqB,KAAK0B,qBAAT,CAIA,GAAI1B,KAAK+E,cAAcxE,OAAS,EAC9B,MAAM,IAAIb,UAAU,uFAGtB0C,EAAmCpC,iBAmBvBwF,EAAuCrI,GACrD,QAAKD,EAAaC,OAIb8I,OAAOzI,UAAU0I,eAAezH,KAAKtB,EAAG,kBAItCA,aAAayH,sCAGNkB,EAAmCtE,EACAsD,GACjD,MAAMrD,EAASD,EAAOE,qBAItBD,EAAO0E,YAAa,EAEE,WAAlB1E,EAAOG,OACTkD,EAAYM,cACe,YAAlB3D,EAAOG,OAChBkD,EAAYiB,YAAYtE,EAAOQ,cAG/BR,EAAO2E,0BAA0BrD,GAAW+B,GAMhD,SAASa,EAAiCnD,GACxC,OAAO,IAAI9C,UACT,yCAAyC8C,uDCpPtC,IAAI6D,GDmMXJ,OAAOK,iBAAiB1B,4BAA4BpH,UAAW,CAC7D+I,OAAQ,CAAEC,YAAY,GACtBC,KAAM,CAAED,YAAY,GACpBE,YAAa,CAAEF,YAAY,GAC3Bd,OAAQ,CAAEc,YAAY,KAEU,iBAAvB9J,EAAOiK,aAChBV,OAAOW,eAAehC,4BAA4BpH,UAAWd,EAAOiK,YAAa,CAC/EzI,MAAO,8BACP2I,cAAc,IC1MkB,iBAAzBnK,EAAOoK,gBAGhBT,GAAyB,CAGvBtG,CAACrD,EAAOoK,iBACN,OAAO9G,OAGXiG,OAAOW,eAAeP,GAAwB3J,EAAOoK,cAAe,CAAEN,YAAY,WCuBvEO,GAMXhH,YAAYyB,EAAwCwF,GAH5ChH,0BAA2ErB,EAC3EqB,kBAAc,EAGpBA,KAAK2B,QAAUH,EACfxB,KAAKiH,eAAiBD,EAGxBjH,OACE,MAAMmH,EAAY,IAAMlH,KAAKmH,aAI7B,OAHAnH,KAAKoH,gBAAkBpH,KAAKoH,gBAC1BtI,EAAqBkB,KAAKoH,gBAAiBF,EAAWA,GACtDA,IACKlH,KAAKoH,gBAGdrH,OAAO7B,GACL,MAAMmJ,EAAc,IAAMrH,KAAKsH,aAAapJ,GAC5C,OAAO8B,KAAKoH,gBACVtI,EAAqBkB,KAAKoH,gBAAiBC,EAAaA,GACxDA,IAGItH,aACN,GAAIC,KAAKuH,YACP,OAAOjK,QAAQK,QAAQ,CAAEO,WAAOS,EAAWuG,MAAM,IAGnD,MAAM1D,EAASxB,KAAK2B,QACpB,QAAoChD,IAAhC6C,EAAOE,qBACT,OAAOvD,EAAoBoE,EAAoB,YAGjD,IAAIqD,EACAC,EACJ,MAAMvH,EAAUP,GAA+C,CAACJ,EAASG,KACvE8H,EAAiBjI,EACjBkI,EAAgB/H,KAuBlB,OADAgI,EAAgCtE,EApBI,CAClC6D,YAAaJ,IACXjF,KAAKoH,qBAAkBzI,EAGvBO,GAAe,IAAM0G,EAAe,CAAE1H,MAAO+G,EAAOC,MAAM,OAE5DE,YAAa,KACXpF,KAAKoH,qBAAkBzI,EACvBqB,KAAKuH,aAAc,EACnBnF,EAAmCZ,GACnCoE,EAAe,CAAE1H,WAAOS,EAAWuG,MAAM,KAE3Ca,YAAa3H,IACX4B,KAAKoH,qBAAkBzI,EACvBqB,KAAKuH,aAAc,EACnBnF,EAAmCZ,GACnCqE,EAAczH,MAIXE,EAGDyB,aAAa7B,GACnB,GAAI8B,KAAKuH,YACP,OAAOjK,QAAQK,QAAQ,CAAEO,MAAAA,EAAOgH,MAAM,IAExClF,KAAKuH,aAAc,EAEnB,MAAM/F,EAASxB,KAAK2B,QACpB,QAAoChD,IAAhC6C,EAAOE,qBACT,OAAOvD,EAAoBoE,EAAoB,qBAKjD,IAAKvC,KAAKiH,eAAgB,CACxB,MAAMO,EAAStF,EAAkCV,EAAQtD,GAEzD,OADAkE,EAAmCZ,GAC5B1C,EAAqB0I,GAAQ,MAAStJ,MAAAA,EAAOgH,MAAM,MAI5D,OADA9C,EAAmCZ,GAC5BvD,EAAoB,CAAEC,MAAAA,EAAOgH,MAAM,KAa9C,MAAMuC,GAAiF,CACrF1H,OACE,OAAK2H,GAA8B1H,MAG5BA,KAAK2H,mBAAmBC,OAFtBzJ,EAAoB0J,GAAuC,UAKtE9H,OAAuD7B,GACrD,OAAKwJ,GAA8B1H,MAG5BA,KAAK2H,mBAAmBG,OAAO5J,GAF7BC,EAAoB0J,GAAuC,aAoBxE,SAASH,GAAuCvK,GAC9C,IAAKD,EAAaC,GAChB,OAAO,EAGT,IAAK8I,OAAOzI,UAAU0I,eAAezH,KAAKtB,EAAG,sBAC3C,OAAO,EAGT,IAEE,OAAQA,EAA+CwK,8BACrDZ,GACF,SACA,OAAO,GAMX,SAASc,GAAuCrF,GAC9C,OAAO,IAAI9C,UAAU,+BAA+B8C,2DApCvB7D,IAA3B0H,IACFJ,OAAO8B,eAAeN,GAAsCpB,ICrJ9D,MAAM2B,GAAmC/E,OAAOgF,OAAS,SAAU9K,GAEjE,OAAOA,GAAMA,YCLC+K,GAAqCjH,GAGnD,OAAOA,EAASkH,iBAGFC,GAAmBC,EACAC,EACAC,EACAC,EACAC,GACjC,IAAIC,WAAWL,GAAMM,IAAI,IAAID,WAAWH,EAAKC,EAAWC,GAAIH,YAoB9CM,GAAiBC,EAAyBC,EAAeC,GAGvE,GAAIF,EAAOV,MACT,OAAOU,EAAOV,MAAMW,EAAOC,GAE7B,MAAMxI,EAASwI,EAAMD,EACfX,EAAQ,IAAIa,YAAYzI,GAE9B,OADA6H,GAAmBD,EAAO,EAAGU,EAAQC,EAAOvI,GACrC4H,WCrBOc,GAAkBC,GAChC,MAAML,EAASD,GAAiBM,EAAEL,OAAQK,EAAEC,WAAYD,EAAEC,WAAaD,EAAEE,YACzE,OAAO,IAAIV,WAAWG,YCPRQ,GAAgBC,GAI9B,MAAMC,EAAOD,EAAUE,OAAOrE,QAM9B,OALAmE,EAAUG,iBAAmBF,EAAKG,KAC9BJ,EAAUG,gBAAkB,IAC9BH,EAAUG,gBAAkB,GAGvBF,EAAKrL,eAGEyL,GAAwBL,EAAyCpL,EAAUwL,GAGzF,GD1BiB,iBADiBpG,EC2BToG,IDtBrB1B,GAAY1E,IAIZA,EAAI,GCkB0BoG,IAASE,EAAAA,EACzC,MAAM,IAAIC,WAAW,4DD5BWvG,EC+BlCgG,EAAUE,OAAO5I,KAAK,CAAE1C,MAAAA,EAAOwL,KAAAA,IAC/BJ,EAAUG,iBAAmBC,WAWfI,GAAcR,GAG5BA,EAAUE,OAAS,IAAI1J,EACvBwJ,EAAUG,gBAAkB,QCRjBM,0BAMXhK,cACE,MAAM,IAAIL,UAAU,uBAMtBsK,WACE,IAAKC,GAA4BjK,MAC/B,MAAMkK,GAA+B,QAGvC,OAAOlK,KAAKmK,MAWdpK,QAAQqK,GACN,IAAKH,GAA4BjK,MAC/B,MAAMkK,GAA+B,WAKvC,GAHAnG,EAAuBqG,EAAc,EAAG,WACxCA,EAAe/F,EAAwC+F,EAAc,wBAEhBzL,IAAjDqB,KAAKqK,wCACP,MAAM,IAAI3K,UAAU,0CAGDM,KAAKmK,MAAOtB,OAOjCyB,GAAoCtK,KAAKqK,wCAAyCD,GAWpFrK,mBAAmBiK,GACjB,IAAKC,GAA4BjK,MAC/B,MAAMkK,GAA+B,sBAIvC,GAFAnG,EAAuBiG,EAAM,EAAG,uBAE3BhB,YAAYuB,OAAOP,GACtB,MAAM,IAAItK,UAAU,gDAGtB,QAAqDf,IAAjDqB,KAAKqK,wCACP,MAAM,IAAI3K,UAAU,0CAGDsK,EAAKnB,OAI1B2B,GAA+CxK,KAAKqK,wCAAyCL,IAIjG/D,OAAOK,iBAAiByD,0BAA0BvM,UAAW,CAC3DiN,QAAS,CAAEjE,YAAY,GACvBkE,mBAAoB,CAAElE,YAAY,GAClCwD,KAAM,CAAExD,YAAY,KAEY,iBAAvB9J,EAAOiK,aAChBV,OAAOW,eAAemD,0BAA0BvM,UAAWd,EAAOiK,YAAa,CAC7EzI,MAAO,4BACP2I,cAAc,UAgDL8D,6BA4BX5K,cACE,MAAM,IAAIL,UAAU,uBAMtBkL,kBACE,IAAKC,GAA+B7K,MAClC,MAAM8K,GAAwC,eAGhD,OAAOC,GAA2C/K,MAOpDgL,kBACE,IAAKH,GAA+B7K,MAClC,MAAM8K,GAAwC,eAGhD,OAAOG,GAA2CjL,MAOpDD,QACE,IAAK8K,GAA+B7K,MAClC,MAAM8K,GAAwC,SAGhD,GAAI9K,KAAKkL,gBACP,MAAM,IAAIxL,UAAU,8DAGtB,MAAMyL,EAAQnL,KAAKoL,8BAA8BxJ,OACjD,GAAc,aAAVuJ,EACF,MAAM,IAAIzL,UAAU,kBAAkByL,8DAGxCE,GAAkCrL,MAQpCD,QAAQkF,GACN,IAAK4F,GAA+B7K,MAClC,MAAM8K,GAAwC,WAIhD,GADA/G,EAAuBkB,EAAO,EAAG,YAC5B+D,YAAYuB,OAAOtF,GACtB,MAAM,IAAIvF,UAAU,sCAEtB,GAAyB,IAArBuF,EAAMmE,WACR,MAAM,IAAI1J,UAAU,uCAEtB,GAAgC,IAA5BuF,EAAM4D,OAAOO,WACf,MAAM,IAAI1J,UAAU,gDAGtB,GAAIM,KAAKkL,gBACP,MAAM,IAAIxL,UAAU,gCAGtB,MAAMyL,EAAQnL,KAAKoL,8BAA8BxJ,OACjD,GAAc,aAAVuJ,EACF,MAAM,IAAIzL,UAAU,kBAAkByL,mEAGxCG,GAAoCtL,KAAMiF,GAM5ClF,MAAMiG,GACJ,IAAK6E,GAA+B7K,MAClC,MAAM8K,GAAwC,SAGhDS,GAAkCvL,KAAMgG,GAI1CjG,CAAC+C,GAAa1E,GACZoN,GAAkDxL,MAElD8J,GAAW9J,MAEX,MAAMwH,EAASxH,KAAKyL,iBAAiBrN,GAErC,OADAsN,GAA4C1L,MACrCwH,EAITzH,CAACgD,GAAW+B,GACV,MAAMrD,EAASzB,KAAKoL,8BAGpB,GAAIpL,KAAKyJ,gBAAkB,EAAG,CAG5B,MAAMkC,EAAQ3L,KAAKwJ,OAAOrE,QAC1BnF,KAAKyJ,iBAAmBkC,EAAMvC,WAE9BwC,GAA6C5L,MAE7C,MAAMgK,EAAO,IAAItB,WAAWiD,EAAM9C,OAAQ8C,EAAMxC,WAAYwC,EAAMvC,YAGlE,YADAtE,EAAYO,YAAY2E,GAI1B,MAAM6B,EAAwB7L,KAAK8L,uBACnC,QAA8BnN,IAA1BkN,EAAqC,CACvC,IAAIhD,EACJ,IACEA,EAAS,IAAIG,YAAY6C,GACzB,MAAOE,GAEP,YADAjH,EAAYiB,YAAYgG,GAI1B,MAAMC,EAAgD,CACpDnD,OAAAA,EACAoD,iBAAkBJ,EAClB1C,WAAY,EACZC,WAAYyC,EACZK,YAAa,EACbC,YAAa,EACbC,gBAAiB1D,WACjB2D,WAAY,WAGdrM,KAAKsM,kBAAkB1L,KAAKoL,GAG9BnH,EAA6BpD,EAAQqD,GACrCyH,GAA6CvM,gBAoBjC6K,GAA+B1N,GAC7C,QAAKD,EAAaC,OAIb8I,OAAOzI,UAAU0I,eAAezH,KAAKtB,EAAG,kCAItCA,aAAawN,8BAGtB,SAASV,GAA4B9M,GACnC,QAAKD,EAAaC,OAIb8I,OAAOzI,UAAU0I,eAAezH,KAAKtB,EAAG,4CAItCA,aAAa4M,2BAGtB,SAASwC,GAA6CC,GAEpD,IA8TF,SAAoDA,GAClD,MAAM/K,EAAS+K,EAAWpB,8BAE1B,GAAsB,aAAlB3J,EAAOG,OACT,OAAO,EAGT,GAAI4K,EAAWtB,gBACb,OAAO,EAGT,IAAKsB,EAAWC,SACd,OAAO,EAGT,GAAIlH,EAA+B9D,IAAW6D,EAAiC7D,GAAU,EACvF,OAAO,EAGT,GAAIiL,GAA4BjL,IAAWkL,GAAqClL,GAAU,EACxF,OAAO,EAKT,GAFoBwJ,GAA2CuB,GAE5C,EACjB,OAAO,EAGT,OAAO,EA5VYI,CAA2CJ,GAE5D,OAGF,GAAIA,EAAWK,SAEb,YADAL,EAAWM,YAAa,GAM1BN,EAAWK,UAAW,EAItBnO,EADoB8N,EAAWO,kBAG7B,KACEP,EAAWK,UAAW,EAElBL,EAAWM,aACbN,EAAWM,YAAa,EACxBP,GAA6CC,OAGjDxG,IACEuF,GAAkCiB,EAAYxG,MAKpD,SAASwF,GAAkDgB,GACzDQ,GAAkDR,GAClDA,EAAWF,kBAAoB,IAAIxM,EAGrC,SAASmN,GACPxL,EACAuK,GAIA,IAAI9G,GAAO,EACW,WAAlBzD,EAAOG,SAETsD,GAAO,GAGT,MAAMgI,EAAaC,GAAyDnB,GACtC,YAAlCA,EAAmBK,WACrBrH,EAAiCvD,EAAQyL,EAAqChI,YChZ7BzD,EACAwD,EACAC,GACnD,MAIMkI,EAJS3L,EAAOE,QAIS0L,kBAAkBlI,QAC7CD,EACFkI,EAAgBhI,YAAYH,GAE5BmI,EAAgB/H,YAAYJ,GDwY5BqI,CAAqC7L,EAAQyL,EAAYhI,GAI7D,SAASiI,GACPnB,GAEA,MAAME,EAAcF,EAAmBE,YACjCC,EAAcH,EAAmBG,YAKvC,OAAO,IAAIH,EAAmBI,gBAC5BJ,EAAmBnD,OAAQmD,EAAmB7C,WAAY+C,EAAcC,GAG5E,SAASoB,GAAgDf,EACA3D,EACAM,EACAC,GACvDoD,EAAWhD,OAAO5I,KAAK,CAAEiI,OAAAA,EAAQM,WAAAA,EAAYC,WAAAA,IAC7CoD,EAAW/C,iBAAmBL,EAGhC,SAASoE,GAA4DhB,EACAR,GACnE,MAAMG,EAAcH,EAAmBG,YAEjCsB,EAAsBzB,EAAmBE,YAAcF,EAAmBE,YAAcC,EAExFuB,EAAiBtK,KAAKuK,IAAInB,EAAW/C,gBACXuC,EAAmB5C,WAAa4C,EAAmBE,aAC7E0B,EAAiB5B,EAAmBE,YAAcwB,EAClDG,EAAkBD,EAAiBA,EAAiBzB,EAE1D,IAAI2B,EAA4BJ,EAC5BK,GAAQ,EACRF,EAAkBJ,IACpBK,EAA4BD,EAAkB7B,EAAmBE,YACjE6B,GAAQ,GAGV,MAAMC,EAAQxB,EAAWhD,OAEzB,KAAOsE,EAA4B,GAAG,CACpC,MAAMG,EAAcD,EAAME,OAEpBC,EAAc/K,KAAKuK,IAAIG,EAA2BG,EAAY7E,YAE9DgF,EAAYpC,EAAmB7C,WAAa6C,EAAmBE,YACrE9D,GAAmB4D,EAAmBnD,OAAQuF,EAAWH,EAAYpF,OAAQoF,EAAY9E,WAAYgF,GAEjGF,EAAY7E,aAAe+E,EAC7BH,EAAM7I,SAEN8I,EAAY9E,YAAcgF,EAC1BF,EAAY7E,YAAc+E,GAE5B3B,EAAW/C,iBAAmB0E,EAE9BE,GAAuD7B,EAAY2B,EAAanC,GAEhF8B,GAA6BK,EAS/B,OAAOJ,EAGT,SAASM,GAAuD7B,EACA9C,EACAsC,GAG9DA,EAAmBE,aAAexC,EAGpC,SAASkC,GAA6CY,GAGjB,IAA/BA,EAAW/C,iBAAyB+C,EAAWtB,iBACjDQ,GAA4Cc,GAC5C8B,GAAoB9B,EAAWpB,gCAE/BmB,GAA6CC,GAIjD,SAASQ,GAAkDR,GACzB,OAA5BA,EAAW+B,eAIf/B,EAAW+B,aAAalE,6CAA0C1L,EAClE6N,EAAW+B,aAAapE,MAAQ,KAChCqC,EAAW+B,aAAe,MAG5B,SAASC,GAAiEhC,GAGxE,KAAOA,EAAWF,kBAAkB/L,OAAS,GAAG,CAC9C,GAAmC,IAA/BiM,EAAW/C,gBACb,OAGF,MAAMuC,EAAqBQ,EAAWF,kBAAkB4B,OAEpDV,GAA4DhB,EAAYR,KAC1EyC,GAAiDjC,GAEjDS,GACET,EAAWpB,8BACXY,KAuHR,SAAS0C,GAA4ClC,EAA0CpC,GAC7F,MAAMuE,EAAkBnC,EAAWF,kBAAkB4B,OAGrDlB,GAAkDR,GAGpC,WADAA,EAAWpB,8BAA8BxJ,OA7CzD,SAA0D4K,EACAmC,GAGxD,MAAMlN,EAAS+K,EAAWpB,8BAC1B,GAAIsB,GAA4BjL,GAC9B,KAAOkL,GAAqClL,GAAU,GAEpDwL,GAAqDxL,EAD1BgN,GAAiDjC,IAyC9EoC,CAAiDpC,GAnCrD,SAA4DA,EACApC,EACA4B,GAK1D,GAFAqC,GAAuD7B,EAAYpC,EAAc4B,GAE7EA,EAAmBE,YAAcF,EAAmBG,YACtD,OAGFsC,GAAiDjC,GAEjD,MAAMqC,EAAgB7C,EAAmBE,YAAcF,EAAmBG,YAC1E,GAAI0C,EAAgB,EAAG,CACrB,MAAM9F,EAAMiD,EAAmB7C,WAAa6C,EAAmBE,YACzD4C,EAAYlG,GAAiBoD,EAAmBnD,OAAQE,EAAM8F,EAAe9F,GACnFwE,GAAgDf,EAAYsC,EAAW,EAAGA,EAAU1F,YAGtF4C,EAAmBE,aAAe2C,EAClC5B,GAAqDT,EAAWpB,8BAA+BY,GAE/FwC,GAAiEhC,GAgB/DuC,CAAmDvC,EAAYpC,EAAcuE,GAG/EpC,GAA6CC,GAG/C,SAASiC,GACPjC,GAIA,OADmBA,EAAWF,kBAAkBnH,QAoClD,SAASuG,GAA4Cc,GACnDA,EAAWO,oBAAiBpO,EAC5B6N,EAAWf,sBAAmB9M,WAKhB0M,GAAkCmB,GAChD,MAAM/K,EAAS+K,EAAWpB,8BAE1B,IAAIoB,EAAWtB,iBAAqC,aAAlBzJ,EAAOG,OAIzC,GAAI4K,EAAW/C,gBAAkB,EAC/B+C,EAAWtB,iBAAkB,MAD/B,CAMA,GAAIsB,EAAWF,kBAAkB/L,OAAS,EAAG,CAE3C,GAD6BiM,EAAWF,kBAAkB4B,OACjChC,YAAc,EAAG,CACxC,MAAMlG,EAAI,IAAItG,UAAU,2DAGxB,MAFA6L,GAAkCiB,EAAYxG,GAExCA,GAIV0F,GAA4Cc,GAC5C8B,GAAoB7M,aAGN6J,GAAoCkB,EAA0CvH,GAC5F,MAAMxD,EAAS+K,EAAWpB,8BAE1B,GAAIoB,EAAWtB,iBAAqC,aAAlBzJ,EAAOG,OACvC,OAGF,MAAMiH,EAAS5D,EAAM4D,OACfM,EAAalE,EAAMkE,WACnBC,EAAanE,EAAMmE,WAInB4F,EAAwCnG,EAE9C,GAAI2D,EAAWF,kBAAkB/L,OAAS,EAAG,CAC3C,MAAM0O,EAAuBzC,EAAWF,kBAAkB4B,OACrCe,EAAqBpG,OHpwBrC,EGywBLoG,EAAqBpG,OAA6BoG,EAAqBpG,OAKzE,GAFAmE,GAAkDR,GAE9CjH,EAA+B9D,GACjC,GAAiD,IAA7C6D,EAAiC7D,GAEnC8L,GAAgDf,EAAYwC,EAAmB7F,EAAYC,OACtF,CAEDoD,EAAWF,kBAAkB/L,OAAS,GAExCkO,GAAiDjC,GAGnDxH,EAAiCvD,EADT,IAAIiH,WAAWsG,EAAmB7F,EAAYC,IACZ,QAEnDsD,GAA4BjL,IAErC8L,GAAgDf,EAAYwC,EAAmB7F,EAAYC,GAC3FoF,GAAiEhC,IAGjEe,GAAgDf,EAAYwC,EAAmB7F,EAAYC,GAG7FmD,GAA6CC,YAG/BjB,GAAkCiB,EAA0CxG,GAC1F,MAAMvE,EAAS+K,EAAWpB,8BAEJ,aAAlB3J,EAAOG,SAIX4J,GAAkDgB,GAElD1C,GAAW0C,GACXd,GAA4Cc,GAC5C0C,GAAoBzN,EAAQuE,aAGd+E,GACdyB,GAEA,GAAgC,OAA5BA,EAAW+B,cAAyB/B,EAAWF,kBAAkB/L,OAAS,EAAG,CAC/E,MAAMoO,EAAkBnC,EAAWF,kBAAkB4B,OAC/ClE,EAAO,IAAItB,WAAWiG,EAAgB9F,OAChB8F,EAAgBxF,WAAawF,EAAgBzC,YAC7CyC,EAAgBvF,WAAauF,EAAgBzC,aAEnEtB,EAAyC3E,OAAOkJ,OAAOpF,0BAA0BvM,YAuK3F,SAAwC4R,EACA5C,EACAxC,GAKtCoF,EAAQ/E,wCAA0CmC,EAClD4C,EAAQjF,MAAQH,EA9KdqF,CAA+BzE,EAAa4B,EAAYxC,GACxDwC,EAAW+B,aAAe3D,EAE5B,OAAO4B,EAAW+B,aAGpB,SAAStD,GAA2CuB,GAClD,MAAMrB,EAAQqB,EAAWpB,8BAA8BxJ,OAEvD,MAAc,YAAVuJ,EACK,KAEK,WAAVA,EACK,EAGFqB,EAAW8C,aAAe9C,EAAW/C,yBAG9Ba,GAAoCkC,EAA0CpC,GAG5F,MAAMuE,EAAkBnC,EAAWF,kBAAkB4B,OAGrD,GAAc,WAFA1B,EAAWpB,8BAA8BxJ,QAGrD,GAAqB,IAAjBwI,EACF,MAAM,IAAI1K,UAAU,wEAEjB,CAEL,GAAqB,IAAjB0K,EACF,MAAM,IAAI1K,UAAU,mFAEtB,GAAIiP,EAAgBzC,YAAc9B,EAAeuE,EAAgBvF,WAC/D,MAAM,IAAIS,WAAW,6BAIzB8E,EAAgB9F,OAA6B8F,EAAgB9F,OAE7D6F,GAA4ClC,EAAYpC,YAG1CI,GAA+CgC,EACAxC,GAI7D,MAAM2E,EAAkBnC,EAAWF,kBAAkB4B,OAGrD,GAAc,WAFA1B,EAAWpB,8BAA8BxJ,QAGrD,GAAwB,IAApBoI,EAAKZ,WACP,MAAM,IAAI1J,UAAU,yFAItB,GAAwB,IAApBsK,EAAKZ,WACP,MAAM,IAAI1J,UACR,mGAKN,GAAIiP,EAAgBxF,WAAawF,EAAgBzC,cAAgBlC,EAAKb,WACpE,MAAM,IAAIU,WAAW,2DAEvB,GAAI8E,EAAgB1C,mBAAqBjC,EAAKnB,OAAOO,WACnD,MAAM,IAAIS,WAAW,8DAEvB,GAAI8E,EAAgBzC,YAAclC,EAAKZ,WAAauF,EAAgBvF,WAClE,MAAM,IAAIS,WAAW,2DAGvB,MAAM0F,EAAiBvF,EAAKZ,WAC5BuF,EAAgB9F,OAA6BmB,EAAKnB,OAClD6F,GAA4ClC,EAAY+C,YAG1CC,GAAkC/N,EACA+K,EACAiD,EACAC,EACAC,EACAC,EACA/D,GAOhDW,EAAWpB,8BAAgC3J,EAE3C+K,EAAWM,YAAa,EACxBN,EAAWK,UAAW,EAEtBL,EAAW+B,aAAe,KAG1B/B,EAAWhD,OAASgD,EAAW/C,qBAAkB9K,EACjDmL,GAAW0C,GAEXA,EAAWtB,iBAAkB,EAC7BsB,EAAWC,UAAW,EAEtBD,EAAW8C,aAAeM,EAE1BpD,EAAWO,eAAiB2C,EAC5BlD,EAAWf,iBAAmBkE,EAE9BnD,EAAWV,uBAAyBD,EAEpCW,EAAWF,kBAAoB,IAAIxM,EAEnC2B,EAAO2E,0BAA4BoG,EAGnC9N,EACET,EAFkBwR,MAGlB,KACEjD,EAAWC,UAAW,EAKtBF,GAA6CC,MAE/CqD,IACEtE,GAAkCiB,EAAYqD,MAiDpD,SAAS3F,GAA+B1H,GACtC,OAAO,IAAI9C,UACT,uCAAuC8C,qDAK3C,SAASsI,GAAwCtI,GAC/C,OAAO,IAAI9C,UACT,0CAA0C8C,iECp/B9BsN,GAAgCrO,GAC9C,OAAO,IAAIsO,yBAAyBtO,YAKtBuO,GAA4DvO,EACA2L,GAIzE3L,EAAOE,QAAsC0L,kBAAkBzM,KAAKwM,YAkBvDT,GAAqClL,GACnD,OAAQA,EAAOE,QAAqC0L,kBAAkB9M,gBAGxDmM,GAA4BjL,GAC1C,MAAMD,EAASC,EAAOE,QAEtB,YAAehD,IAAX6C,KAICyO,GAA2BzO,GD0RlCyE,OAAOK,iBAAiBqE,6BAA6BnN,UAAW,CAC9D0S,MAAO,CAAE1J,YAAY,GACrB2J,QAAS,CAAE3J,YAAY,GACvB4J,MAAO,CAAE5J,YAAY,GACrBoE,YAAa,CAAEpE,YAAY,GAC3BwE,YAAa,CAAExE,YAAY,KAEK,iBAAvB9J,EAAOiK,aAChBV,OAAOW,eAAe+D,6BAA6BnN,UAAWd,EAAOiK,YAAa,CAChFzI,MAAO,+BACP2I,cAAc,UC9QLkJ,yBAYXhQ,YAAY0B,GAIV,GAHAsC,EAAuBtC,EAAQ,EAAG,4BAClCgD,EAAqBhD,EAAQ,mBAEzBgE,GAAuBhE,GACzB,MAAM,IAAI/B,UAAU,+EAGtB,IAAKmL,GAA+BpJ,EAAO2E,2BACzC,MAAM,IAAI1G,UAAU,+FAItB6B,EAAsCvB,KAAMyB,GAE5CzB,KAAKqN,kBAAoB,IAAIvN,EAO/B4F,aACE,OAAKuK,GAA2BjQ,MAIzBA,KAAKyC,eAHHtE,EAAoBkS,GAA8B,WAS7DtQ,OAAO3B,GACL,OAAK6R,GAA2BjQ,WAIErB,IAA9BqB,KAAK0B,qBACAvD,EAAoBoE,EAAoB,WAG1CL,EAAkClC,KAAM5B,GAPtCD,EAAoBkS,GAA8B,WAe7DtQ,KAAgCiK,GAC9B,IAAKiG,GAA2BjQ,MAC9B,OAAO7B,EAAoBkS,GAA8B,SAG3D,IAAKrH,YAAYuB,OAAOP,GACtB,OAAO7L,EAAoB,IAAIuB,UAAU,sCAE3C,GAAwB,IAApBsK,EAAKZ,WACP,OAAOjL,EAAoB,IAAIuB,UAAU,uCAE3C,GAA+B,IAA3BsK,EAAKnB,OAAOO,WACd,OAAOjL,EAAoB,IAAIuB,UAAU,gDAM3C,GAJqBsK,EAAKnB,YAIQlK,IAA9BqB,KAAK0B,qBACP,OAAOvD,EAAoBoE,EAAoB,cAGjD,IAAIqD,EACAC,EACJ,MAAMvH,EAAUP,GAA4C,CAACJ,EAASG,KACpE8H,EAAiBjI,EACjBkI,EAAgB/H,KAQlB,OADAwS,GAA6BtQ,KAAMgK,EALS,CAC1C3E,YAAaJ,GAASW,EAAe,CAAE1H,MAAO+G,EAAOC,MAAM,IAC3DE,YAAaH,GAASW,EAAe,CAAE1H,MAAO+G,EAAOC,MAAM,IAC3Da,YAAaC,GAAKH,EAAcG,KAG3B1H,EAYTyB,cACE,IAAKkQ,GAA2BjQ,MAC9B,MAAMqQ,GAA8B,eAGtC,QAAkC1R,IAA9BqB,KAAK0B,qBAAT,CAIA,GAAI1B,KAAKqN,kBAAkB9M,OAAS,EAClC,MAAM,IAAIb,UAAU,uFAGtB0C,EAAmCpC,iBAmBvBiQ,GAA2B9S,GACzC,QAAKD,EAAaC,OAIb8I,OAAOzI,UAAU0I,eAAezH,KAAKtB,EAAG,sBAItCA,aAAa4S,mCAGNO,GACd9O,EACAwI,EACAoD,GAEA,MAAM3L,EAASD,EAAOE,qBAItBD,EAAO0E,YAAa,EAEE,YAAlB1E,EAAOG,OACTwL,EAAgBrH,YAAYtE,EAAOQ,uBD6TrCuK,EACAxC,EACAoD,GAEA,MAAM3L,EAAS+K,EAAWpB,8BAE1B,IAAIe,EAAc,EACdnC,EAAKuG,cAAgBC,WACvBrE,EAAenC,EAAKuG,YAA8CE,mBAGpE,MAAMC,EAAO1G,EAAKuG,YAGZ1H,EAA6BmB,EAAKnB,OAMlCmD,EAAgD,CACpDnD,OAAAA,EACAoD,iBAAkBpD,EAAOO,WACzBD,WAAYa,EAAKb,WACjBC,WAAYY,EAAKZ,WACjB8C,YAAa,EACbC,YAAAA,EACAC,gBAAiBsE,EACjBrE,WAAY,QAGd,GAAIG,EAAWF,kBAAkB/L,OAAS,EAQxC,OAPAiM,EAAWF,kBAAkB1L,KAAKoL,QAMlCgE,GAAiCvO,EAAQ2L,GAI3C,GAAsB,WAAlB3L,EAAOG,OAAX,CAMA,GAAI4K,EAAW/C,gBAAkB,EAAG,CAClC,GAAI+D,GAA4DhB,EAAYR,GAAqB,CAC/F,MAAMkB,EAAaC,GAAyDnB,GAK5E,OAHAJ,GAA6CY,QAE7CY,EAAgB/H,YAAY6H,GAI9B,GAAIV,EAAWtB,gBAAiB,CAC9B,MAAMlF,EAAI,IAAItG,UAAU,2DAIxB,OAHA6L,GAAkCiB,EAAYxG,QAE9CoH,EAAgBrH,YAAYC,IAKhCwG,EAAWF,kBAAkB1L,KAAKoL,GAElCgE,GAAoCvO,EAAQ2L,GAC5Cb,GAA6CC,OA5B7C,CACE,MAAMmE,EAAY,IAAID,EAAK1E,EAAmBnD,OAAQmD,EAAmB7C,WAAY,GACrFiE,EAAgBhI,YAAYuL,ICvW5BC,CACEnP,EAAO2E,0BACP4D,EACAoD,GAON,SAASiD,GAA8B7N,GACrC,OAAO,IAAI9C,UACT,sCAAsC8C,6DChR1BqO,GAAqBC,EAA2BC,GAC9D,MAAMnB,cAAEA,GAAkBkB,EAE1B,QAAsBnS,IAAlBiR,EACF,OAAOmB,EAGT,GAAI/I,GAAY4H,IAAkBA,EAAgB,EAChD,MAAM,IAAI/F,WAAW,yBAGvB,OAAO+F,WAGOoB,GAAwBF,GACtC,MAAMpH,KAAEA,GAASoH,EAEjB,OAAKpH,GACI,KAAM,YClBDuH,GAA0BC,EACAvN,GACxCF,EAAiByN,EAAMvN,GACvB,MAAMiM,EAAgBsB,MAAAA,SAAAA,EAAMtB,cACtBlG,EAAOwH,MAAAA,SAAAA,EAAMxH,KACnB,MAAO,CACLkG,mBAAiCjR,IAAlBiR,OAA8BjR,EAAYwF,EAA0ByL,GACnFlG,UAAe/K,IAAT+K,OAAqB/K,EAAYwS,GAA2BzH,EAAM,GAAG/F,6BAI/E,SAASwN,GAA8B9R,EACAsE,GAErC,OADAC,EAAevE,EAAIsE,GACZsB,GAASd,EAA0B9E,EAAG4F,ICoB/C,SAASmM,GACP/R,EACAgS,EACA1N,GAGA,OADAC,EAAevE,EAAIsE,GACXvF,GAAgByB,EAAYR,EAAIgS,EAAU,CAACjT,IAGrD,SAASkT,GACPjS,EACAgS,EACA1N,GAGA,OADAC,EAAevE,EAAIsE,GACZ,IAAM9D,EAAYR,EAAIgS,EAAU,IAGzC,SAASE,GACPlS,EACAgS,EACA1N,GAGA,OADAC,EAAevE,EAAIsE,GACX6I,GAAgDlN,EAAYD,EAAIgS,EAAU,CAAC7E,IAGrF,SAASgF,GACPnS,EACAgS,EACA1N,GAGA,OADAC,EAAevE,EAAIsE,GACZ,CAACsB,EAAUuH,IAAgD3M,EAAYR,EAAIgS,EAAU,CAACpM,EAAOuH,aCpEtFiF,GAAqBtU,EAAYwG,GAC/C,IAAK+N,GAAiBvU,GACpB,MAAM,IAAIuC,UAAU,GAAGiE,8BJ0N3BsC,OAAOK,iBAAiByJ,yBAAyBvS,UAAW,CAC1D+I,OAAQ,CAAEC,YAAY,GACtBC,KAAM,CAAED,YAAY,GACpBE,YAAa,CAAEF,YAAY,GAC3Bd,OAAQ,CAAEc,YAAY,KAEU,iBAAvB9J,EAAOiK,aAChBV,OAAOW,eAAemJ,yBAAyBvS,UAAWd,EAAOiK,YAAa,CAC5EzI,MAAO,2BACP2I,cAAc,IK3KlB,MAAM8K,GAA8D,mBAA5BC,gBCFxC,MAAMC,eAuBJ9R,YAAY+R,EAA0D,GAC1DC,EAAqD,SACrCpT,IAAtBmT,EACFA,EAAoB,KAEpBjO,EAAaiO,EAAmB,mBAGlC,MAAMhB,EAAWG,GAAuBc,EAAa,oBAC/CC,WH9E+BX,EACA1N,GACvCF,EAAiB4N,EAAU1N,GAC3B,MAAMsO,EAAQZ,MAAAA,SAAAA,EAAUY,MAClB/B,EAAQmB,MAAAA,SAAAA,EAAUnB,MAClBgC,EAAQb,MAAAA,SAAAA,EAAUa,MAClBC,EAAOd,MAAAA,SAAAA,EAAUc,KACjBC,EAAQf,MAAAA,SAAAA,EAAUe,MACxB,MAAO,CACLH,WAAiBtT,IAAVsT,OACLtT,EACAyS,GAAmCa,EAAOZ,EAAW,GAAG1N,6BAC1DuM,WAAiBvR,IAAVuR,OACLvR,EACA2S,GAAmCpB,EAAOmB,EAAW,GAAG1N,6BAC1DuO,WAAiBvT,IAAVuT,OACLvT,EACA4S,GAAmCW,EAAOb,EAAW,GAAG1N,6BAC1DyO,WAAiBzT,IAAVyT,OACLzT,EACA6S,GAAmCY,EAAOf,EAAW,GAAG1N,6BAC1DwO,KAAAA,GGyDuBE,CAAsBP,EAAmB,mBAEhEQ,GAAyBtS,MAGzB,QAAarB,IADAqT,EAAeG,KAE1B,MAAM,IAAItI,WAAW,6BAGvB,MAAM0I,EAAgBvB,GAAqBF,IAu+B/C,SAAmErP,EACAuQ,EACApC,EACA2C,GACjE,MAAM/F,EAAavG,OAAOkJ,OAAOqD,gCAAgChV,WAEjE,IAAIiS,EAAiD,OACjDgD,EAA8C,IAAMxU,OAAoBU,GACxE+T,EAAsC,IAAMzU,OAAoBU,GAChEgU,EAAiD,IAAM1U,OAAoBU,QAElDA,IAAzBqT,EAAeE,QACjBzC,EAAiB,IAAMuC,EAAeE,MAAO1F,SAElB7N,IAAzBqT,EAAeI,QACjBK,EAAiBxN,GAAS+M,EAAeI,MAAOnN,EAAOuH,SAE5B7N,IAAzBqT,EAAe9B,QACjBwC,EAAiB,IAAMV,EAAe9B,cAEXvR,IAAzBqT,EAAeC,QACjBU,EAAiBvU,GAAU4T,EAAeC,MAAO7T,IAGnDwU,GACEnR,EAAQ+K,EAAYiD,EAAgBgD,EAAgBC,EAAgBC,EAAgB/C,EAAe2C,GA7/BnGM,CAAuD7S,KAAMgS,EAFvCnB,GAAqBC,EAAU,GAEuCyB,GAM9FO,aACE,IAAKpB,GAAiB1R,MACpB,MAAM+S,GAA0B,UAGlC,OAAOC,GAAuBhT,MAYhCD,MAAM3B,GACJ,OAAKsT,GAAiB1R,MAIlBgT,GAAuBhT,MAClB7B,EAAoB,IAAIuB,UAAU,oDAGpCuT,GAAoBjT,KAAM5B,GAPxBD,EAAoB4U,GAA0B,UAkBzDhT,QACE,OAAK2R,GAAiB1R,MAIlBgT,GAAuBhT,MAClB7B,EAAoB,IAAIuB,UAAU,oDAGvCwT,GAAoClT,MAC/B7B,EAAoB,IAAIuB,UAAU,2CAGpCyT,GAAoBnT,MAXlB7B,EAAoB4U,GAA0B,UAsBzDhT,YACE,IAAK2R,GAAiB1R,MACpB,MAAM+S,GAA0B,aAGlC,OAAOK,GAAmCpT,OAsC9C,SAASoT,GAAsC3R,GAC7C,OAAO,IAAI4R,4BAA4B5R,GAsBzC,SAAS6Q,GAA4B7Q,GACnCA,EAAOG,OAAS,WAIhBH,EAAOQ,kBAAetD,EAEtB8C,EAAO6R,aAAU3U,EAIjB8C,EAAO8R,+BAA4B5U,EAInC8C,EAAO+R,eAAiB,IAAI1T,EAI5B2B,EAAOgS,2BAAwB9U,EAI/B8C,EAAOiS,mBAAgB/U,EAIvB8C,EAAOkS,2BAAwBhV,EAG/B8C,EAAOmS,0BAAuBjV,EAG9B8C,EAAOoS,eAAgB,EAGzB,SAASnC,GAAiBvU,GACxB,QAAKD,EAAaC,OAIb8I,OAAOzI,UAAU0I,eAAezH,KAAKtB,EAAG,8BAItCA,aAAa0U,gBAGtB,SAASmB,GAAuBvR,GAG9B,YAAuB9C,IAAnB8C,EAAO6R,QAOb,SAASL,GAAoBxR,EAAwBrD,SACnD,GAAsB,WAAlBqD,EAAOG,QAAyC,YAAlBH,EAAOG,OACvC,OAAO3D,OAAoBU,GAE7B8C,EAAO8R,0BAA0BO,aAAe1V,YAChDqD,EAAO8R,0BAA0BQ,iCAAkB9B,QAKnD,MAAM9G,EAAQ1J,EAAOG,OAErB,GAAc,WAAVuJ,GAAgC,YAAVA,EACxB,OAAOlN,OAAoBU,GAE7B,QAAoCA,IAAhC8C,EAAOmS,qBACT,OAAOnS,EAAOmS,qBAAqBI,SAKrC,IAAIC,GAAqB,EACX,aAAV9I,IACF8I,GAAqB,EAErB7V,OAASO,GAGX,MAAML,EAAUP,GAAsB,CAACJ,EAASG,KAC9C2D,EAAOmS,qBAAuB,CAC5BI,cAAUrV,EACVuV,SAAUvW,EACVwW,QAASrW,EACTsW,QAAShW,EACTiW,oBAAqBJ,MASzB,OANAxS,EAAOmS,qBAAsBI,SAAW1V,EAEnC2V,GACHK,GAA4B7S,EAAQrD,GAG/BE,EAGT,SAAS6U,GAAoB1R,GAC3B,MAAM0J,EAAQ1J,EAAOG,OACrB,GAAc,WAAVuJ,GAAgC,YAAVA,EACxB,OAAOhN,EAAoB,IAAIuB,UAC7B,kBAAkByL,+DAMtB,MAAM7M,EAAUP,GAAsB,CAACJ,EAASG,KAC9C,MAAMyW,EAA6B,CACjCL,SAAUvW,EACVwW,QAASrW,GAGX2D,EAAOiS,cAAgBa,KAGnBC,EAAS/S,EAAO6R,QAywBxB,IAAiD9G,EAlwB/C,YANe7N,IAAX6V,GAAwB/S,EAAOoS,eAA2B,aAAV1I,GAClDsJ,GAAiCD,GAwwBnC7K,GAD+C6C,EApwBV/K,EAAO8R,0BAqwBXmB,GAAe,GAChDC,GAAoDnI,GApwB7ClO,EAqBT,SAASsW,GAAgCnT,EAAwB2O,GAGjD,aAFA3O,EAAOG,OAQrBiT,GAA6BpT,GAL3B6S,GAA4B7S,EAAQ2O,GAQxC,SAASkE,GAA4B7S,EAAwBrD,GAI3D,MAAMoO,EAAa/K,EAAO8R,0BAG1B9R,EAAOG,OAAS,WAChBH,EAAOQ,aAAe7D,EACtB,MAAMoW,EAAS/S,EAAO6R,aACP3U,IAAX6V,GACFM,GAAsDN,EAAQpW,IAoHlE,SAAkDqD,GAChD,QAAqC9C,IAAjC8C,EAAOgS,4BAAwE9U,IAAjC8C,EAAOkS,sBACvD,OAAO,EAGT,OAAO,EAtHFoB,CAAyCtT,IAAW+K,EAAWC,UAClEoI,GAA6BpT,GAIjC,SAASoT,GAA6BpT,GAGpCA,EAAOG,OAAS,UAChBH,EAAO8R,0BAA0B1Q,KAEjC,MAAMmS,EAAcvT,EAAOQ,aAM3B,GALAR,EAAO+R,eAAeyB,SAAQC,IAC5BA,EAAaf,QAAQa,MAEvBvT,EAAO+R,eAAiB,IAAI1T,OAEQnB,IAAhC8C,EAAOmS,qBAET,YADAuB,GAAkD1T,GAIpD,MAAM2T,EAAe3T,EAAOmS,qBAG5B,GAFAnS,EAAOmS,0BAAuBjV,EAE1ByW,EAAaf,oBAGf,OAFAe,EAAajB,QAAQa,QACrBG,GAAkD1T,GAKpD/C,EADgB+C,EAAO8R,0BAA0B3Q,GAAYwS,EAAahB,UAGxE,KACEgB,EAAalB,WACbiB,GAAkD1T,MAEnDrD,IACCgX,EAAajB,QAAQ/V,GACrB+W,GAAkD1T,MAiExD,SAASyR,GAAoCzR,GAC3C,YAA6B9C,IAAzB8C,EAAOiS,oBAAgE/U,IAAjC8C,EAAOkS,sBA4BnD,SAASwB,GAAkD1T,QAE5B9C,IAAzB8C,EAAOiS,gBAGTjS,EAAOiS,cAAcS,QAAQ1S,EAAOQ,cACpCR,EAAOiS,mBAAgB/U,GAEzB,MAAM6V,EAAS/S,EAAO6R,aACP3U,IAAX6V,GACFa,GAAiCb,EAAQ/S,EAAOQ,cAIpD,SAASqT,GAAiC7T,EAAwB8T,GAIhE,MAAMf,EAAS/S,EAAO6R,aACP3U,IAAX6V,GAAwBe,IAAiB9T,EAAOoS,gBAC9C0B,EAozBR,SAAwCf,GAItCgB,GAAoChB,GAvzBhCiB,CAA+BjB,GAI/BC,GAAiCD,IAIrC/S,EAAOoS,cAAgB0B,EA7YzBtP,OAAOK,iBAAiBuL,eAAerU,UAAW,CAChDyU,MAAO,CAAEzL,YAAY,GACrB0J,MAAO,CAAE1J,YAAY,GACrBkP,UAAW,CAAElP,YAAY,GACzBsM,OAAQ,CAAEtM,YAAY,KAEU,iBAAvB9J,EAAOiK,aAChBV,OAAOW,eAAeiL,eAAerU,UAAWd,EAAOiK,YAAa,CAClEzI,MAAO,iBACP2I,cAAc,UA4YLwM,4BAoBXtT,YAAY0B,GAIV,GAHAsC,EAAuBtC,EAAQ,EAAG,+BAClCgQ,GAAqBhQ,EAAQ,mBAEzBuR,GAAuBvR,GACzB,MAAM,IAAI/B,UAAU,+EAGtBM,KAAK2V,qBAAuBlU,EAC5BA,EAAO6R,QAAUtT,KAEjB,MAAMmL,EAAQ1J,EAAOG,OAErB,GAAc,aAAVuJ,GACG+H,GAAoCzR,IAAWA,EAAOoS,cACzD2B,GAAoCxV,MAEpC4V,GAA8C5V,MAGhD6V,GAAqC7V,WAChC,GAAc,aAAVmL,EACT2K,GAA8C9V,KAAMyB,EAAOQ,cAC3D4T,GAAqC7V,WAChC,GAAc,WAAVmL,EACTyK,GAA8C5V,MAmrBlD6V,GADsDrB,EAjrBHxU,MAmrBnD+V,GAAkCvB,OAlrBzB,CAGL,MAAMQ,EAAcvT,EAAOQ,aAC3B6T,GAA8C9V,KAAMgV,GACpDgB,GAA+ChW,KAAMgV,GA2qB3D,IAAwDR,EAnqBtD9O,aACE,OAAKuQ,GAA8BjW,MAI5BA,KAAKyC,eAHHtE,EAAoB+X,GAAiC,WAchElL,kBACE,IAAKiL,GAA8BjW,MACjC,MAAMkW,GAAiC,eAGzC,QAAkCvX,IAA9BqB,KAAK2V,qBACP,MAAMQ,GAA2B,eAGnC,OA2LJ,SAAmD3B,GACjD,MAAM/S,EAAS+S,EAAOmB,qBAChBxK,EAAQ1J,EAAOG,OAErB,GAAc,YAAVuJ,GAAiC,aAAVA,EACzB,OAAO,KAGT,GAAc,WAAVA,EACF,OAAO,EAGT,OAAOiL,GAA8C3U,EAAO8R,2BAvMnD8C,CAA0CrW,MAWnD+N,YACE,OAAKkI,GAA8BjW,MAI5BA,KAAKsW,cAHHnY,EAAoB+X,GAAiC,UAShEnW,MAAM3B,GACJ,OAAK6X,GAA8BjW,WAIDrB,IAA9BqB,KAAK2V,qBACAxX,EAAoBgY,GAA2B,UA4G5D,SAA0C3B,EAAqCpW,GAK7E,OAAO6U,GAJQuB,EAAOmB,qBAIavX,GA9G1BmY,CAAiCvW,KAAM5B,GAPrCD,EAAoB+X,GAAiC,UAahEnW,QACE,IAAKkW,GAA8BjW,MACjC,OAAO7B,EAAoB+X,GAAiC,UAG9D,MAAMzU,EAASzB,KAAK2V,qBAEpB,YAAehX,IAAX8C,EACKtD,EAAoBgY,GAA2B,UAGpDjD,GAAoCzR,GAC/BtD,EAAoB,IAAIuB,UAAU,2CAGpC8W,GAAiCxW,MAa1CD,cACE,IAAKkW,GAA8BjW,MACjC,MAAMkW,GAAiC,oBAK1BvX,IAFAqB,KAAK2V,sBAQpBc,GAAmCzW,MAarCD,MAAMkF,GACJ,OAAKgR,GAA8BjW,WAIDrB,IAA9BqB,KAAK2V,qBACAxX,EAAoBgY,GAA2B,aAGjDO,GAAiC1W,KAAMiF,GAPrC9G,EAAoB+X,GAAiC,WA6BlE,SAASD,GAAuC9Y,GAC9C,QAAKD,EAAaC,OAIb8I,OAAOzI,UAAU0I,eAAezH,KAAKtB,EAAG,yBAItCA,aAAakW,6BAatB,SAASmD,GAAiChC,GAKxC,OAAOrB,GAJQqB,EAAOmB,sBA0BxB,SAASgB,GAAuDnC,EAAqCpE,GAChE,YAA/BoE,EAAOoC,oBACTvB,GAAiCb,EAAQpE,GA+e7C,SAAmDoE,EAAqCpW,GAKtF4X,GAA+CxB,EAAQpW,GAlfrDyY,CAA0CrC,EAAQpE,GAItD,SAAS0E,GAAsDN,EAAqCpE,GAChE,YAA9BoE,EAAOsC,mBACTC,GAAgCvC,EAAQpE,GAgiB5C,SAAkDoE,EAAqCpW,GAIrF0X,GAA8CtB,EAAQpW,GAliBpD4Y,CAAyCxC,EAAQpE,GAmBrD,SAASqG,GAAmCjC,GAC1C,MAAM/S,EAAS+S,EAAOmB,qBAIhBsB,EAAgB,IAAIvX,UACxB,oFAEFoV,GAAsDN,EAAQyC,GAI9DN,GAAuDnC,EAAQyC,GAE/DxV,EAAO6R,aAAU3U,EACjB6V,EAAOmB,0BAAuBhX,EAGhC,SAAS+X,GAAoClC,EAAwCvP,GACnF,MAAMxD,EAAS+S,EAAOmB,qBAIhBnJ,EAAa/K,EAAO8R,0BAEpB2D,EAqPR,SAAwD1K,EACAvH,GACtD,IACE,OAAOuH,EAAW2K,uBAAuBlS,GACzC,MAAOmS,GAEP,OADAC,GAA6C7K,EAAY4K,GAClD,GA3PSE,CAA4C9K,EAAYvH,GAE1E,GAAIxD,IAAW+S,EAAOmB,qBACpB,OAAOxX,EAAoBgY,GAA2B,aAGxD,MAAMhL,EAAQ1J,EAAOG,OACrB,GAAc,YAAVuJ,EACF,OAAOhN,EAAoBsD,EAAOQ,cAEpC,GAAIiR,GAAoCzR,IAAqB,WAAV0J,EACjD,OAAOhN,EAAoB,IAAIuB,UAAU,6DAE3C,GAAc,aAAVyL,EACF,OAAOhN,EAAoBsD,EAAOQ,cAKpC,MAAM3D,EAhiBR,SAAuCmD,GAarC,OATgB1D,GAAsB,CAACJ,EAASG,KAC9C,MAAMoX,EAA6B,CACjChB,SAAUvW,EACVwW,QAASrW,GAGX2D,EAAO+R,eAAe5S,KAAKsU,MAshBbqC,CAA8B9V,GAI9C,OA4OF,SAAiD+K,EACAvH,EACAiS,GAC/C,IACEvN,GAAqB6C,EAAYvH,EAAOiS,GACxC,MAAOM,GAEP,YADAH,GAA6C7K,EAAYgL,GAI3D,MAAM/V,EAAS+K,EAAWiL,0BAC1B,IAAKvE,GAAoCzR,IAA6B,aAAlBA,EAAOG,OAAuB,CAEhF0T,GAAiC7T,EADZiW,GAA+ClL,IAItEmI,GAAoDnI,GA9PpDmL,CAAqCnL,EAAYvH,EAAOiS,GAEjD5Y,EAlJT2H,OAAOK,iBAAiB+M,4BAA4B7V,UAAW,CAC7DyU,MAAO,CAAEzL,YAAY,GACrB0J,MAAO,CAAE1J,YAAY,GACrBE,YAAa,CAAEF,YAAY,GAC3B4L,MAAO,CAAE5L,YAAY,GACrBd,OAAQ,CAAEc,YAAY,GACtBwE,YAAa,CAAExE,YAAY,GAC3BuH,MAAO,CAAEvH,YAAY,KAEW,iBAAvB9J,EAAOiK,aAChBV,OAAOW,eAAeyM,4BAA4B7V,UAAWd,EAAOiK,YAAa,CAC/EzI,MAAO,8BACP2I,cAAc,IAyIlB,MAAM6N,GAA+B,SASxBlC,gCAwBXzS,cACE,MAAM,IAAIL,UAAU,uBAUtBkY,kBACE,IAAKC,GAAkC7X,MACrC,MAAM8X,GAAqC,eAE7C,OAAO9X,KAAK8T,aAMdiE,aACE,IAAKF,GAAkC7X,MACrC,MAAM8X,GAAqC,UAE7C,QAA8BnZ,IAA1BqB,KAAK+T,iBAIP,MAAM,IAAIrU,UAAU,qEAEtB,OAAOM,KAAK+T,iBAAiBgE,OAU/BhY,MAAMiG,GACJ,IAAK6R,GAAkC7X,MACrC,MAAM8X,GAAqC,SAG/B,aADA9X,KAAKyX,0BAA0B7V,QAO7CoW,GAAqChY,KAAMgG,GAI7CjG,CAAC6C,GAAYxE,GACX,MAAMoJ,EAASxH,KAAKiY,gBAAgB7Z,GAEpC,OADA8Z,GAA+ClY,MACxCwH,EAITzH,CAAC8C,KACCiH,GAAW9J,OAkBf,SAAS6X,GAAkC1a,GACzC,QAAKD,EAAaC,OAIb8I,OAAOzI,UAAU0I,eAAezH,KAAKtB,EAAG,8BAItCA,aAAaqV,iCAGtB,SAASI,GAAwCnR,EACA+K,EACAiD,EACAgD,EACAC,EACAC,EACA/C,EACA2C,GAI/C/F,EAAWiL,0BAA4BhW,EACvCA,EAAO8R,0BAA4B/G,EAGnCA,EAAWhD,YAAS7K,EACpB6N,EAAW/C,qBAAkB9K,EAC7BmL,GAAW0C,GAEXA,EAAWsH,kBAAenV,EAC1B6N,EAAWuH,4BDx+BX,GAAIpC,GACF,OAAO,IAAKC,gBCu+BgBuG,GAC9B3L,EAAWC,UAAW,EAEtBD,EAAW2K,uBAAyB5E,EACpC/F,EAAW8C,aAAeM,EAE1BpD,EAAW4L,gBAAkB3F,EAC7BjG,EAAW6L,gBAAkB3F,EAC7BlG,EAAWyL,gBAAkBtF,EAE7B,MAAM4C,EAAemC,GAA+ClL,GACpE8I,GAAiC7T,EAAQ8T,GAIzC7W,EADqBT,EADDwR,MAIlB,KAEEjD,EAAWC,UAAW,EACtBkI,GAAoDnI,MAEtDqD,IAEErD,EAAWC,UAAW,EACtBmI,GAAgCnT,EAAQoO,MAmC9C,SAASqI,GAA+C1L,GACtDA,EAAW4L,qBAAkBzZ,EAC7B6N,EAAW6L,qBAAkB1Z,EAC7B6N,EAAWyL,qBAAkBtZ,EAC7B6N,EAAW2K,4BAAyBxY,EAkBtC,SAASyX,GAA8C5J,GACrD,OAAOA,EAAW8C,aAAe9C,EAAW/C,gBAwB9C,SAASkL,GAAuDnI,GAC9D,MAAM/K,EAAS+K,EAAWiL,0BAE1B,IAAKjL,EAAWC,SACd,OAGF,QAAqC9N,IAAjC8C,EAAOgS,sBACT,OAKF,GAAc,aAFAhS,EAAOG,OAInB,YADAiT,GAA6BpT,GAI/B,GAAiC,IAA7B+K,EAAWhD,OAAOjJ,OACpB,OAGF,MAAMrC,EAAuBsO,ERnoCNhD,OAAO0E,OAClBhQ,MQmoCRA,IAAUwW,GAahB,SAAqDlI,GACnD,MAAM/K,EAAS+K,EAAWiL,2BAvqB5B,SAAgDhW,GAG9CA,EAAOkS,sBAAwBlS,EAAOiS,cACtCjS,EAAOiS,mBAAgB/U,GAqqBvB2Z,CAAuC7W,GAEvC4H,GAAamD,GAGb,MAAM+L,EAAmB/L,EAAW6L,kBACpCH,GAA+C1L,GAC/C9N,EACE6Z,GACA,MA/uBJ,SAA2C9W,GAEzCA,EAAOkS,sBAAuBO,cAASvV,GACvC8C,EAAOkS,2BAAwBhV,EAMjB,aAJA8C,EAAOG,SAMnBH,EAAOQ,kBAAetD,OACcA,IAAhC8C,EAAOmS,uBACTnS,EAAOmS,qBAAqBM,WAC5BzS,EAAOmS,0BAAuBjV,IAIlC8C,EAAOG,OAAS,SAEhB,MAAM4S,EAAS/S,EAAO6R,aACP3U,IAAX6V,GACFuB,GAAkCvB,GA0tBhCgE,CAAkC/W,MAEpCrD,KArtBJ,SAAoDqD,EAAwB2O,GAE1E3O,EAAOkS,sBAAuBQ,QAAQ/D,GACtC3O,EAAOkS,2BAAwBhV,OAKKA,IAAhC8C,EAAOmS,uBACTnS,EAAOmS,qBAAqBO,QAAQ/D,GACpC3O,EAAOmS,0BAAuBjV,GAEhCiW,GAAgCnT,EAAQ2O,GA0sBpCqI,CAA2ChX,EAAQrD,MA5BrDsa,CAA4ClM,GAiChD,SAAwDA,EAAgDvH,GACtG,MAAMxD,EAAS+K,EAAWiL,2BArrB5B,SAAqDhW,GAGnDA,EAAOgS,sBAAwBhS,EAAO+R,eAAerO,QAorBrDwT,CAA4ClX,GAG5C/C,EADyB8N,EAAW4L,gBAAgBnT,IAGlD,MAhxBJ,SAA2CxD,GAEzCA,EAAOgS,sBAAuBS,cAASvV,GACvC8C,EAAOgS,2BAAwB9U,EA8wB3Bia,CAAkCnX,GAElC,MAAM0J,EAAQ1J,EAAOG,OAKrB,GAFAyH,GAAamD,IAER0G,GAAoCzR,IAAqB,aAAV0J,EAAsB,CACxE,MAAMoK,EAAemC,GAA+ClL,GACpE8I,GAAiC7T,EAAQ8T,GAG3CZ,GAAoDnI,MAEtDpO,IACwB,aAAlBqD,EAAOG,QACTsW,GAA+C1L,GA3xBvD,SAAoD/K,EAAwB2O,GAE1E3O,EAAOgS,sBAAuBU,QAAQ/D,GACtC3O,EAAOgS,2BAAwB9U,EAI/BiW,GAAgCnT,EAAQ2O,GAsxBpCyI,CAA2CpX,EAAQrD,MA1DrD0a,CAA4CtM,EAAYtO,GAI5D,SAASmZ,GAA6C7K,EAAkD4D,GAClD,aAAhD5D,EAAWiL,0BAA0B7V,QACvCoW,GAAqCxL,EAAY4D,GAyDrD,SAASsH,GAA+ClL,GAEtD,OADoB4J,GAA8C5J,IAC5C,EAKxB,SAASwL,GAAqCxL,EAAkD4D,GAC9F,MAAM3O,EAAS+K,EAAWiL,0BAI1BS,GAA+C1L,GAC/C8H,GAA4B7S,EAAQ2O,GAKtC,SAAS2C,GAA0BvQ,GACjC,OAAO,IAAI9C,UAAU,4BAA4B8C,0CAKnD,SAASsV,GAAqCtV,GAC5C,OAAO,IAAI9C,UACT,6CAA6C8C,2DAMjD,SAAS0T,GAAiC1T,GACxC,OAAO,IAAI9C,UACT,yCAAyC8C,uDAG7C,SAAS2T,GAA2B3T,GAClC,OAAO,IAAI9C,UAAU,UAAY8C,EAAO,qCAG1C,SAASqT,GAAqCrB,GAC5CA,EAAO/R,eAAiB1E,GAAW,CAACJ,EAASG,KAC3C0W,EAAO9R,uBAAyB/E,EAChC6W,EAAO7R,sBAAwB7E,EAC/B0W,EAAOoC,oBAAsB,aAIjC,SAASZ,GAA+CxB,EAAqCpW,GAC3FyX,GAAqCrB,GACrCa,GAAiCb,EAAQpW,GAQ3C,SAASiX,GAAiCb,EAAqCpW,QACxCO,IAAjC6V,EAAO7R,wBAKX1D,EAA0BuV,EAAO/R,gBACjC+R,EAAO7R,sBAAsBvE,GAC7BoW,EAAO9R,4BAAyB/D,EAChC6V,EAAO7R,2BAAwBhE,EAC/B6V,EAAOoC,oBAAsB,YAW/B,SAASb,GAAkCvB,QACH7V,IAAlC6V,EAAO9R,yBAKX8R,EAAO9R,4BAAuB/D,GAC9B6V,EAAO9R,4BAAyB/D,EAChC6V,EAAO7R,2BAAwBhE,EAC/B6V,EAAOoC,oBAAsB,YAG/B,SAASpB,GAAoChB,GAC3CA,EAAO8B,cAAgBvY,GAAW,CAACJ,EAASG,KAC1C0W,EAAOuE,sBAAwBpb,EAC/B6W,EAAOwE,qBAAuBlb,KAEhC0W,EAAOsC,mBAAqB,UAG9B,SAAShB,GAA8CtB,EAAqCpW,GAC1FoX,GAAoChB,GACpCuC,GAAgCvC,EAAQpW,GAG1C,SAASwX,GAA8CpB,GACrDgB,GAAoChB,GACpCC,GAAiCD,GAGnC,SAASuC,GAAgCvC,EAAqCpW,QACxCO,IAAhC6V,EAAOwE,uBAIX/Z,EAA0BuV,EAAO8B,eACjC9B,EAAOwE,qBAAqB5a,GAC5BoW,EAAOuE,2BAAwBpa,EAC/B6V,EAAOwE,0BAAuBra,EAC9B6V,EAAOsC,mBAAqB,YAiB9B,SAASrC,GAAiCD,QACH7V,IAAjC6V,EAAOuE,wBAIXvE,EAAOuE,2BAAsBpa,GAC7B6V,EAAOuE,2BAAwBpa,EAC/B6V,EAAOwE,0BAAuBra,EAC9B6V,EAAOsC,mBAAqB,aAlY9B7Q,OAAOK,iBAAiBkM,gCAAgChV,UAAW,CACjEoa,YAAa,CAAEpR,YAAY,GAC3BuR,OAAQ,CAAEvR,YAAY,GACtB4J,MAAO,CAAE5J,YAAY,KAEW,iBAAvB9J,EAAOiK,aAChBV,OAAOW,eAAe4L,gCAAgChV,UAAWd,EAAOiK,YAAa,CACnFzI,MAAO,kCACP2I,cAAc,ICrgCX,MAAMoS,GACa,oBAAjBC,aAA+BA,kBAAeva,ECsCvD,MAAMua,GA3BN,SAAmCxI,GACjC,GAAsB,mBAATA,GAAuC,iBAATA,EACzC,OAAO,EAET,IAEE,OADA,IAAKA,GACE,EACP,SACA,OAAO,GAoBTyI,CAA0BF,IAAsBA,GAhBlD,WAEE,MAAMvI,EAAO,SAA0C0I,EAAkB5W,GACvExC,KAAKoZ,QAAUA,GAAW,GAC1BpZ,KAAKwC,KAAOA,GAAQ,QAChB6W,MAAMC,mBACRD,MAAMC,kBAAkBtZ,KAAMA,KAAKuQ,cAKvC,OAFAG,EAAKlT,UAAYyI,OAAOkJ,OAAOkK,MAAM7b,WACrCyI,OAAOW,eAAe8J,EAAKlT,UAAW,cAAe,CAAEU,MAAOwS,EAAM6I,UAAU,EAAM1S,cAAc,IAC3F6J,EAK8D8I,YCbvDC,GAAwBC,EACArR,EACAsR,EACAC,EACA5S,EACA+Q,GAUtC,MAAMvW,EAASmD,EAAsC+U,GAC/ClF,EAASpB,GAAsC/K,GAErDqR,EAAOvT,YAAa,EAEpB,IAAI0T,GAAe,EAGfC,EAAe7b,OAA0BU,GAE7C,OAAOZ,GAAW,CAACJ,EAASG,KAC1B,IAAI6U,EACJ,QAAehU,IAAXoZ,EAAsB,CAuBxB,GAtBApF,EAAiB,KACf,MAAMvC,EAAQ,IAAI8I,GAAa,UAAW,cACpCa,EAAsC,GACvCH,GACHG,EAAQnZ,MAAK,IACS,aAAhByH,EAAKzG,OACAqR,GAAoB5K,EAAM+H,GAE5BnS,OAAoBU,KAG1BqI,GACH+S,EAAQnZ,MAAK,IACW,aAAlB8Y,EAAO9X,OACFO,GAAqBuX,EAAQtJ,GAE/BnS,OAAoBU,KAG/Bqb,GAAmB,IAAM1c,QAAQ2c,IAAIF,EAAQG,KAAIC,GAAUA,SAAY,EAAM/J,IAG3E2H,EAAOqC,QAET,YADAzH,IAIFoF,EAAOsC,iBAAiB,QAAS1H,GAwGnC,IAA2BlR,EAAyCnD,EAAwB6b,EAhC5F,GA3BAG,EAAmBZ,EAAQlY,EAAOiB,gBAAgBuS,IAC3C4E,EAGHW,GAAS,EAAMvF,GAFfgF,GAAmB,IAAM/G,GAAoB5K,EAAM2M,KAAc,EAAMA,MAO3EsF,EAAmBjS,EAAMmM,EAAO/R,gBAAgBuS,IACzChO,EAGHuT,GAAS,EAAMvF,GAFfgF,GAAmB,IAAM7X,GAAqBuX,EAAQ1E,KAAc,EAAMA,MAgDnDvT,EAzCTiY,EAyCkDpb,EAzC1CkD,EAAOiB,eAyC2D0X,EAzC3C,KAC1CR,EAGHY,IAFAP,GAAmB,IHgqB3B,SAA8DxF,GAC5D,MAAM/S,EAAS+S,EAAOmB,qBAIhBxK,EAAQ1J,EAAOG,OACrB,OAAIsR,GAAoCzR,IAAqB,WAAV0J,EAC1ClN,OAAoBU,GAGf,YAAVwM,EACKhN,EAAoBsD,EAAOQ,cAK7BuU,GAAiChC,GGhrBTgG,CAAqDhG,MAwC1D,WAAlB/S,EAAOG,OACTuY,IAEAvb,EAAgBN,EAAS6b,GApCzBjH,GAAoC7K,IAAyB,WAAhBA,EAAKzG,OAAqB,CACzE,MAAM6Y,EAAa,IAAI/a,UAAU,+EAE5BsH,EAGHuT,GAAS,EAAME,GAFfT,GAAmB,IAAM7X,GAAqBuX,EAAQe,KAAa,EAAMA,GAQ7E,SAASC,IAGP,MAAMC,EAAkBb,EACxB,OAAOzb,EACLyb,GACA,IAAMa,IAAoBb,EAAeY,SAA0B/b,IAIvE,SAAS2b,EAAmB7Y,EACAnD,EACA6b,GACJ,YAAlB1Y,EAAOG,OACTuY,EAAO1Y,EAAOQ,cAEdpD,EAAcP,EAAS6b,GAY3B,SAASH,EAAmBG,EAAgCS,EAA2BC,GAYrF,SAASC,IACPpc,EACEyb,KACA,IAAMY,EAASH,EAAiBC,KAChCG,GAAYD,GAAS,EAAMC,KAf3BnB,IAGJA,GAAe,EAEK,aAAhBxR,EAAKzG,QAA0BsR,GAAoC7K,GAGrEyS,IAFAlc,EAAgB8b,IAAyBI,IAc7C,SAASP,EAASU,EAAmB7K,GAC/ByJ,IAGJA,GAAe,EAEK,aAAhBxR,EAAKzG,QAA0BsR,GAAoC7K,GAGrE0S,EAASE,EAAS7K,GAFlBxR,EAAgB8b,KAAyB,IAAMK,EAASE,EAAS7K,MAMrE,SAAS2K,EAASE,EAAmB7K,GACnCqG,GAAmCjC,GACnCpS,EAAmCZ,QAEpB7C,IAAXoZ,GACFA,EAAOmD,oBAAoB,QAASvI,GAElCsI,EACFnd,EAAOsS,GAEPzS,OAAQgB,GA1EZM,EA3ESlB,GAAiB,CAACod,EAAaC,MACpC,SAASxT,EAAK1C,GACRA,EACFiW,IAIA9c,EASFwb,EACK5b,GAAoB,GAGtBI,EAAmBmW,EAAO8B,eAAe,IACvCvY,GAAoB,CAACsd,EAAaC,KACvCxV,EACEtE,EACA,CACE6D,YAAaJ,IACX6U,EAAezb,EAAmBqY,GAAiClC,EAAQvP,QAAQtG,EAAW9B,GAC9Fwe,GAAY,IAEdjW,YAAa,IAAMiW,GAAY,GAC/BtV,YAAauV,SAvBgB1T,EAAMwT,GAIzCxT,EAAK,gBChFA2T,gCAwBXxb,cACE,MAAM,IAAIL,UAAU,uBAOtBsL,kBACE,IAAKwQ,GAAkCxb,MACrC,MAAM8X,GAAqC,eAG7C,OAAO2D,GAA8Czb,MAOvDD,QACE,IAAKyb,GAAkCxb,MACrC,MAAM8X,GAAqC,SAG7C,IAAK4D,GAAiD1b,MACpD,MAAM,IAAIN,UAAU,mDAGtBic,GAAqC3b,MAOvCD,QAAQkF,GACN,IAAKuW,GAAkCxb,MACrC,MAAM8X,GAAqC,WAG7C,IAAK4D,GAAiD1b,MACpD,MAAM,IAAIN,UAAU,qDAGtB,OAAOkc,GAAuC5b,KAAMiF,GAMtDlF,MAAMiG,GACJ,IAAKwV,GAAkCxb,MACrC,MAAM8X,GAAqC,SAG7C+D,GAAqC7b,KAAMgG,GAI7CjG,CAAC+C,GAAa1E,GACZ0L,GAAW9J,MACX,MAAMwH,EAASxH,KAAKyL,iBAAiBrN,GAErC,OADA0d,GAA+C9b,MACxCwH,EAITzH,CAACgD,GAAW+B,GACV,MAAMrD,EAASzB,KAAK+b,0BAEpB,GAAI/b,KAAKwJ,OAAOjJ,OAAS,EAAG,CAC1B,MAAM0E,EAAQoE,GAAarJ,MAEvBA,KAAKkL,iBAA0C,IAAvBlL,KAAKwJ,OAAOjJ,QACtCub,GAA+C9b,MAC/CsO,GAAoB7M,IAEpBua,GAAgDhc,MAGlD8E,EAAYO,YAAYJ,QAExBJ,EAA6BpD,EAAQqD,GACrCkX,GAAgDhc,OAoBtD,SAASwb,GAA2Cre,GAClD,QAAKD,EAAaC,OAIb8I,OAAOzI,UAAU0I,eAAezH,KAAKtB,EAAG,8BAItCA,aAAaoe,iCAGtB,SAASS,GAAgDxP,GAEvD,IADmByP,GAA8CzP,GAE/D,OAGF,GAAIA,EAAWK,SAEb,YADAL,EAAWM,YAAa,GAM1BN,EAAWK,UAAW,EAGtBnO,EADoB8N,EAAWO,kBAG7B,KACEP,EAAWK,UAAW,EAElBL,EAAWM,aACbN,EAAWM,YAAa,EACxBkP,GAAgDxP,OAGpDxG,IACE6V,GAAqCrP,EAAYxG,MAKvD,SAASiW,GAA8CzP,GACrD,MAAM/K,EAAS+K,EAAWuP,0BAE1B,IAAKL,GAAiDlP,GACpD,OAAO,EAGT,IAAKA,EAAWC,SACd,OAAO,EAGT,GAAIhH,GAAuBhE,IAAW6D,EAAiC7D,GAAU,EAC/E,OAAO,EAKT,OAFoBga,GAA8CjP,GAE/C,EAOrB,SAASsP,GAA+CtP,GACtDA,EAAWO,oBAAiBpO,EAC5B6N,EAAWf,sBAAmB9M,EAC9B6N,EAAW2K,4BAAyBxY,WAKtBgd,GAAqCnP,GACnD,IAAKkP,GAAiDlP,GACpD,OAGF,MAAM/K,EAAS+K,EAAWuP,0BAE1BvP,EAAWtB,iBAAkB,EAEI,IAA7BsB,EAAWhD,OAAOjJ,SACpBub,GAA+CtP,GAC/C8B,GAAoB7M,aAIRma,GACdpP,EACAvH,GAEA,IAAKyW,GAAiDlP,GACpD,OAGF,MAAM/K,EAAS+K,EAAWuP,0BAE1B,GAAItW,GAAuBhE,IAAW6D,EAAiC7D,GAAU,EAC/EuD,EAAiCvD,EAAQwD,GAAO,OAC3C,CACL,IAAIiS,EACJ,IACEA,EAAY1K,EAAW2K,uBAAuBlS,GAC9C,MAAOmS,GAEP,MADAyE,GAAqCrP,EAAY4K,GAC3CA,EAGR,IACEzN,GAAqB6C,EAAYvH,EAAOiS,GACxC,MAAOM,GAEP,MADAqE,GAAqCrP,EAAYgL,GAC3CA,GAIVwE,GAAgDxP,YAGlCqP,GAAqCrP,EAAkDxG,GACrG,MAAMvE,EAAS+K,EAAWuP,0BAEJ,aAAlBta,EAAOG,SAIXkI,GAAW0C,GAEXsP,GAA+CtP,GAC/C0C,GAAoBzN,EAAQuE,aAGdyV,GACdjP,GAEA,MAAMrB,EAAQqB,EAAWuP,0BAA0Bna,OAEnD,MAAc,YAAVuJ,EACK,KAEK,WAAVA,EACK,EAGFqB,EAAW8C,aAAe9C,EAAW/C,yBAc9BiS,GACdlP,GAEA,MAAMrB,EAAQqB,EAAWuP,0BAA0Bna,OAEnD,OAAK4K,EAAWtB,iBAA6B,aAAVC,WAOrB+Q,GAAwCza,EACA+K,EACAiD,EACAC,EACAC,EACAC,EACA2C,GAGtD/F,EAAWuP,0BAA4Bta,EAEvC+K,EAAWhD,YAAS7K,EACpB6N,EAAW/C,qBAAkB9K,EAC7BmL,GAAW0C,GAEXA,EAAWC,UAAW,EACtBD,EAAWtB,iBAAkB,EAC7BsB,EAAWM,YAAa,EACxBN,EAAWK,UAAW,EAEtBL,EAAW2K,uBAAyB5E,EACpC/F,EAAW8C,aAAeM,EAE1BpD,EAAWO,eAAiB2C,EAC5BlD,EAAWf,iBAAmBkE,EAE9BlO,EAAO2E,0BAA4BoG,EAGnC9N,EACET,EAFkBwR,MAGlB,KACEjD,EAAWC,UAAW,EAKtBuP,GAAgDxP,MAElDqD,IACEgM,GAAqCrP,EAAYqD,MAkCvD,SAASiI,GAAqCtV,GAC5C,OAAO,IAAI9C,UACT,6CAA6C8C,oECrWjC2Z,GAAqB1a,EACA2a,GAGnC,OAAIvR,GAA+BpJ,EAAO2E,oCAqIN3E,GAIpC,IAMI4a,EACAC,EACAC,EACAC,EAEAC,EAXAjb,EAA2CmD,EAAmClD,GAC9Eib,GAAU,EACVC,GAAsB,EACtBC,GAAsB,EACtBC,GAAY,EACZC,GAAY,EAOhB,MAAMC,EAAgBhf,GAAiBJ,IACrC8e,EAAuB9e,KAGzB,SAASqf,EAAmBC,GAC1Bpe,EAAcoe,EAAWxa,gBAAgBoN,IACnCoN,IAAezb,IAGnB+J,GAAkCgR,EAAQnW,0BAA2ByJ,GACrEtE,GAAkCiR,EAAQpW,0BAA2ByJ,GAChEgN,GAAcC,GACjBL,OAAqB9d,OAK3B,SAASue,IACHjN,GAA2BzO,KAE7BY,EAAmCZ,GAEnCA,EAASmD,EAAmClD,GAC5Cub,EAAmBxb,IA8DrBsE,EAAgCtE,EA3Da,CAC3C6D,YAAaJ,IAIX/F,GAAe,KACbyd,GAAsB,EACtBC,GAAsB,EAEtB,MAAMO,EAASlY,EACf,IAAImY,EAASnY,EACb,IAAK4X,IAAcC,EACjB,IACEM,EAASnU,GAAkBhE,GAC3B,MAAOoY,GAIP,OAHA9R,GAAkCgR,EAAQnW,0BAA2BiX,GACrE9R,GAAkCiR,EAAQpW,0BAA2BiX,QACrEZ,EAAqBta,GAAqBV,EAAQ4b,IAKjDR,GACHvR,GAAoCiR,EAAQnW,0BAA2B+W,GAEpEL,GACHxR,GAAoCkR,EAAQpW,0BAA2BgX,GAGzEV,GAAU,EACNC,EACFW,IACSV,GACTW,QAINnY,YAAa,KACXsX,GAAU,EACLG,GACHxR,GAAkCkR,EAAQnW,2BAEvC0W,GACHzR,GAAkCmR,EAAQpW,2BAExCmW,EAAQnW,0BAA0BkG,kBAAkB/L,OAAS,GAC/D+J,GAAoCiS,EAAQnW,0BAA2B,GAErEoW,EAAQpW,0BAA0BkG,kBAAkB/L,OAAS,GAC/D+J,GAAoCkS,EAAQpW,0BAA2B,GAEpEyW,GAAcC,GACjBL,OAAqB9d,IAGzBoH,YAAa,KACX2W,GAAU,KAMhB,SAASc,EAAmBxT,EAAuByT,GAC7CjY,EAA0ChE,KAE5CY,EAAmCZ,GAEnCA,EAASsO,GAAgCrO,GACzCub,EAAmBxb,IAGrB,MAAMkc,EAAaD,EAAajB,EAAUD,EACpCoB,EAAcF,EAAalB,EAAUC,EAwE3ClM,GAA6B9O,EAAQwI,EAtEqB,CACxD3E,YAAaJ,IAIX/F,GAAe,KACbyd,GAAsB,EACtBC,GAAsB,EAEtB,MAAMgB,EAAeH,EAAaX,EAAYD,EAG9C,GAFsBY,EAAaZ,EAAYC,EAgBnCc,GACVpT,GAA+CkT,EAAWtX,0BAA2BnB,OAfnE,CAClB,IAAI4Y,EACJ,IACEA,EAAc5U,GAAkBhE,GAChC,MAAOoY,GAIP,OAHA9R,GAAkCmS,EAAWtX,0BAA2BiX,GACxE9R,GAAkCoS,EAAYvX,0BAA2BiX,QACzEZ,EAAqBta,GAAqBV,EAAQ4b,IAG/CO,GACHpT,GAA+CkT,EAAWtX,0BAA2BnB,GAEvFqG,GAAoCqS,EAAYvX,0BAA2ByX,GAK7EnB,GAAU,EACNC,EACFW,IACSV,GACTW,QAINnY,YAAaH,IACXyX,GAAU,EAEV,MAAMkB,EAAeH,EAAaX,EAAYD,EACxCiB,EAAgBL,EAAaZ,EAAYC,EAE1Cc,GACHvS,GAAkCqS,EAAWtX,2BAE1C0X,GACHzS,GAAkCsS,EAAYvX,gCAGlCzH,IAAVsG,IAGG2Y,GACHpT,GAA+CkT,EAAWtX,0BAA2BnB,IAElF6Y,GAAiBH,EAAYvX,0BAA0BkG,kBAAkB/L,OAAS,GACrF+J,GAAoCqT,EAAYvX,0BAA2B,IAI1EwX,GAAiBE,GACpBrB,OAAqB9d,IAGzBoH,YAAa,KACX2W,GAAU,KAMhB,SAASY,IACP,GAAIZ,EAEF,OADAC,GAAsB,EACf1e,OAAoBU,GAG7B+d,GAAU,EAEV,MAAM9R,EAAcG,GAA2CwR,EAAQnW,2BAOvE,OANoB,OAAhBwE,EACFsS,IAEAM,EAAmB5S,EAAYT,OAAQ,GAGlClM,OAAoBU,GAG7B,SAAS4e,IACP,GAAIb,EAEF,OADAE,GAAsB,EACf3e,OAAoBU,GAG7B+d,GAAU,EAEV,MAAM9R,EAAcG,GAA2CyR,EAAQpW,2BAOvE,OANoB,OAAhBwE,EACFsS,IAEAM,EAAmB5S,EAAYT,OAAQ,GAGlClM,OAAoBU,GAG7B,SAASof,EAAiB3f,GAGxB,GAFAye,GAAY,EACZR,EAAUje,EACN0e,EAAW,CACb,MAAMkB,EAAkB9V,GAAoB,CAACmU,EAASC,IAChD2B,EAAe9b,GAAqBV,EAAQuc,GAClDvB,EAAqBwB,GAEvB,OAAOlB,EAGT,SAASmB,EAAiB9f,GAGxB,GAFA0e,GAAY,EACZR,EAAUle,EACNye,EAAW,CACb,MAAMmB,EAAkB9V,GAAoB,CAACmU,EAASC,IAChD2B,EAAe9b,GAAqBV,EAAQuc,GAClDvB,EAAqBwB,GAEvB,OAAOlB,EAGT,SAAStN,KAST,OALA8M,EAAU4B,GAAyB1O,EAAgB6N,EAAgBS,GACnEvB,EAAU2B,GAAyB1O,EAAgB8N,EAAgBW,GAEnElB,EAAmBxb,GAEZ,CAAC+a,EAASC,GApYR4B,CAAsB3c,YAMWA,EACA2a,GAI1C,MAAM5a,EAASmD,EAAsClD,GAErD,IAII4a,EACAC,EACAC,EACAC,EAEAC,EATAC,GAAU,EACV2B,GAAY,EACZxB,GAAY,EACZC,GAAY,EAOhB,MAAMC,EAAgBhf,GAAsBJ,IAC1C8e,EAAuB9e,KAGzB,SAAS+R,IACP,GAAIgN,EAEF,OADA2B,GAAY,EACLpgB,OAAoBU,GAG7B+d,GAAU,EAwDV,OAFA5W,EAAgCtE,EApDI,CAClC6D,YAAaJ,IAIX/F,GAAe,KACbmf,GAAY,EACZ,MAAMlB,EAASlY,EACTmY,EAASnY,EAQV4X,GACHjB,GACEW,EAAQnW,0BACR+W,GAGCL,GACHlB,GACEY,EAAQpW,0BACRgX,GAIJV,GAAU,EACN2B,GACF3O,QAINtK,YAAa,KACXsX,GAAU,EACLG,GACHlB,GAAqCY,EAAQnW,2BAE1C0W,GACHnB,GAAqCa,EAAQpW,2BAG1CyW,GAAcC,GACjBL,OAAqB9d,IAGzBoH,YAAa,KACX2W,GAAU,KAKPze,OAAoBU,GAG7B,SAASof,EAAiB3f,GAGxB,GAFAye,GAAY,EACZR,EAAUje,EACN0e,EAAW,CACb,MAAMkB,EAAkB9V,GAAoB,CAACmU,EAASC,IAChD2B,EAAe9b,GAAqBV,EAAQuc,GAClDvB,EAAqBwB,GAEvB,OAAOlB,EAGT,SAASmB,EAAiB9f,GAGxB,GAFA0e,GAAY,EACZR,EAAUle,EACNye,EAAW,CACb,MAAMmB,EAAkB9V,GAAoB,CAACmU,EAASC,IAChD2B,EAAe9b,GAAqBV,EAAQuc,GAClDvB,EAAqBwB,GAEvB,OAAOlB,EAGT,SAAStN,KAeT,OAXA8M,EAAU+B,GAAqB7O,EAAgBC,EAAeqO,GAC9DvB,EAAU8B,GAAqB7O,EAAgBC,EAAewO,GAE9Drf,EAAc2C,EAAOiB,gBAAiBoN,IACpCgM,GAAqCU,EAAQnW,0BAAiEyJ,GAC9GgM,GAAqCW,EAAQpW,0BAAiEyJ,GACzGgN,GAAcC,GACjBL,OAAqB9d,MAIlB,CAAC4d,EAASC,GA9HV+B,CAAyB9c,GCNlC,SAAS+c,GACPnf,EACAgS,EACA1N,GAGA,OADAC,EAAevE,EAAIsE,GACXvF,GAAgByB,EAAYR,EAAIgS,EAAU,CAACjT,IAGrD,SAASqgB,GACPpf,EACAgS,EACA1N,GAGA,OADAC,EAAevE,EAAIsE,GACX6I,GAA4C3M,EAAYR,EAAIgS,EAAU,CAAC7E,IAGjF,SAASkS,GACPrf,EACAgS,EACA1N,GAGA,OADAC,EAAevE,EAAIsE,GACX6I,GAA4ClN,EAAYD,EAAIgS,EAAU,CAAC7E,IAGjF,SAASmS,GAA0BxM,EAAcxO,GAE/C,GAAa,WADbwO,EAAO,GAAGA,KAER,MAAM,IAAIzS,UAAU,GAAGiE,MAAYwO,8DAErC,OAAOA,EChET,SAASyM,GAAgCC,EAAclb,GAErD,GAAa,UADbkb,EAAO,GAAGA,KAER,MAAM,IAAInf,UAAU,GAAGiE,MAAYkb,oEAErC,OAAOA,WCbOC,GAAmBC,EACApb,GACjCF,EAAiBsb,EAASpb,GAC1B,MAAMiW,EAAemF,MAAAA,SAAAA,EAASnF,aACxB5S,EAAgB+X,MAAAA,SAAAA,EAAS/X,cACzB2S,EAAeoF,MAAAA,SAAAA,EAASpF,aACxB5B,EAASgH,MAAAA,SAAAA,EAAShH,OAIxB,YAHepZ,IAAXoZ,GAWN,SAA2BA,EAAiBpU,GAC1C,aTK4BzF,GAC5B,GAAqB,iBAAVA,GAAgC,OAAVA,EAC/B,OAAO,EAET,IACE,MAAiD,kBAAlCA,EAAsBkc,QACrC,SAEA,OAAO,GSbJ4E,CAAcjH,GACjB,MAAM,IAAIrY,UAAU,GAAGiE,4BAZvBsb,CAAkBlH,EAAQ,GAAGpU,8BAExB,CACLiW,aAAcsF,QAAQtF,GACtB5S,cAAekY,QAAQlY,GACvB2S,aAAcuF,QAAQvF,GACtB5B,OAAAA,GJoHJ9R,OAAOK,iBAAiBiV,gCAAgC/d,UAAW,CACjE0S,MAAO,CAAE1J,YAAY,GACrB2J,QAAS,CAAE3J,YAAY,GACvB4J,MAAO,CAAE5J,YAAY,GACrBwE,YAAa,CAAExE,YAAY,KAEK,iBAAvB9J,EAAOiK,aAChBV,OAAOW,eAAe2U,gCAAgC/d,UAAWd,EAAOiK,YAAa,CACnFzI,MAAO,kCACP2I,cAAc,UKpELsY,eAcXpf,YAAYqf,EAAqF,GACrFrN,EAAqD,SACnCpT,IAAxBygB,EACFA,EAAsB,KAEtBvb,EAAaub,EAAqB,mBAGpC,MAAMtO,EAAWG,GAAuBc,EAAa,oBAC/CsN,WHpFR3F,EACA/V,GAEAF,EAAiBiW,EAAQ/V,GACzB,MAAM0N,EAAWqI,EACX7N,EAAwBwF,MAAAA,SAAAA,EAAUxF,sBAClCtF,EAAS8K,MAAAA,SAAAA,EAAU9K,OACnB+Y,EAAOjO,MAAAA,SAAAA,EAAUiO,KACjBpN,EAAQb,MAAAA,SAAAA,EAAUa,MAClBC,EAAOd,MAAAA,SAAAA,EAAUc,KACvB,MAAO,CACLtG,2BAAiDlN,IAA1BkN,OACrBlN,EACA0F,EACEwH,EACA,GAAGlI,6CAEP4C,YAAmB5H,IAAX4H,OACN5H,EACA6f,GAAsCjY,EAAQ8K,EAAW,GAAG1N,8BAC9D2b,UAAe3gB,IAAT2gB,OACJ3gB,EACA8f,GAAoCa,EAAMjO,EAAW,GAAG1N,4BAC1DuO,WAAiBvT,IAAVuT,OACLvT,EACA+f,GAAqCxM,EAAOb,EAAW,GAAG1N,6BAC5DwO,UAAexT,IAATwT,OAAqBxT,EAAYggB,GAA0BxM,EAAM,GAAGxO,6BG0DjD4b,CAAqCH,EAAqB,mBAInF,GAFAI,GAAyBxf,MAEK,UAA1Bqf,EAAiBlN,KAAkB,CACrC,QAAsBxT,IAAlBmS,EAASpH,KACX,MAAM,IAAIG,WAAW,wEhB23B3BpI,EACAge,EACA7P,GAEA,MAAMpD,EAA2CvG,OAAOkJ,OAAOxE,6BAA6BnN,WAE5F,IAAIiS,EAAiD,OACjDC,EAAqC,IAAMzR,OAAoBU,GAC/DgR,EAAkD,IAAM1R,OAAoBU,QAE7CA,IAA/B8gB,EAAqBvN,QACvBzC,EAAiB,IAAMgQ,EAAqBvN,MAAO1F,SAEnB7N,IAA9B8gB,EAAqBH,OACvB5P,EAAgB,IAAM+P,EAAqBH,KAAM9S,SAEf7N,IAAhC8gB,EAAqBlZ,SACvBoJ,EAAkBvR,GAAUqhB,EAAqBlZ,OAAQnI,IAG3D,MAAMyN,EAAwB4T,EAAqB5T,sBACnD,GAA8B,IAA1BA,EACF,MAAM,IAAInM,UAAU,gDAGtB8P,GACE/N,EAAQ+K,EAAYiD,EAAgBC,EAAeC,EAAiBC,EAAe/D,GgBl5BjF6T,CACE1f,KACAqf,EAHoBxO,GAAqBC,EAAU,QAMhD,CAEL,MAAMyB,EAAgBvB,GAAqBF,aL+P/CrP,EACA4d,EACAzP,EACA2C,GAEA,MAAM/F,EAAiDvG,OAAOkJ,OAAOoM,gCAAgC/d,WAErG,IAAIiS,EAAiD,OACjDC,EAAqC,IAAMzR,OAAoBU,GAC/DgR,EAAkD,IAAM1R,OAAoBU,QAEjDA,IAA3B0gB,EAAiBnN,QACnBzC,EAAiB,IAAM4P,EAAiBnN,MAAO1F,SAEnB7N,IAA1B0gB,EAAiBC,OACnB5P,EAAgB,IAAM2P,EAAiBC,KAAM9S,SAEf7N,IAA5B0gB,EAAiB9Y,SACnBoJ,EAAkBvR,GAAUihB,EAAiB9Y,OAAQnI,IAGvD8d,GACEza,EAAQ+K,EAAYiD,EAAgBC,EAAeC,EAAiBC,EAAe2C,GKnRjFoN,CACE3f,KACAqf,EAHoBxO,GAAqBC,EAAU,GAKnDyB,IAQNO,aACE,IAAKpO,GAAiB1E,MACpB,MAAM+S,GAA0B,UAGlC,OAAOtN,GAAuBzF,MAShCD,OAAO3B,GACL,OAAKsG,GAAiB1E,MAIlByF,GAAuBzF,MAClB7B,EAAoB,IAAIuB,UAAU,qDAGpCyC,GAAqBnC,KAAM5B,GAPzBD,EAAoB4U,GAA0B,WA6BzDhT,UACE6f,GAEA,IAAKlb,GAAiB1E,MACpB,MAAM+S,GAA0B,aAKlC,YAAqBpU,aFnLYogB,EACApb,GACnCF,EAAiBsb,EAASpb,GAC1B,MAAMkb,EAAOE,MAAAA,SAAAA,EAASF,KACtB,MAAO,CACLA,UAAelgB,IAATkgB,OAAqBlgB,EAAYigB,GAAgCC,EAAM,GAAGlb,6BE4KhEkc,CAAqBD,EAAY,mBAErCf,KACHla,EAAmC3E,MAIrC8P,GAAgC9P,MAczCD,YACE+f,EACAF,EAAmD,IAEnD,IAAKlb,GAAiB1E,MACpB,MAAM+S,GAA0B,eAElChP,EAAuB+b,EAAc,EAAG,eAExC,MAAMC,WC3MRxW,EACA5F,GAEAF,EAAiB8F,EAAM5F,GAEvB,MAAMqc,EAAWzW,MAAAA,SAAAA,EAAMyW,SACvB/b,EAAoB+b,EAAU,WAAY,wBAC1Cvb,EAAqBub,EAAU,GAAGrc,gCAElC,MAAM4V,EAAWhQ,MAAAA,SAAAA,EAAMgQ,SAIvB,OAHAtV,EAAoBsV,EAAU,WAAY,wBAC1C9H,GAAqB8H,EAAU,GAAG5V,gCAE3B,CAAEqc,SAAAA,EAAUzG,SAAAA,GD8LC0G,CAA4BH,EAAc,mBACtDf,EAAUD,GAAmBc,EAAY,oBAE/C,GAAIna,GAAuBzF,MACzB,MAAM,IAAIN,UAAU,kFAEtB,GAAIsT,GAAuB+M,EAAUxG,UACnC,MAAM,IAAI7Z,UAAU,kFAStB,OAFAT,EAJgBwa,GACdzZ,KAAM+f,EAAUxG,SAAUwF,EAAQpF,aAAcoF,EAAQnF,aAAcmF,EAAQ/X,cAAe+X,EAAQhH,SAKhGgI,EAAUC,SAWnBjgB,OAAOmgB,EACAN,EAAmD,IACxD,IAAKlb,GAAiB1E,MACpB,OAAO7B,EAAoB4U,GAA0B,WAGvD,QAAoBpU,IAAhBuhB,EACF,OAAO/hB,EAAoB,wCAE7B,IAAKuT,GAAiBwO,GACpB,OAAO/hB,EACL,IAAIuB,UAAU,8EAIlB,IAAIqf,EACJ,IACEA,EAAUD,GAAmBc,EAAY,oBACzC,MAAO5Z,GACP,OAAO7H,EAAoB6H,GAG7B,OAAIP,GAAuBzF,MAClB7B,EACL,IAAIuB,UAAU,8EAGdsT,GAAuBkN,GAClB/hB,EACL,IAAIuB,UAAU,8EAIX+Z,GACLzZ,KAAMkgB,EAAanB,EAAQpF,aAAcoF,EAAQnF,aAAcmF,EAAQ/X,cAAe+X,EAAQhH,QAelGhY,MACE,IAAK2E,GAAiB1E,MACpB,MAAM+S,GAA0B,OAIlC,OAAO7K,GADUiU,GAAkBnc,OAgBrCD,OAAO6f,GACL,IAAKlb,GAAiB1E,MACpB,MAAM+S,GAA0B,UAIlC,gBrB5JkDtR,EACAuF,GACpD,MAAMxF,EAASmD,EAAsClD,GAC/C0e,EAAO,IAAIpZ,GAAgCvF,EAAQwF,GACnDrK,EAAmDsJ,OAAOkJ,OAAO1H,IAEvE,OADA9K,EAASgL,mBAAqBwY,EACvBxjB,EqBsJEyjB,CAAsCpgB,cEnTV+e,EACApb,GACrCF,EAAiBsb,EAASpb,GAC1B,MAAMqD,EAAgB+X,MAAAA,SAAAA,EAAS/X,cAC/B,MAAO,CAAEA,cAAekY,QAAQlY,IF8SdqZ,CAAuBT,EAAY,mBACQ5Y,yBAmD/CsX,GAAwB7O,EACAC,EACAC,EACAC,EAAgB,EAChB2C,EAAgD,KAAM,IAG5F,MAAM9Q,EAA4BwE,OAAOkJ,OAAOgQ,eAAe3hB,WAC/DgiB,GAAyB/d,GAOzB,OAJAya,GACEza,EAFqDwE,OAAOkJ,OAAOoM,gCAAgC/d,WAE/EiS,EAAgBC,EAAeC,EAAiBC,EAAe2C,GAG9E9Q,WAIO0c,GACd1O,EACAC,EACAC,GAEA,MAAMlO,EAA6BwE,OAAOkJ,OAAOgQ,eAAe3hB,WAChEgiB,GAAyB/d,GAKzB,OAFA+N,GAAkC/N,EADewE,OAAOkJ,OAAOxE,6BAA6BnN,WACtCiS,EAAgBC,EAAeC,EAAiB,OAAGhR,GAElG8C,EAGT,SAAS+d,GAAyB/d,GAChCA,EAAOG,OAAS,WAChBH,EAAOE,aAAUhD,EACjB8C,EAAOQ,kBAAetD,EACtB8C,EAAO0E,YAAa,WAGNzB,GAAiBvH,GAC/B,QAAKD,EAAaC,OAIb8I,OAAOzI,UAAU0I,eAAezH,KAAKtB,EAAG,8BAItCA,aAAagiB,yBASN1Z,GAAuBhE,GAGrC,YAAuB9C,IAAnB8C,EAAOE,iBASGQ,GAAwBV,EAA2BrD,GAGjE,GAFAqD,EAAO0E,YAAa,EAEE,WAAlB1E,EAAOG,OACT,OAAO3D,OAAoBU,GAE7B,GAAsB,YAAlB8C,EAAOG,OACT,OAAOzD,EAAoBsD,EAAOQ,cAGpCqM,GAAoB7M,GAEpB,MAAMD,EAASC,EAAOE,aACPhD,IAAX6C,GAAwByO,GAA2BzO,KACrDA,EAAO6L,kBAAkB4H,SAAQ7H,IAC/BA,EAAgBhI,iBAAYzG,MAE9B6C,EAAO6L,kBAAoB,IAAIvN,GAIjC,OAAOhB,EADqB2C,EAAO2E,0BAA0BtD,GAAa1E,GACzBvB,YAGnCyR,GAAuB7M,GAGrCA,EAAOG,OAAS,SAEhB,MAAMJ,EAASC,EAAOE,aAEPhD,IAAX6C,IAIJM,EAAkCN,GAE9BgE,EAAiChE,KACnCA,EAAOuD,cAAckQ,SAAQnQ,IAC3BA,EAAYM,iBAEd5D,EAAOuD,cAAgB,IAAIjF,aAIfoP,GAAuBzN,EAA2BuE,GAIhEvE,EAAOG,OAAS,UAChBH,EAAOQ,aAAe+D,EAEtB,MAAMxE,EAASC,EAAOE,aAEPhD,IAAX6C,IAIJa,EAAiCb,EAAQwE,GAErCR,EAAiChE,IACnCA,EAAOuD,cAAckQ,SAAQnQ,IAC3BA,EAAYiB,YAAYC,MAG1BxE,EAAOuD,cAAgB,IAAIjF,IAI3B0B,EAAO6L,kBAAkB4H,SAAQ7H,IAC/BA,EAAgBrH,YAAYC,MAG9BxE,EAAO6L,kBAAoB,IAAIvN,IAuBnC,SAASiT,GAA0BvQ,GACjC,OAAO,IAAI9C,UAAU,4BAA4B8C,mDGhhBnC8d,GAA2BpP,EACAvN,GACzCF,EAAiByN,EAAMvN,GACvB,MAAMiM,EAAgBsB,MAAAA,SAAAA,EAAMtB,cAE5B,OADA3L,EAAoB2L,EAAe,gBAAiB,uBAC7C,CACLA,cAAezL,EAA0ByL,IHyT7C3J,OAAOK,iBAAiB6Y,eAAe3hB,UAAW,CAChD+I,OAAQ,CAAEC,YAAY,GACtB+Z,UAAW,CAAE/Z,YAAY,GACzBga,YAAa,CAAEha,YAAY,GAC3Bia,OAAQ,CAAEja,YAAY,GACtBka,IAAK,CAAEla,YAAY,GACnBma,OAAQ,CAAEna,YAAY,GACtBsM,OAAQ,CAAEtM,YAAY,KAEU,iBAAvB9J,EAAOiK,aAChBV,OAAOW,eAAeuY,eAAe3hB,UAAWd,EAAOiK,YAAa,CAClEzI,MAAO,iBACP2I,cAAc,IAGkB,iBAAzBnK,EAAOoK,eAChBb,OAAOW,eAAeuY,eAAe3hB,UAAWd,EAAOoK,cAAe,CACpE5I,MAAOihB,eAAe3hB,UAAUmjB,OAChCpH,UAAU,EACV1S,cAAc,II/UlB,MAAM+Z,GAA0B3b,GACvBA,EAAMmE,WAEfnD,OAAOW,eAAega,GAAwB,OAAQ,CACpD1iB,MAAO,OACP2I,cAAc,UAQKga,0BAInB9gB,YAAYgf,GACVhb,EAAuBgb,EAAS,EAAG,6BACnCA,EAAUuB,GAA2BvB,EAAS,mBAC9C/e,KAAK8gB,wCAA0C/B,EAAQnP,cAMzDA,oBACE,IAAKmR,GAA4B/gB,MAC/B,MAAMghB,GAA8B,iBAEtC,OAAOhhB,KAAK8gB,wCAMdpX,WACE,IAAKqX,GAA4B/gB,MAC/B,MAAMghB,GAA8B,QAEtC,OAAOJ,IAiBX,SAASI,GAA8Bxe,GACrC,OAAO,IAAI9C,UAAU,uCAAuC8C,8DAG9Cue,GAA4B5jB,GAC1C,QAAKD,EAAaC,OAIb8I,OAAOzI,UAAU0I,eAAezH,KAAKtB,EAAG,4CAItCA,aAAa0jB,2BA1BtB5a,OAAOK,iBAAiBua,0BAA0BrjB,UAAW,CAC3DoS,cAAe,CAAEpJ,YAAY,GAC7BkD,KAAM,CAAElD,YAAY,KAEY,iBAAvB9J,EAAOiK,aAChBV,OAAOW,eAAeia,0BAA0BrjB,UAAWd,EAAOiK,YAAa,CAC7EzI,MAAO,4BACP2I,cAAc,ICnDlB,MAAMoa,GAAoB,IACjB,EAEThb,OAAOW,eAAeqa,GAAmB,OAAQ,CAC/C/iB,MAAO,OACP2I,cAAc,UAQKqa,qBAInBnhB,YAAYgf,GACVhb,EAAuBgb,EAAS,EAAG,wBACnCA,EAAUuB,GAA2BvB,EAAS,mBAC9C/e,KAAKmhB,mCAAqCpC,EAAQnP,cAMpDA,oBACE,IAAKwR,GAAuBphB,MAC1B,MAAMqhB,GAAyB,iBAEjC,OAAOrhB,KAAKmhB,mCAOdzX,WACE,IAAK0X,GAAuBphB,MAC1B,MAAMqhB,GAAyB,QAEjC,OAAOJ,IAiBX,SAASI,GAAyB7e,GAChC,OAAO,IAAI9C,UAAU,kCAAkC8C,yDAGzC4e,GAAuBjkB,GACrC,QAAKD,EAAaC,OAIb8I,OAAOzI,UAAU0I,eAAezH,KAAKtB,EAAG,uCAItCA,aAAa+jB,sBC3CtB,SAASI,GACPjiB,EACAgS,EACA1N,GAGA,OADAC,EAAevE,EAAIsE,GACX6I,GAAoD3M,EAAYR,EAAIgS,EAAU,CAAC7E,IAGzF,SAAS+U,GACPliB,EACAgS,EACA1N,GAGA,OADAC,EAAevE,EAAIsE,GACX6I,GAAoDlN,EAAYD,EAAIgS,EAAU,CAAC7E,IAGzF,SAASgV,GACPniB,EACAgS,EACA1N,GAGA,OADAC,EAAevE,EAAIsE,GACZ,CAACsB,EAAUuH,IAAoD3M,EAAYR,EAAIgS,EAAU,CAACpM,EAAOuH,IDP1GvG,OAAOK,iBAAiB4a,qBAAqB1jB,UAAW,CACtDoS,cAAe,CAAEpJ,YAAY,GAC7BkD,KAAM,CAAElD,YAAY,KAEY,iBAAvB9J,EAAOiK,aAChBV,OAAOW,eAAesa,qBAAqB1jB,UAAWd,EAAOiK,YAAa,CACxEzI,MAAO,uBACP2I,cAAc,UEtBL4a,gBAmBX1hB,YAAY2hB,EAAuD,GACvDC,EAA6D,GAC7DC,EAA6D,SAChDjjB,IAAnB+iB,IACFA,EAAiB,MAGnB,MAAMG,EAAmB5Q,GAAuB0Q,EAAqB,oBAC/DG,EAAmB7Q,GAAuB2Q,EAAqB,mBAE/DG,WDtD+B1Q,EACA1N,GACvCF,EAAiB4N,EAAU1N,GAC3B,MAAMqe,EAAQ3Q,MAAAA,SAAAA,EAAU2Q,MAClBC,EAAe5Q,MAAAA,SAAAA,EAAU4Q,aACzB/P,EAAQb,MAAAA,SAAAA,EAAUa,MAClB6N,EAAY1O,MAAAA,SAAAA,EAAU0O,UACtBmC,EAAe7Q,MAAAA,SAAAA,EAAU6Q,aAC/B,MAAO,CACLF,WAAiBrjB,IAAVqjB,OACLrjB,EACA2iB,GAAgCU,EAAO3Q,EAAW,GAAG1N,6BACvDse,aAAAA,EACA/P,WAAiBvT,IAAVuT,OACLvT,EACA4iB,GAAgCrP,EAAOb,EAAW,GAAG1N,6BACvDoc,eAAyBphB,IAAdohB,OACTphB,EACA6iB,GAAoCzB,EAAW1O,EAAW,GAAG1N,iCAC/Due,aAAAA,GCmCoBC,CAAmBT,EAAgB,mBACvD,QAAiC/iB,IAA7BojB,EAAYE,aACd,MAAM,IAAIpY,WAAW,kCAEvB,QAAiClL,IAA7BojB,EAAYG,aACd,MAAM,IAAIrY,WAAW,kCAGvB,MAAMuY,EAAwBvR,GAAqBiR,EAAkB,GAC/DO,EAAwBrR,GAAqB8Q,GAC7CQ,EAAwBzR,GAAqBgR,EAAkB,GAC/DU,EAAwBvR,GAAqB6Q,GAEnD,IAAIW,GAyFR,SAAyC/gB,EACAghB,EACAH,EACAC,EACAH,EACAC,GACvC,SAAS5S,IACP,OAAOgT,EAGT,SAAShQ,EAAexN,GACtB,OAgRJ,SAAwDxD,EAA+BwD,GAGrF,MAAMuH,EAAa/K,EAAOihB,2BAE1B,GAAIjhB,EAAOoS,cAAe,CAGxB,OAAO/U,EAF2B2C,EAAOkhB,4BAEc,KACrD,MAAMpJ,EAAW9X,EAAOmhB,UAExB,GAAc,aADArJ,EAAS3X,OAErB,MAAM2X,EAAStX,aAGjB,OAAO4gB,GAAuDrW,EAAYvH,MAI9E,OAAO4d,GAAuDrW,EAAYvH,GAnSjE6d,CAAyCrhB,EAAQwD,GAG1D,SAAS0N,EAAevU,GACtB,OAkSJ,SAAkDqD,EAAyBrD,GAIzE,OADA2kB,GAAqBthB,EAAQrD,GACtBH,OAAoBU,GAtSlBqkB,CAAyCvhB,EAAQrD,GAG1D,SAASsU,IACP,OAqSJ,SAAwDjR,GAEtD,MAAMue,EAAWve,EAAOwhB,UAElBzW,EAAa/K,EAAOihB,2BACpBQ,EAAe1W,EAAW2W,kBAIhC,OAHAC,GAAgD5W,GAGzC1N,EAAqBokB,GAAc,KACxC,GAAwB,YAApBlD,EAASpe,OACX,MAAMoe,EAAS/d,aAEjB0Z,GAAqCqE,EAAS5Z,8BAC7CyJ,IAED,MADAkT,GAAqBthB,EAAQoO,GACvBmQ,EAAS/d,gBArTRohB,CAAyC5hB,GAMlD,SAASiO,IACP,OAoTJ,SAAmDjO,GASjD,OAHA6hB,GAA+B7hB,GAAQ,GAGhCA,EAAOkhB,2BA7TLY,CAA0C9hB,GAGnD,SAASkO,EAAgBvR,GAEvB,OADAolB,GAA4C/hB,EAAQrD,GAC7CH,OAAoBU,GAT7B8C,EAAOmhB,UhB4BT,SAAiCnT,EACAgD,EACAC,EACAC,EACA/C,EAAgB,EAChB2C,EAAgD,KAAM,IAGrF,MAAM9Q,EAA4BwE,OAAOkJ,OAAO0C,eAAerU,WAO/D,OANA8U,GAAyB7Q,GAIzBmR,GAAqCnR,EAFkBwE,OAAOkJ,OAAOqD,gCAAgChV,WAE5CiS,EAAgBgD,EAAgBC,EACpDC,EAAgB/C,EAAe2C,GAC7D9Q,EgB3CYgiB,CAAqBhU,EAAgBgD,EAAgBC,EAAgBC,EAChD2P,EAAuBC,GAW/D9gB,EAAOwhB,UAAY3E,GAAqB7O,EAAgBC,EAAeC,EAAiByS,EAChDC,GAGxC5gB,EAAOoS,mBAAgBlV,EACvB8C,EAAOkhB,gCAA6BhkB,EACpC8C,EAAOiiB,wCAAqC/kB,EAC5C2kB,GAA+B7hB,GAAQ,GAEvCA,EAAOihB,gCAA6B/jB,EA/HlCglB,CACE3jB,KALmBjC,GAAiBJ,IACpC6kB,EAAuB7kB,KAIH2kB,EAAuBC,EAAuBH,EAAuBC,GA6R/F,SAAoE5gB,EACAsgB,GAClE,MAAMvV,EAAkDvG,OAAOkJ,OAAOyU,iCAAiCpmB,WAEvG,IAAIqmB,EAAsB5e,IACxB,IAEE,OADA6e,GAAwCtX,EAAYvH,GAC7ChH,OAAoBU,GAC3B,MAAOolB,GACP,OAAO5lB,EAAoB4lB,KAI3BC,EAAsC,IAAM/lB,OAAoBU,QAEtCA,IAA1BojB,EAAYhC,YACd8D,EAAqB5e,GAAS8c,EAAYhC,UAAW9a,EAAOuH,SAEpC7N,IAAtBojB,EAAYC,QACdgC,EAAiB,IAAMjC,EAAYC,MAAOxV,KAjC9C,SAAqD/K,EACA+K,EACAqX,EACAG,GAInDxX,EAAWyX,2BAA6BxiB,EACxCA,EAAOihB,2BAA6BlW,EAEpCA,EAAW0X,oBAAsBL,EACjCrX,EAAW2W,gBAAkBa,EAyB7BG,CAAsC1iB,EAAQ+K,EAAYqX,EAAoBG,GAjT5EI,CAAqDpkB,KAAM+hB,QAEjCpjB,IAAtBojB,EAAY7P,MACdsQ,EAAqBT,EAAY7P,MAAMlS,KAAK0iB,6BAE5CF,OAAqB7jB,GAOzBqhB,eACE,IAAKqE,GAAkBrkB,MACrB,MAAM+S,GAA0B,YAGlC,OAAO/S,KAAKijB,UAMd1J,eACE,IAAK8K,GAAkBrkB,MACrB,MAAM+S,GAA0B,YAGlC,OAAO/S,KAAK4iB,WAmGhB,SAASyB,GAAkBlnB,GACzB,QAAKD,EAAaC,OAIb8I,OAAOzI,UAAU0I,eAAezH,KAAKtB,EAAG,+BAItCA,aAAaskB,iBAItB,SAASsB,GAAqBthB,EAAyBuE,GACrD6V,GACEpa,EAAOwhB,UAAU7c,0BACjBJ,GAEFwd,GAA4C/hB,EAAQuE,GAGtD,SAASwd,GAA4C/hB,EAAyBuE,GAC5Eod,GAAgD3hB,EAAOihB,4BACvDrL,GAA6C5V,EAAOmhB,UAAUrP,0BAA2BvN,GACrFvE,EAAOoS,eAITyP,GAA+B7hB,GAAQ,GAI3C,SAAS6hB,GAA+B7hB,EAAyB8T,QAIrB5W,IAAtC8C,EAAOkhB,4BACTlhB,EAAOiiB,qCAGTjiB,EAAOkhB,2BAA6B5kB,GAAWJ,IAC7C8D,EAAOiiB,mCAAqC/lB,KAG9C8D,EAAOoS,cAAgB0B,EA3IzBtP,OAAOK,iBAAiBmb,gBAAgBjkB,UAAW,CACjDwiB,SAAU,CAAExZ,YAAY,GACxB+S,SAAU,CAAE/S,YAAY,KAEQ,iBAAvB9J,EAAOiK,aAChBV,OAAOW,eAAe6a,gBAAgBjkB,UAAWd,EAAOiK,YAAa,CACnEzI,MAAO,kBACP2I,cAAc,UA8IL+c,iCAQX7jB,cACE,MAAM,IAAIL,UAAU,uBAMtBsL,kBACE,IAAKsZ,GAAmCtkB,MACtC,MAAM8X,GAAqC,eAI7C,OAAO2D,GADoBzb,KAAKikB,2BAA2BhB,UAAU7c,2BAQvErG,QAAQkF,GACN,IAAKqf,GAAmCtkB,MACtC,MAAM8X,GAAqC,WAG7CgM,GAAwC9jB,KAAMiF,GAOhDlF,MAAM3B,GACJ,IAAKkmB,GAAmCtkB,MACtC,MAAM8X,GAAqC,SAoHjD,IAAkG9R,EAAAA,EAjHlD5H,EAkH9C2kB,GAlHwC/iB,KAkHRikB,2BAA4Bje,GA3G5DjG,YACE,IAAKukB,GAAmCtkB,MACtC,MAAM8X,GAAqC,cAqHjD,SAAsDtL,GACpD,MAAM/K,EAAS+K,EAAWyX,2BAG1BtI,GAF2Bla,EAAOwhB,UAAU7c,2BAI5C,MAAMgK,EAAQ,IAAI1Q,UAAU,8BAC5B8jB,GAA4C/hB,EAAQ2O,GAzHlDmU,CAA0CvkB,OAmB9C,SAASskB,GAA4CnnB,GACnD,QAAKD,EAAaC,OAIb8I,OAAOzI,UAAU0I,eAAezH,KAAKtB,EAAG,+BAItCA,aAAaymB,kCA0CtB,SAASR,GAAgD5W,GACvDA,EAAW0X,yBAAsBvlB,EACjC6N,EAAW2W,qBAAkBxkB,EAG/B,SAASmlB,GAA2CtX,EAAiDvH,GACnG,MAAMxD,EAAS+K,EAAWyX,2BACpBO,EAAqB/iB,EAAOwhB,UAAU7c,0BAC5C,IAAKsV,GAAiD8I,GACpD,MAAM,IAAI9kB,UAAU,wDAMtB,IACEkc,GAAuC4I,EAAoBvf,GAC3D,MAAOe,GAIP,MAFAwd,GAA4C/hB,EAAQuE,GAE9CvE,EAAOwhB,UAAUhhB,uBZjHzBuK,GAEA,OAAIyP,GAA8CzP,IYkH7BiY,CAA+CD,KAC/C/iB,EAAOoS,eAE1ByP,GAA+B7hB,GAAQ,GAQ3C,SAASohB,GAAuDrW,EACAvH,GAE9D,OAAOnG,EADkB0N,EAAW0X,oBAAoBjf,QACVtG,GAAWkR,IAEvD,MADAkT,GAAqBvW,EAAWyX,2BAA4BpU,GACtDA,KAiFV,SAASiI,GAAqCtV,GAC5C,OAAO,IAAI9C,UACT,8CAA8C8C,4DAKlD,SAASuQ,GAA0BvQ,GACjC,OAAO,IAAI9C,UACT,6BAA6B8C,2CApMjCyD,OAAOK,iBAAiBsd,iCAAiCpmB,UAAW,CAClE2S,QAAS,CAAE3J,YAAY,GACvB4J,MAAO,CAAE5J,YAAY,GACrBke,UAAW,CAAEle,YAAY,GACzBwE,YAAa,CAAExE,YAAY,KAEK,iBAAvB9J,EAAOiK,aAChBV,OAAOW,eAAegd,iCAAiCpmB,UAAWd,EAAOiK,YAAa,CACpFzI,MAAO,mCACP2I,cAAc,IC7TlB,MAAM8d,GAAU,CACdxF,eAAAA,eACA5D,gCAAAA,gCACA5Q,6BAAAA,6BACAZ,0BAAAA,0BACAnF,4BAAAA,4BACAmL,yBAAAA,yBAEA8B,eAAAA,eACAW,gCAAAA,gCACAa,4BAAAA,4BAEAwN,0BAAAA,0BACAK,qBAAAA,qBAEAO,gBAAAA,gBACAmC,iCAAAA,kCAIF,QAAuB,IAAZ9mB,EACT,IAAK,MAAM8nB,KAAQD,GACb1e,OAAOzI,UAAU0I,eAAezH,KAAKkmB,GAASC,IAChD3e,OAAOW,eAAe9J,EAAS8nB,EAAM,CACnC1mB,MAAOymB,GAAQC,GACfrL,UAAU,EACV1S,cAAc"}