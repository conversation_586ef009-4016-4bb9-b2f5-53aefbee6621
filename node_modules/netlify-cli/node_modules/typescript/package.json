{"name": "typescript", "author": "Microsoft Corp.", "homepage": "https://www.typescriptlang.org/", "version": "5.1.6", "license": "Apache-2.0", "description": "TypeScript is a language for application scale JavaScript development", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript"], "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "repository": {"type": "git", "url": "https://github.com/Microsoft/TypeScript.git"}, "main": "./lib/typescript.js", "typings": "./lib/typescript.d.ts", "bin": {"tsc": "./bin/tsc", "tsserver": "./bin/tsserver"}, "engines": {"node": ">=14.17"}, "files": ["bin", "lib", "!lib/enu", "LICENSE.txt", "README.md", "SECURITY.md", "ThirdPartyNoticeText.txt", "!**/.gitattributes"], "devDependencies": {"@esfx/canceltoken": "^1.0.0", "@octokit/rest": "latest", "@types/chai": "^4.3.4", "@types/fs-extra": "^9.0.13", "@types/glob": "^8.1.0", "@types/microsoft__typescript-etw": "^0.1.1", "@types/minimist": "^1.2.2", "@types/mocha": "^10.0.1", "@types/ms": "^0.7.31", "@types/node": "latest", "@types/source-map-support": "^0.5.6", "@types/which": "^2.0.1", "@typescript-eslint/eslint-plugin": "^5.33.1", "@typescript-eslint/parser": "^5.33.1", "@typescript-eslint/utils": "^5.33.1", "azure-devops-node-api": "^12.0.0", "chai": "^4.3.7", "chalk": "^4.1.2", "chokidar": "^3.5.3", "del": "^6.1.1", "diff": "^5.1.0", "esbuild": "^0.17.2", "eslint": "^8.22.0", "eslint-formatter-autolinkable-stylish": "^1.2.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-local": "^1.0.0", "eslint-plugin-no-null": "^1.0.2", "eslint-plugin-simple-import-sort": "^10.0.0", "fast-xml-parser": "^4.0.11", "fs-extra": "^9.1.0", "glob": "^8.1.0", "hereby": "^1.6.4", "jsonc-parser": "^3.2.0", "minimist": "^1.2.8", "mocha": "^10.2.0", "mocha-fivemat-progress-reporter": "^0.1.0", "ms": "^2.1.3", "node-fetch": "^3.2.10", "source-map-support": "^0.5.21", "tslib": "^2.5.0", "typescript": "^5.0.2", "which": "^2.0.2"}, "overrides": {"typescript@*": "$typescript"}, "scripts": {"test": "hereby runtests-parallel --light=false", "test:eslint-rules": "hereby run-eslint-rules-tests", "build": "npm run build:compiler && npm run build:tests", "build:compiler": "hereby local", "build:tests": "hereby tests", "build:tests:notypecheck": "hereby tests --no-typecheck", "start": "node lib/tsc", "clean": "hereby clean", "gulp": "hereby", "lint": "hereby lint", "setup-hooks": "node scripts/link-hooks.mjs"}, "browser": {"fs": false, "os": false, "path": false, "crypto": false, "buffer": false, "@microsoft/typescript-etw": false, "source-map-support": false, "inspector": false}, "packageManager": "npm@8.19.4", "volta": {"node": "20.1.0", "npm": "8.19.4"}}