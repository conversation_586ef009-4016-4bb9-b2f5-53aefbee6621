cmd_Release/obj.target/unix_dgram/src/unix_dgram.o := g++ -o Release/obj.target/unix_dgram/src/unix_dgram.o ../src/unix_dgram.cc '-DNODE_GYP_MODULE_NAME=unix_dgram' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node -I/Users/<USER>/Library/Caches/node-gyp/20.12.1/src -I/Users/<USER>/Library/Caches/node-gyp/20.12.1/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/20.12.1/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/20.12.1/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/20.12.1/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/20.12.1/deps/v8/include -I../../nan  -O3 -gdwarf-2 -mmacosx-version-min=10.15 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++17 -stdlib=libc++ -fno-rtti -fno-exceptions -fno-strict-aliasing -MMD -MF ./Release/.deps/Release/obj.target/unix_dgram/src/unix_dgram.o.d.raw -I/opt/homebrew/opt/libpcap/include  -c
Release/obj.target/unix_dgram/src/unix_dgram.o: ../src/unix_dgram.cc \
  ../../nan/nan.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/node_version.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/uv.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/uv/errno.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/uv/version.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/uv/unix.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/uv/threadpool.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/uv/darwin.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/node.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/cppgc/common.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8config.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-array-buffer.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-local-handle.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-internal.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-version.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-object.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-maybe.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-persistent-handle.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-weak-callback-info.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-primitive.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-data.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-value.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-traced-handle.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-container.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-context.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-snapshot.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-date.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-debug.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-script.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-callbacks.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-promise.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-message.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-exception.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-extension.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-external.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-function.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-function-callback.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-template.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-memory-span.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-initialization.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-isolate.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-embedder-heap.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-microtask.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-statistics.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-unwinder.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-embedder-state-scope.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-platform.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-json.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-locker.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-microtask-queue.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-primitive-object.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-proxy.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-regexp.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-typed-array.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-value-serializer.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-wasm.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/node_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/node_buffer.h \
  /Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/node_object_wrap.h \
  ../../nan/nan_callbacks.h ../../nan/nan_callbacks_12_inl.h \
  ../../nan/nan_maybe_43_inl.h ../../nan/nan_converters.h \
  ../../nan/nan_converters_43_inl.h ../../nan/nan_new.h \
  ../../nan/nan_implementation_12_inl.h \
  ../../nan/nan_persistent_12_inl.h ../../nan/nan_weak.h \
  ../../nan/nan_object_wrap.h ../../nan/nan_private.h \
  ../../nan/nan_typedarray_contents.h ../../nan/nan_json.h \
  ../../nan/nan_scriptorigin.h
../src/unix_dgram.cc:
../../nan/nan.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/node_version.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/uv.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/uv/errno.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/uv/version.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/uv/unix.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/uv/threadpool.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/uv/darwin.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/node.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/cppgc/common.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8config.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-array-buffer.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-local-handle.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-internal.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-version.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-object.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-maybe.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-persistent-handle.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-weak-callback-info.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-primitive.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-data.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-value.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-traced-handle.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-container.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-context.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-snapshot.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-date.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-debug.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-script.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-callbacks.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-promise.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-message.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-exception.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-extension.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-external.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-function.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-function-callback.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-template.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-memory-span.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-initialization.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-isolate.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-embedder-heap.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-microtask.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-statistics.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-unwinder.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-embedder-state-scope.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-platform.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-json.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-locker.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-microtask-queue.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-primitive-object.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-proxy.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-regexp.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-typed-array.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-value-serializer.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/v8-wasm.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/node_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/node_buffer.h:
/Users/<USER>/Library/Caches/node-gyp/20.12.1/include/node/node_object_wrap.h:
../../nan/nan_callbacks.h:
../../nan/nan_callbacks_12_inl.h:
../../nan/nan_maybe_43_inl.h:
../../nan/nan_converters.h:
../../nan/nan_converters_43_inl.h:
../../nan/nan_new.h:
../../nan/nan_implementation_12_inl.h:
../../nan/nan_persistent_12_inl.h:
../../nan/nan_weak.h:
../../nan/nan_object_wrap.h:
../../nan/nan_private.h:
../../nan/nan_typedarray_contents.h:
../../nan/nan_json.h:
../../nan/nan_scriptorigin.h:
