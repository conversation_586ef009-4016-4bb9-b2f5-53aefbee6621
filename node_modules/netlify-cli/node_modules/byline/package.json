{"name": "byline", "description": "simple line-by-line stream reader", "homepage": "https://github.com/jahewson/node-byline", "bugs": {"url": "https://github.com/jahewson/node-byline/issues"}, "version": "5.0.0", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jahewson/node-byline"}, "engines": {"node": ">=0.10.0"}, "main": "./lib/byline.js", "files": ["lib"], "devDependencies": {"mocha": "~2.1.0", "request": "~2.27.0"}, "scripts": {"test": "mocha -R spec --timeout 60000"}}