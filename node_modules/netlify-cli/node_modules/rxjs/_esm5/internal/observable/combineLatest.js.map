{"version": 3, "file": "combineLatest.js", "sources": ["../../../src/internal/observable/combineLatest.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,WAAW,EAAG,MAAM,qBAAqB,CAAC;AACnD,OAAO,EAAE,OAAO,EAAG,MAAM,iBAAiB,CAAC;AAE3C,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAGrD,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC,IAAM,IAAI,GAAG,EAAE,CAAC;AAsNhB,MAAM,UAAU,aAAa;IAC3B,qBAAgF;SAAhF,UAAgF,EAAhF,qBAAgF,EAAhF,IAAgF;QAAhF,gCAAgF;;IAEhF,IAAI,cAAc,GAAgD,SAAS,CAAC;IAC5E,IAAI,SAAS,GAA4B,SAAS,CAAC;IAEnD,IAAI,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;QACpD,SAAS,GAAG,WAAW,CAAC,GAAG,EAAmB,CAAC;KAChD;IAED,IAAI,OAAO,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;QAC7D,cAAc,GAAG,WAAW,CAAC,GAAG,EAAkC,CAAC;KACpE;IAID,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;QACvD,WAAW,GAAG,WAAW,CAAC,CAAC,CAAQ,CAAC;KACrC;IAED,OAAO,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC;AAC3F,CAAC;AAED;IACE,+BAAoB,cAA6C;QAA7C,mBAAc,GAAd,cAAc,CAA+B;IACjE,CAAC;IAED,oCAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,uBAAuB,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;IACxF,CAAC;IACH,4BAAC;AAAD,CAAC,AAPD,IAOC;;AAOD;IAAmD,mDAAqB;IAMtE,iCAAY,WAA0B,EAAU,cAA6C;QAA7F,YACE,kBAAM,WAAW,CAAC,SACnB;QAF+C,oBAAc,GAAd,cAAc,CAA+B;QALrF,YAAM,GAAW,CAAC,CAAC;QACnB,YAAM,GAAU,EAAE,CAAC;QACnB,iBAAW,GAAU,EAAE,CAAC;;IAKhC,CAAC;IAES,uCAAK,GAAf,UAAgB,UAAe;QAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC;IAES,2CAAS,GAAnB;QACE,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAM,GAAG,GAAG,WAAW,CAAC,MAAM,CAAC;QAC/B,IAAI,GAAG,KAAK,CAAC,EAAE;YACb,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;SAC9B;aAAM;YACL,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;YAClB,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;YACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;gBAC5B,IAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;gBAClC,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;aAC7D;SACF;IACH,CAAC;IAED,gDAAc,GAAd,UAAe,MAAqB;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE;YAC5B,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;SAC9B;IACH,CAAC;IAED,4CAAU,GAAV,UAAW,WAAc,EAAE,UAAa,EAC7B,UAAkB;QAC3B,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;QAClC,IAAM,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS;YAC/B,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;QACxD,MAAM,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;QAEhC,IAAI,SAAS,KAAK,CAAC,EAAE;YACnB,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;aACjC;iBAAM;gBACL,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;aACxC;SACF;IACH,CAAC;IAEO,oDAAkB,GAA1B,UAA2B,MAAa;QACtC,IAAI,MAAW,CAAC;QAChB,IAAI;YACF,MAAM,GAAG,IAAI,CAAC,cAAe,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SACnD;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,WAAW,CAAC,KAAM,CAAC,GAAG,CAAC,CAAC;YAC7B,OAAO;SACR;QACD,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IACH,8BAAC;AAAD,CAAC,AAhED,CAAmD,eAAe,GAgEjE"}