{"version": 3, "file": "bindNodeCallback.js", "sources": ["../../../src/internal/observable/bindNodeCallback.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAG/C,OAAO,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAC;AACvC,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAoJ1C,MAAM,UAAU,gBAAgB,CAC9B,YAAsB,EACtB,cAAsC,EACtC,SAAyB;IAGzB,IAAI,cAAc,EAAE;QAClB,IAAI,WAAW,CAAC,cAAc,CAAC,EAAE;YAC/B,SAAS,GAAG,cAAc,CAAC;SAC5B;aAAM;YAEL,OAAO;gBAAC,cAAc;qBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;oBAAd,yBAAc;;gBAAK,OAAA,gBAAgB,CAAC,YAAY,EAAE,SAAS,CAAC,eAAI,IAAI,EAAE,IAAI,CAChF,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,eAAI,IAAI,EAAE,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,EAA9D,CAA8D,CAAC,CAC5E;YAF0B,CAE1B,CAAC;SACH;KACF;IAED,OAAO;QAAoB,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,yBAAc;;QACvC,IAAM,MAAM,GAAmB;YAC7B,OAAO,EAAE,SAAS;YAClB,IAAI,MAAA;YACJ,YAAY,cAAA;YACZ,SAAS,WAAA;YACT,OAAO,EAAE,IAAI;SACd,CAAC;QACF,OAAO,IAAI,UAAU,CAAI,UAAA,UAAU;YACzB,IAAA,wBAAO,CAAY;YACrB,IAAA,wBAAO,CAAY;YACzB,IAAI,CAAC,SAAS,EAAE;gBACd,IAAI,CAAC,OAAO,EAAE;oBACZ,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,YAAY,EAAK,CAAC;oBACjD,IAAM,OAAO,GAAG;wBAAC,mBAAmB;6BAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;4BAAnB,8BAAmB;;wBAClC,IAAM,GAAG,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;wBAE9B,IAAI,GAAG,EAAE;4BACP,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;4BACnB,OAAO;yBACR;wBAED,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;wBAC/D,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACrB,CAAC,CAAC;oBAEF,IAAI;wBACF,YAAY,CAAC,KAAK,CAAC,OAAO,EAAM,IAAI,SAAE,OAAO,GAAE,CAAC;qBACjD;oBAAC,OAAO,GAAG,EAAE;wBACZ,IAAI,cAAc,CAAC,OAAO,CAAC,EAAE;4BAC3B,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;yBACpB;6BAAM;4BACL,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;yBACnB;qBACF;iBACF;gBACD,OAAO,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;aACtC;iBAAM;gBACL,OAAO,SAAS,CAAC,QAAQ,CAAmB,QAAQ,EAAE,CAAC,EAAE,EAAE,MAAM,QAAA,EAAE,UAAU,YAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;aAC3F;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAgBD,SAAS,QAAQ,CAA6C,KAAuB;IAArF,iBA0BC;IAzBS,IAAA,qBAAM,EAAE,6BAAU,EAAE,uBAAO,CAAW;IACtC,IAAA,kCAAY,EAAE,kBAAI,EAAE,4BAAS,CAAY;IACjD,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;IAE7B,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,YAAY,EAAK,CAAC;QAEjD,IAAM,OAAO,GAAG;YAAC,mBAAmB;iBAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;gBAAnB,8BAAmB;;YAClC,IAAM,GAAG,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;YAC9B,IAAI,GAAG,EAAE;gBACP,KAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAsB,aAAa,EAAE,CAAC,EAAE,EAAE,GAAG,KAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC,CAAC;aACvF;iBAAM;gBACL,IAAM,KAAK,GAAG,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC/D,KAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAqB,YAAY,EAAE,CAAC,EAAE,EAAE,KAAK,OAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC,CAAC;aACvF;QACH,CAAC,CAAC;QAEF,IAAI;YACF,YAAY,CAAC,KAAK,CAAC,OAAO,EAAM,IAAI,SAAE,OAAO,GAAE,CAAC;SACjD;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAsB,aAAa,EAAE,CAAC,EAAE,EAAE,GAAG,KAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC,CAAC;SACvF;KACF;IAED,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AAC1C,CAAC;AAOD,SAAS,YAAY,CAAI,GAAuB;IACtC,IAAA,iBAAK,EAAE,qBAAO,CAAS;IAC/B,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpB,OAAO,CAAC,QAAQ,EAAE,CAAC;AACrB,CAAC;AAOD,SAAS,aAAa,CAAI,GAAwB;IACxC,IAAA,aAAG,EAAE,qBAAO,CAAS;IAC7B,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACrB,CAAC"}