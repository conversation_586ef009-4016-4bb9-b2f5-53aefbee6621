/** PURE_IMPORTS_START _Observable,_from,_empty PURE_IMPORTS_END */
import { Observable } from '../Observable';
import { from } from './from';
import { EMPTY } from './empty';
export function using(resourceFactory, observableFactory) {
    return new Observable(function (subscriber) {
        var resource;
        try {
            resource = resourceFactory();
        }
        catch (err) {
            subscriber.error(err);
            return undefined;
        }
        var result;
        try {
            result = observableFactory(resource);
        }
        catch (err) {
            subscriber.error(err);
            return undefined;
        }
        var source = result ? from(result) : EMPTY;
        var subscription = source.subscribe(subscriber);
        return function () {
            subscription.unsubscribe();
            if (resource) {
                resource.unsubscribe();
            }
        };
    });
}
//# sourceMappingURL=using.js.map
