{"version": 3, "file": "ReplaySubject.js", "sources": ["../../src/internal/ReplaySubject.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAEpC,OAAO,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AAE1C,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,uBAAuB,EAAE,MAAM,gCAAgC,CAAC;AACzE,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAQ5D;IAAsC,yCAAU;IAM9C,uBAAY,UAA6C,EAC7C,UAA6C,EACrC,SAAyB;QAFjC,2BAAA,EAAA,aAAqB,MAAM,CAAC,iBAAiB;QAC7C,2BAAA,EAAA,aAAqB,MAAM,CAAC,iBAAiB;QADzD,YAGE,iBAAO,SAUR;QAXmB,eAAS,GAAT,SAAS,CAAgB;QAPrC,aAAO,GAA2B,EAAE,CAAC;QAGrC,yBAAmB,GAAY,KAAK,CAAC;QAM3C,KAAI,CAAC,WAAW,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QACnD,KAAI,CAAC,WAAW,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QAEnD,IAAI,UAAU,KAAK,MAAM,CAAC,iBAAiB,EAAE;YAC3C,KAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,KAAI,CAAC,IAAI,GAAG,KAAI,CAAC,sBAAsB,CAAC;SACzC;aAAM;YACL,KAAI,CAAC,IAAI,GAAG,KAAI,CAAC,cAAc,CAAC;SACjC;;IACH,CAAC;IAEO,8CAAsB,GAA9B,UAA+B,KAAQ;QACrC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAGpB,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE;gBACrC,OAAO,CAAC,KAAK,EAAE,CAAC;aACjB;SACF;QACD,iBAAM,IAAI,YAAC,KAAK,CAAC,CAAC;IACpB,CAAC;IAEO,sCAAc,GAAtB,UAAuB,KAAQ;QAC7B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;YAC1D,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACjC;QACD,iBAAM,IAAI,YAAC,KAAK,CAAC,CAAC;IACpB,CAAC;IAGD,kCAAU,GAAV,UAAW,UAAyB;QAElC,IAAM,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACrD,IAAM,OAAO,GAAG,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACrF,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,IAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;QAC3B,IAAI,YAA0B,CAAC;QAE/B,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,MAAM,IAAI,uBAAuB,EAAE,CAAC;SACrC;aAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC1C,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC;SACnC;aAAM;YACL,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChC,YAAY,GAAG,IAAI,mBAAmB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;SAC1D;QAED,IAAI,SAAS,EAAE;YACb,UAAU,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,mBAAmB,CAAI,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;SAChF;QAED,IAAI,mBAAmB,EAAE;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAClD,UAAU,CAAC,IAAI,CAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aAChC;SACF;aAAM;YACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAClD,UAAU,CAAC,IAAI,CAAkB,OAAO,CAAC,CAAC,CAAE,CAAC,KAAK,CAAC,CAAC;aACrD;SACF;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACpC;aAAM,IAAI,IAAI,CAAC,SAAS,EAAE;YACzB,UAAU,CAAC,QAAQ,EAAE,CAAC;SACvB;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,+BAAO,GAAP;QACE,OAAO,CAAC,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC;IACzC,CAAC;IAEO,gDAAwB,GAAhC;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC3B,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAM,OAAO,GAAqB,IAAI,CAAC,OAAO,CAAC;QAE/C,IAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC;QACnC,IAAI,WAAW,GAAG,CAAC,CAAC;QAKpB,OAAO,WAAW,GAAG,WAAW,EAAE;YAChC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,WAAW,EAAE;gBACnD,MAAM;aACP;YACD,WAAW,EAAE,CAAC;SACf;QAED,IAAI,WAAW,GAAG,WAAW,EAAE;YAC7B,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,GAAG,WAAW,CAAC,CAAC;SAChE;QAED,IAAI,WAAW,GAAG,CAAC,EAAE;YACnB,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;SAChC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEH,oBAAC;AAAD,CAAC,AArHD,CAAsC,OAAO,GAqH5C;;AAED;IACE,qBAAmB,IAAY,EAAS,KAAQ;QAA7B,SAAI,GAAJ,IAAI,CAAQ;QAAS,UAAK,GAAL,KAAK,CAAG;IAChD,CAAC;IACH,kBAAC;AAAD,CAAC,AAHD,IAGC"}