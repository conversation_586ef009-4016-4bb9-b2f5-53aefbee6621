{"version": 3, "file": "Subscriber.js", "sources": ["../src/internal/Subscriber.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,gDAA+C;AAC/C,uCAAoD;AAEpD,+CAA8C;AAC9C,gEAAqF;AACrF,mCAAkC;AAClC,0DAAyD;AAYzD;IAAmC,8BAAY;IAuC7C,oBAAY,iBAA+D,EAC/D,KAAyB,EACzB,QAAqB;QAFjC,YAGE,iBAAO,SA2BR;QA7CgB,oBAAc,GAAQ,IAAI,CAAC;QAC3B,qBAAe,GAAY,KAAK,CAAC;QACjC,wBAAkB,GAAY,KAAK,CAAC;QAE3C,eAAS,GAAY,KAAK,CAAC;QAgBnC,QAAQ,SAAS,CAAC,MAAM,EAAE;YACxB,KAAK,CAAC;gBACJ,KAAI,CAAC,WAAW,GAAG,gBAAa,CAAC;gBACjC,MAAM;YACR,KAAK,CAAC;gBACJ,IAAI,CAAC,iBAAiB,EAAE;oBACtB,KAAI,CAAC,WAAW,GAAG,gBAAa,CAAC;oBACjC,MAAM;iBACP;gBACD,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE;oBACzC,IAAI,iBAAiB,YAAY,UAAU,EAAE;wBAC3C,KAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC,kBAAkB,CAAC;wBAC/D,KAAI,CAAC,WAAW,GAAG,iBAAiB,CAAC;wBACrC,iBAAiB,CAAC,GAAG,CAAC,KAAI,CAAC,CAAC;qBAC7B;yBAAM;wBACL,KAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;wBAC/B,KAAI,CAAC,WAAW,GAAG,IAAI,cAAc,CAAI,KAAI,EAAyB,iBAAiB,CAAC,CAAC;qBAC1F;oBACD,MAAM;iBACP;YACH;gBACE,KAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAC/B,KAAI,CAAC,WAAW,GAAG,IAAI,cAAc,CAAI,KAAI,EAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;gBAC1G,MAAM;SACT;;IACH,CAAC;IAnED,qBAAC,2BAAkB,CAAC,GAApB,cAAyB,OAAO,IAAI,CAAC,CAAC,CAAC;IAchC,iBAAM,GAAb,UAAiB,IAAsB,EACtB,KAAyB,EACzB,QAAqB;QACpC,IAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACzD,UAAU,CAAC,kBAAkB,GAAG,KAAK,CAAC;QACtC,OAAO,UAAU,CAAC;IACpB,CAAC;IAwDD,yBAAI,GAAJ,UAAK,KAAS;QACZ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACnB;IACH,CAAC;IASD,0BAAK,GAAL,UAAM,GAAS;QACb,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SAClB;IACH,CAAC;IAQD,6BAAQ,GAAR;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,SAAS,EAAE,CAAC;SAClB;IACH,CAAC;IAED,gCAAW,GAAX;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO;SACR;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,iBAAM,WAAW,WAAE,CAAC;IACtB,CAAC;IAES,0BAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAES,2BAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAES,8BAAS,GAAnB;QACE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;QAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAGD,2CAAsB,GAAtB;QACW,IAAA,wCAAgB,CAAU;QACnC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,OAAO,IAAI,CAAC;IACd,CAAC;IACH,iBAAC;AAAD,CAAC,AA/ID,CAAmC,2BAAY,GA+I9C;AA/IY,gCAAU;AAsJvB;IAAuC,kCAAa;IAIlD,wBAAoB,iBAAgC,EACxC,cAA0D,EAC1D,KAAyB,EACzB,QAAqB;QAHjC,YAIE,iBAAO,SAwBR;QA5BmB,uBAAiB,GAAjB,iBAAiB,CAAe;QAMlD,IAAI,IAA0B,CAAC;QAC/B,IAAI,OAAO,GAAQ,KAAI,CAAC;QAExB,IAAI,uBAAU,CAAC,cAAc,CAAC,EAAE;YAC9B,IAAI,GAA2B,cAAe,CAAC;SAChD;aAAM,IAAI,cAAc,EAAE;YACzB,IAAI,GAAyB,cAAe,CAAC,IAAI,CAAC;YAClD,KAAK,GAAyB,cAAe,CAAC,KAAK,CAAC;YACpD,QAAQ,GAAyB,cAAe,CAAC,QAAQ,CAAC;YAC1D,IAAI,cAAc,KAAK,gBAAa,EAAE;gBACpC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;gBACxC,IAAI,uBAAU,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;oBACnC,KAAI,CAAC,GAAG,CAAc,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;iBAC1D;gBACD,OAAO,CAAC,WAAW,GAAG,KAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;aACnD;SACF;QAED,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,KAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,KAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,KAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;;IAC5B,CAAC;IAED,6BAAI,GAAJ,UAAK,KAAS;QACZ,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE;YACzB,IAAA,0CAAiB,CAAU;YACnC,IAAI,CAAC,eAAM,CAAC,qCAAqC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,EAAE;gBAC1F,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;aACtC;iBAAM,IAAI,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;gBACrE,IAAI,CAAC,WAAW,EAAE,CAAC;aACpB;SACF;IACH,CAAC;IAED,8BAAK,GAAL,UAAM,GAAS;QACb,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACX,IAAA,0CAAiB,CAAU;YAC3B,IAAA,6FAAqC,CAAY;YACzD,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,qCAAqC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,EAAE;oBACnF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;oBACpC,IAAI,CAAC,WAAW,EAAE,CAAC;iBACpB;qBAAM;oBACL,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;oBAC1D,IAAI,CAAC,WAAW,EAAE,CAAC;iBACpB;aACF;iBAAM,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,EAAE;gBAChD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,IAAI,qCAAqC,EAAE;oBACzC,MAAM,GAAG,CAAC;iBACX;gBACD,iCAAe,CAAC,GAAG,CAAC,CAAC;aACtB;iBAAM;gBACL,IAAI,qCAAqC,EAAE;oBACzC,iBAAiB,CAAC,cAAc,GAAG,GAAG,CAAC;oBACvC,iBAAiB,CAAC,eAAe,GAAG,IAAI,CAAC;iBAC1C;qBAAM;oBACL,iCAAe,CAAC,GAAG,CAAC,CAAC;iBACtB;gBACD,IAAI,CAAC,WAAW,EAAE,CAAC;aACpB;SACF;IACH,CAAC;IAED,iCAAQ,GAAR;QAAA,iBAiBC;QAhBC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACX,IAAA,0CAAiB,CAAU;YACnC,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,IAAM,eAAe,GAAG,cAAM,OAAA,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAI,CAAC,QAAQ,CAAC,EAAlC,CAAkC,CAAC;gBAEjE,IAAI,CAAC,eAAM,CAAC,qCAAqC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,EAAE;oBAC1F,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;oBACnC,IAAI,CAAC,WAAW,EAAE,CAAC;iBACpB;qBAAM;oBACL,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAC;oBACzD,IAAI,CAAC,WAAW,EAAE,CAAC;iBACpB;aACF;iBAAM;gBACL,IAAI,CAAC,WAAW,EAAE,CAAC;aACpB;SACF;IACH,CAAC;IAEO,qCAAY,GAApB,UAAqB,EAAY,EAAE,KAAW;QAC5C,IAAI;YACF,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;SAC/B;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,eAAM,CAAC,qCAAqC,EAAE;gBAChD,MAAM,GAAG,CAAC;aACX;iBAAM;gBACL,iCAAe,CAAC,GAAG,CAAC,CAAC;aACtB;SACF;IACH,CAAC;IAEO,wCAAe,GAAvB,UAAwB,MAAqB,EAAE,EAAY,EAAE,KAAW;QACtE,IAAI,CAAC,eAAM,CAAC,qCAAqC,EAAE;YACjD,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC;SAC7B;QACD,IAAI;YACF,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;SAC/B;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,eAAM,CAAC,qCAAqC,EAAE;gBAChD,MAAM,CAAC,cAAc,GAAG,GAAG,CAAC;gBAC5B,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC9B,OAAO,IAAI,CAAC;aACb;iBAAM;gBACL,iCAAe,CAAC,GAAG,CAAC,CAAC;gBACrB,OAAO,IAAI,CAAC;aACb;SACF;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAGD,qCAAY,GAAZ;QACU,IAAA,0CAAiB,CAAU;QACnC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,iBAAiB,CAAC,WAAW,EAAE,CAAC;IAClC,CAAC;IACH,qBAAC;AAAD,CAAC,AArID,CAAuC,UAAU,GAqIhD;AArIY,wCAAc"}