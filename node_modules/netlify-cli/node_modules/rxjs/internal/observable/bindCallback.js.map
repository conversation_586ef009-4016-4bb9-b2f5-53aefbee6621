{"version": 3, "file": "bindCallback.js", "sources": ["../../src/internal/observable/bindCallback.ts"], "names": [], "mappings": ";;AACA,4CAA2C;AAC3C,gDAA+C;AAE/C,wCAAuC;AACvC,yDAAwD;AACxD,2CAA0C;AAC1C,mDAAkD;AA4KlD,SAAgB,YAAY,CAC1B,YAAsB,EACtB,cAAuC,EACvC,SAAyB;IAEzB,IAAI,cAAc,EAAE;QAClB,IAAI,yBAAW,CAAC,cAAc,CAAC,EAAE;YAC/B,SAAS,GAAG,cAAc,CAAC;SAC5B;aAAM;YAEL,OAAO;gBAAC,cAAc;qBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;oBAAd,yBAAc;;gBAAK,OAAA,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC,eAAI,IAAI,EAAE,IAAI,CAC5E,SAAG,CAAC,UAAC,IAAI,IAAK,OAAA,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,eAAI,IAAI,EAAE,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,EAA9D,CAA8D,CAAC,CAC9E;YAF0B,CAE1B,CAAC;SACH;KACF;IAED,OAAO;QAAqB,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,yBAAc;;QACxC,IAAM,OAAO,GAAG,IAAI,CAAC;QACrB,IAAI,OAAwB,CAAC;QAC7B,IAAM,MAAM,GAAG;YACb,OAAO,SAAA;YACP,OAAO,SAAA;YACP,YAAY,cAAA;YACZ,SAAS,WAAA;SACV,CAAC;QACF,OAAO,IAAI,uBAAU,CAAI,UAAA,UAAU;YACjC,IAAI,CAAC,SAAS,EAAE;gBACd,IAAI,CAAC,OAAO,EAAE;oBACZ,OAAO,GAAG,IAAI,2BAAY,EAAK,CAAC;oBAChC,IAAM,OAAO,GAAG;wBAAC,mBAAmB;6BAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;4BAAnB,8BAAmB;;wBAClC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;wBAC/D,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACrB,CAAC,CAAC;oBAEF,IAAI;wBACF,YAAY,CAAC,KAAK,CAAC,OAAO,EAAM,IAAI,SAAE,OAAO,GAAE,CAAC;qBACjD;oBAAC,OAAO,GAAG,EAAE;wBACZ,IAAI,+BAAc,CAAC,OAAO,CAAC,EAAE;4BAC3B,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;yBACpB;6BAAM;4BACL,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;yBACnB;qBACF;iBACF;gBACD,OAAO,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;aACtC;iBAAM;gBACL,IAAM,KAAK,GAAqB;oBAC9B,IAAI,MAAA,EAAE,UAAU,YAAA,EAAE,MAAM,QAAA;iBACzB,CAAC;gBACF,OAAO,SAAS,CAAC,QAAQ,CAAmB,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;aACjE;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AArDD,oCAqDC;AAeD,SAAS,QAAQ,CAA6C,KAAuB;IAArF,iBAqBC;IApBC,IAAM,IAAI,GAAG,IAAI,CAAC;IACV,IAAA,iBAAI,EAAE,6BAAU,EAAE,qBAAM,CAAW;IACnC,IAAA,kCAAY,EAAE,wBAAO,EAAE,4BAAS,CAAY;IAC9C,IAAA,wBAAO,CAAY;IACzB,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,2BAAY,EAAK,CAAC;QAEjD,IAAM,OAAO,GAAG;YAAC,mBAAmB;iBAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;gBAAnB,8BAAmB;;YAClC,IAAM,KAAK,GAAG,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAC/D,KAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAe,YAAY,EAAE,CAAC,EAAE,EAAE,KAAK,OAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC,CAAC;QAClF,CAAC,CAAC;QAEF,IAAI;YACF,YAAY,CAAC,KAAK,CAAC,OAAO,EAAM,IAAI,SAAE,OAAO,GAAE,CAAC;SACjD;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACpB;KACF;IAED,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AAC1C,CAAC;AAOD,SAAS,YAAY,CAAyC,KAAmB;IACvE,IAAA,mBAAK,EAAE,uBAAO,CAAW;IACjC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpB,OAAO,CAAC,QAAQ,EAAE,CAAC;AACrB,CAAC;AAOD,SAAS,aAAa,CAA0C,KAAoB;IAC1E,IAAA,eAAG,EAAE,uBAAO,CAAW;IAC/B,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACrB,CAAC"}