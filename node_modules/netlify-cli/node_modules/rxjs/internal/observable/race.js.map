{"version": 3, "file": "race.js", "sources": ["../../src/internal/observable/race.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,2CAA0C;AAC1C,yCAAwC;AAKxC,sDAAqD;AAErD,+DAA8D;AAoD9D,SAAgB,IAAI;IAAI,qBAAsC;SAAtC,UAAsC,EAAtC,qBAAsC,EAAtC,IAAsC;QAAtC,gCAAsC;;IAG5D,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QAC5B,IAAI,iBAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3B,WAAW,GAAG,WAAW,CAAC,CAAC,CAAsB,CAAC;SACnD;aAAM;YACL,OAAO,WAAW,CAAC,CAAC,CAAkB,CAAC;SACxC;KACF;IAED,OAAO,qBAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,YAAY,EAAK,CAAC,CAAC;AACvE,CAAC;AAZD,oBAYC;AAED;IAAA;IAIA,CAAC;IAHC,2BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC;IAC1D,CAAC;IACH,mBAAC;AAAD,CAAC,AAJD,IAIC;AAJY,oCAAY;AAWzB;IAAuC,kCAAqB;IAK1D,wBAAY,WAA0B;QAAtC,YACE,kBAAM,WAAW,CAAC,SACnB;QANO,cAAQ,GAAY,KAAK,CAAC;QAC1B,iBAAW,GAAsB,EAAE,CAAC;QACpC,mBAAa,GAAmB,EAAE,CAAC;;IAI3C,CAAC;IAES,8BAAK,GAAf,UAAgB,UAAe;QAC7B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC;IAES,kCAAS,GAAnB;QACE,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAM,GAAG,GAAG,WAAW,CAAC,MAAM,CAAC;QAE/B,IAAI,GAAG,KAAK,CAAC,EAAE;YACb,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;SAC9B;aAAM;YACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE;gBAC9C,IAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;gBAClC,IAAM,YAAY,GAAG,qCAAiB,CAAC,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC,CAAE,CAAC;gBAExE,IAAI,IAAI,CAAC,aAAa,EAAE;oBACtB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;iBACvC;gBACD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;aACxB;YACD,IAAI,CAAC,WAAW,GAAG,IAAK,CAAC;SAC1B;IACH,CAAC;IAED,mCAAU,GAAV,UAAW,WAAc,EAAE,UAAa,EAC7B,UAAkB;QAC3B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YAErB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAClD,IAAI,CAAC,KAAK,UAAU,EAAE;oBACpB,IAAI,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBAEzC,YAAY,CAAC,WAAW,EAAE,CAAC;oBAC3B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;iBAC3B;aACF;YAED,IAAI,CAAC,aAAa,GAAG,IAAK,CAAC;SAC5B;QAED,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,UAAU,CAAC,CAAC;IACrC,CAAC;IACH,qBAAC;AAAD,CAAC,AApDD,CAAuC,iCAAe,GAoDrD;AApDY,wCAAc"}