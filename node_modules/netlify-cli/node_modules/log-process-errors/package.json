{"name": "log-process-errors", "version": "8.0.0", "type": "module", "exports": "./build/src/main.js", "main": "./build/src/main.js", "files": ["build/src/**/*.{js,ts,map,json,sh,md}", "examples/**/*.{js,ts,map,json,sh,md}"], "scripts": {"test": "gulp test"}, "description": "Show some ❤️ to process errors", "keywords": ["process", "error-handling", "error-handler", "error", "handling", "handler", "exception", "promise", "warnings", "logging", "uncaught", "uncaught-exceptions", "uncaughtException", "unhandledRejection", "rejectionHandled", "debugging", "testing", "test"], "license": "Apache-2.0", "homepage": "https://git.io/fhSGY", "repository": "ehmicky/log-process-errors", "bugs": {"url": "https://github.com/ehmicky/log-process-errors/issues"}, "author": "ehmicky <<EMAIL>> (https://github.com/ehmicky)", "directories": {"doc": "docs", "lib": "src", "test": "test"}, "dependencies": {"colors-option": "^3.0.0", "figures": "^4.0.0", "filter-obj": "^3.0.0", "jest-validate": "^27.4.2", "map-obj": "^5.0.0", "moize": "^6.1.0", "semver": "^7.3.5"}, "devDependencies": {"@ehmicky/dev-tasks": "^1.0.65", "@sinonjs/fake-timers": "^8.1.0", "execa": "^6.1.0", "has-ansi": "^5.0.1", "jasmine": "^3.10.0", "mocha": "^9.2.0", "sinon": "^13.0.0", "strip-ansi": "^7.0.1", "tap": "^15.0.10", "tape": "^5.5.2", "test-each": "^3.0.1"}, "engines": {"node": ">=12.20.0"}}