{"version": 3, "sources": ["../../../src/handle/main.js"], "names": ["handleEvent", "uncaughtException", "context", "value", "warning", "unhandledRejection", "promise", "rejectionHandled", "EVENTS"], "mappings": "AAAA,OAASA,WAAT,KAA4B,aAA5B;;;;AAIA,KAAMC,CAAAA,iBAAiB,CAAG,SAAUC,OAAV,CAAmBC,KAAnB,CAA0B;AAClDH,WAAW,CAAC,CAAE,GAAGE,OAAL,CAAcC,KAAd,CAAD,CAAX;AACD,CAFD;;AAIA,KAAMC,CAAAA,OAAO,CAAG,SAAUF,OAAV,CAAmBC,KAAnB,CAA0B;AACxCH,WAAW,CAAC,CAAE,GAAGE,OAAL,CAAcC,KAAd,CAAD,CAAX;AACD,CAFD;;AAIA,KAAME,CAAAA,kBAAkB,CAAG,SAAUH,OAAV,CAAmBC,KAAnB,CAA0BG,OAA1B,CAAmC;AAC5DN,WAAW,CAAC,CAAE,GAAGE,OAAL,CAAcI,OAAd,CAAuBH,KAAvB,CAAD,CAAX;AACD,CAFD;;AAIA,KAAMI,CAAAA,gBAAgB,CAAG,SAAUL,OAAV,CAAmBI,OAAnB,CAA4B;AACnDN,WAAW,CAAC,CAAE,GAAGE,OAAL,CAAcI,OAAd,CAAD,CAAX;AACD,CAFD;;AAIA,MAAO,MAAME,CAAAA,MAAM,CAAG;AACpBP,iBADoB;AAEpBG,OAFoB;AAGpBC,kBAHoB;AAIpBE,gBAJoB,CAAf", "sourcesContent": ["import { handleEvent } from './common.js'\n\n// List of all handled events\n// Each event must pass its related `value` to the generic `handleEvent()`\nconst uncaughtException = function (context, value) {\n  handleEvent({ ...context, value })\n}\n\nconst warning = function (context, value) {\n  handleEvent({ ...context, value })\n}\n\nconst unhandledRejection = function (context, value, promise) {\n  handleEvent({ ...context, promise, value })\n}\n\nconst rejectionHandled = function (context, promise) {\n  handleEvent({ ...context, promise })\n}\n\nexport const EVENTS = {\n  uncaughtException,\n  warning,\n  unhandledRejection,\n  rejectionHandled,\n}\n"], "file": "src/handle/main.js"}