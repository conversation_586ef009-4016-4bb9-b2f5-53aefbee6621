{"version": 3, "sources": ["../../../src/handle/common.js"], "names": ["getError", "addErrorPrint", "exitProcess", "getLevel", "isLimited", "isRepeated", "getEvent", "handleEvent", "opts", "name", "previousEvents", "mEmitLimitedWarning", "promise", "value", "nextRejected", "nextValue", "event", "logEvent", "error", "stack", "mainValue", "level", "log"], "mappings": "AAAA,OAASA,QAAT,CAAmBC,aAAnB,KAAwC,kBAAxC;AACA,OAASC,WAAT,KAA4B,YAA5B;AACA,OAASC,QAAT,KAAyB,aAAzB;AACA,OAASC,SAAT,KAA0B,aAA1B;AACA,OAASC,UAAT,KAA2B,cAA3B;;AAEA,OAASC,QAAT,KAAyB,YAAzB;;;AAGA,MAAO,MAAMC,CAAAA,WAAW,CAAG,eAAgB;AACzCC,IADyC;AAEzCC,IAFyC;AAGzCC,cAHyC;AAIzCC,mBAJyC;AAKzCC,OALyC;AAMzCC,KANyC;AAOzCC,YAPyC;AAQzCC,SARyC,CAAhB;AASxB;AACD,GAAIX,SAAS,CAAC,CAAEM,cAAF,CAAkBC,mBAAlB,CAAuCF,IAAvC,CAA6CI,KAA7C,CAAD,CAAb,CAAqE;AACnE;AACD;;AAED,KAAMG,CAAAA,KAAK,CAAG,KAAMV,CAAAA,QAAQ,CAAC;AAC3BG,IAD2B;AAE3BG,OAF2B;AAG3BC,KAH2B;AAI3BC,YAJ2B;AAK3BC,SAL2B,CAAD,CAA5B;;;AAQA,GAAIV,UAAU,CAAC,CAAEW,KAAF,CAASN,cAAT,CAAD,CAAd,CAA2C;AACzC;AACD;;AAED,KAAMO,CAAAA,QAAQ,CAAC,CAAET,IAAF,CAAQC,IAAR,CAAcO,KAAd,CAAD,CAAd;;AAEA,KAAMd,CAAAA,WAAW,CAAC,CAAEO,IAAF,CAAQD,IAAR,CAAD,CAAjB;AACD,CA7BM;;AA+BP,KAAMS,CAAAA,QAAQ,CAAG,eAAgB,CAAET,IAAF,CAAQC,IAAR,CAAcO,KAAd,CAAhB,CAAuC;AACtD,KAAM,CAAEE,KAAF,CAASC,KAAT,CAAgBC,SAAhB,EAA8BpB,QAAQ,CAAC,CAAES,IAAF,CAAQO,KAAR,CAAD,CAA5C;;AAEA,KAAMK,CAAAA,KAAK,CAAGlB,QAAQ,CAAC,CAAEK,IAAF,CAAQC,IAAR,CAAcS,KAAd,CAAD,CAAtB;;AAEA,GAAIG,KAAK,GAAK,QAAd,CAAwB;AACtB;AACD;;AAEDpB,aAAa,CAAC,CAAEiB,KAAF,CAASV,IAAT,CAAea,KAAf,CAAsBZ,IAAtB,CAA4BU,KAA5B,CAAD,CAAb;;;AAGA,KAAMX,CAAAA,IAAI,CAACc,GAAL,CAASJ,KAAT,CAAgBG,KAAhB,CAAuBD,SAAvB,CAAN;AACD,CAbD", "sourcesContent": ["import { getError, addErrorPrint } from '../error/main.js'\nimport { exitProcess } from '../exit.js'\nimport { getLevel } from '../level.js'\nimport { isLimited } from '../limit.js'\nimport { isRepeated } from '../repeat.js'\n\nimport { getEvent } from './event.js'\n\n// Generic event handler for all events.\nexport const handleEvent = async function ({\n  opts,\n  name,\n  previousEvents,\n  mEmitLimitedWarning,\n  promise,\n  value,\n  nextRejected,\n  nextValue,\n}) {\n  if (isLimited({ previousEvents, mEmitLimitedWarning, name, value })) {\n    return\n  }\n\n  const event = await getEvent({\n    name,\n    promise,\n    value,\n    nextRejected,\n    nextValue,\n  })\n\n  if (isRepeated({ event, previousEvents })) {\n    return\n  }\n\n  await logEvent({ opts, name, event })\n\n  await exitProcess({ name, opts })\n}\n\nconst logEvent = async function ({ opts, name, event }) {\n  const { error, stack, mainValue } = getError({ name, event })\n\n  const level = getLevel({ opts, name, error })\n\n  if (level === 'silent') {\n    return\n  }\n\n  addErrorPrint({ error, opts, level, name, stack })\n\n  // See `exit.js` on why we need to `await`\n  await opts.log(error, level, mainValue)\n}\n"], "file": "src/handle/common.js"}