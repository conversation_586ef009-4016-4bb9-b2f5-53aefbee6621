{"version": 3, "sources": ["../../../src/handle/event.js"], "names": ["filterObj", "getEvent", "name", "promise", "value", "nextRejected", "nextValue", "rejected", "valueA", "parsePromise", "event", "eventA", "isDefined", "NO_PROMISE_EVENTS", "has", "getPromiseValue", "Set", "error", "key", "undefined"], "mappings": "AAAA,MAAOA,CAAAA,SAAP,KAAsB,YAAtB;;;AAGA,MAAO,MAAMC,CAAAA,QAAQ,CAAG,eAAgB;AACtCC,IADsC;AAEtCC,OAFsC;AAGtCC,KAHsC;AAItCC,YAJsC;AAKtCC,SALsC,CAAhB;AAMrB;AACD,KAAM,CAAEC,QAAF,CAAYH,KAAK,CAAEI,MAAnB,EAA8B,KAAMC,CAAAA,YAAY,CAAC;AACrDP,IADqD;AAErDC,OAFqD;AAGrDC,KAHqD,CAAD,CAAtD;;;AAMA,KAAMM,CAAAA,KAAK,CAAG,CAAEH,QAAF,CAAYH,KAAK,CAAEI,MAAnB,CAA2BH,YAA3B,CAAyCC,SAAzC,CAAd;;AAEA,KAAMK,CAAAA,MAAM,CAAGX,SAAS,CAACU,KAAD,CAAQE,SAAR,CAAxB;AACA,MAAOD,CAAAA,MAAP;AACD,CAjBM;;;AAoBP,KAAMF,CAAAA,YAAY,CAAG,eAAgB,CAAEP,IAAF,CAAQC,OAAR,CAAiBC,KAAjB,CAAhB,CAA0C;AAC7D,GAAIS,iBAAiB,CAACC,GAAlB,CAAsBZ,IAAtB,CAAJ,CAAiC;AAC/B,MAAO,CAAEE,KAAF,CAAP;AACD;;AAED,KAAM,CAAEG,QAAF,CAAYH,KAAK,CAAEI,MAAnB,EAA8B,KAAMO,CAAAA,eAAe,CAAC,CAAEZ,OAAF,CAAD,CAAzD;;;AAGA,GAAID,IAAI,GAAK,kBAAb,CAAiC;AAC/B,MAAO,CAAEE,KAAK,CAAEI,MAAT,CAAP;AACD;;AAED,MAAO,CAAED,QAAF,CAAYH,KAAK,CAAEI,MAAnB,CAAP;AACD,CAbD;;;;;;;AAoBA,KAAMK,CAAAA,iBAAiB,CAAG,GAAIG,CAAAA,GAAJ,CAAQ;AAChC,mBADgC;AAEhC,SAFgC;AAGhC,oBAHgC,CAAR,CAA1B;;;;AAOA,KAAMD,CAAAA,eAAe,CAAG,eAAgB,CAAEZ,OAAF,CAAhB,CAA6B;AACnD,GAAI;AACF,MAAO,CAAEI,QAAQ,CAAE,KAAZ,CAAmBH,KAAK,CAAE,KAAMD,CAAAA,OAAhC,CAAP;AACD,CAAC,MAAOc,KAAP,CAAc;AACd,MAAO,CAAEV,QAAQ,CAAE,IAAZ,CAAkBH,KAAK,CAAEa,KAAzB,CAAP;AACD;AACF,CAND;;AAQA,KAAML,CAAAA,SAAS,CAAG,SAAUM,GAAV,CAAed,KAAf,CAAsB;AACtC,MAAOA,CAAAA,KAAK,GAAKe,SAAjB;AACD,CAFD", "sourcesContent": ["import filterObj from 'filter-obj'\n\n// Retrieve `event` object representing the current event information\nexport const getEvent = async function ({\n  name,\n  promise,\n  value,\n  nextRejected,\n  nextValue,\n}) {\n  const { rejected, value: valueA } = await parsePromise({\n    name,\n    promise,\n    value,\n  })\n\n  const event = { rejected, value: valueA, nextRejected, nextValue }\n\n  const eventA = filterObj(event, isDefined)\n  return eventA\n}\n\n// Retrieve promise's resolved/rejected state and value.\nconst parsePromise = async function ({ name, promise, value }) {\n  if (NO_PROMISE_EVENTS.has(name)) {\n    return { value }\n  }\n\n  const { rejected, value: valueA } = await getPromiseValue({ promise })\n\n  // `rejected` is always `true` with `rejectionHandled`, so we skip it\n  if (name === 'rejectionHandled') {\n    return { value: valueA }\n  }\n\n  return { rejected, value: valueA }\n}\n\n// Those events do not try to get the promise value.\n// For `uncaughtException` and `warning`, they are not promise-specific.\n// For `unhandledRejection`:\n//  - we already know `rejected` and `value`\n//  - using `try/catch` will fire `rejectionHandled`\nconst NO_PROMISE_EVENTS = new Set([\n  'uncaughtException',\n  'warning',\n  'unhandledRejection',\n])\n\n// `rejectionHandled` otherwise use `await promise`\nconst getPromiseValue = async function ({ promise }) {\n  try {\n    return { rejected: false, value: await promise }\n  } catch (error) {\n    return { rejected: true, value: error }\n  }\n}\n\nconst isDefined = function (key, value) {\n  return value !== undefined\n}\n"], "file": "src/handle/event.js"}