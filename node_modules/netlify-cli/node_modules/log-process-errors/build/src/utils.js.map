{"version": 3, "sources": ["../../src/utils.js"], "names": ["result", "val", "args"], "mappings": ";AACA,MAAO,MAAMA,CAAAA,MAAM,CAAG,SAAUC,GAAV,CAAe,GAAGC,IAAlB,CAAwB;AAC5C,GAAI,MAAOD,CAAAA,GAAP,GAAe,UAAnB,CAA+B;AAC7B,MAAOA,CAAAA,GAAP;AACD;;AAED,MAAOA,CAAAA,GAAG,CAAC,GAAGC,IAAJ,CAAV;AACD,CANM", "sourcesContent": ["// Like Lodash result(), but faster\nexport const result = function (val, ...args) {\n  if (typeof val !== 'function') {\n    return val\n  }\n\n  return val(...args)\n}\n"], "file": "src/utils.js"}