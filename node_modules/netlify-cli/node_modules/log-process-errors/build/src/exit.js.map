{"version": 3, "sources": ["../../src/exit.js"], "names": ["process", "exitProcess", "name", "opts", "exitOn", "includes", "setTimeout", "exit", "EXIT_STATUS", "EXIT_TIMEOUT", "validateExitOn", "undefined", "invalidEvents", "filter", "EVENTS", "has", "length", "Error", "join", "EVENTS_ARR", "Set"], "mappings": ";AACA,MAAOA,CAAAA,OAAP,KAAoB,SAApB;;;;;;;;;;;;;;;;;AAiBA,MAAO,MAAMC,CAAAA,WAAW,CAAG,SAAU,CAAEC,IAAF,CAAQC,IAAI,CAAE,CAAEC,MAAF,CAAd,CAAV,CAAsC;AAC/D,GAAI,CAACA,MAAM,CAACC,QAAP,CAAgBH,IAAhB,CAAL,CAA4B;AAC1B;AACD;;;;;;AAMDI,UAAU,CAAC,IAAM;;AAEfN,OAAO,CAACO,IAAR,CAAaC,WAAb;AACD,CAHS,CAGPC,YAHO,CAAV;AAID,CAbM;;AAeP,KAAMA,CAAAA,YAAY,CAAG,IAArB;AACA,KAAMD,CAAAA,WAAW,CAAG,CAApB;;AAEA,MAAO,MAAME,CAAAA,cAAc,CAAG,SAAU,CAAEN,MAAF,CAAV,CAAsB;AAClD,GAAIA,MAAM,GAAKO,SAAf,CAA0B;AACxB;AACD;;AAED,KAAMC,CAAAA,aAAa,CAAGR,MAAM,CAACS,MAAP,CAAc,CAACX,IAAD,GAAU,CAACY,MAAM,CAACC,GAAP,CAAWb,IAAX,CAAzB,CAAtB;;AAEA,GAAIU,aAAa,CAACI,MAAd,GAAyB,CAA7B,CAAgC;AAC9B;AACD;;AAED,KAAM,IAAIC,CAAAA,KAAJ;AACH,4BAA2BL,aAAa,CAACM,IAAd;AAC1B,IAD0B;AAE1B,qBAAoBC,UAAU,CAACD,IAAX,CAAgB,IAAhB,CAAsB,EAHxC,CAAN;;AAKD,CAhBM;;AAkBP,KAAMC,CAAAA,UAAU,CAAG;AACjB,mBADiB;AAEjB,oBAFiB;AAGjB,kBAHiB;AAIjB,SAJiB,CAAnB;;AAMA,KAAML,CAAAA,MAAM,CAAG,GAAIM,CAAAA,GAAJ,CAAQD,UAAR,CAAf", "sourcesContent": ["// Do not destructure so tests can stub it\nimport process from 'process'\n\n// Exit process according to `opts.exitOn` (default: ['uncaughtException']):\n//  - `uncaughtException`: default behavior of Node.js and recommended by\n//     https://nodejs.org/api/process.html#process_warning_using_uncaughtexception_correctly\n//  - `unhandledRejection`: default behavior of Node.js since 15.0.0\n// `process.exit()` unfortunately aborts any current async operations and\n// streams are not flushed (including stdout/stderr):\n//  - https://github.com/nodejs/node/issues/784\n//  - https://github.com/nodejs/node/issues/6456\n// We go around this problem by:\n//  - await promise returned by `opts.log()`\n//  - waiting for few seconds (EXIT_TIMEOUT)\n// This last one is a hack. We should instead allow `opts.log()` to return a\n// stream, and keep track of all unique returned streams. On exit, we should\n// then close them and wait for them to flush. We should then always wait for\n// process.stdout|stderr as well.\nexport const exitProcess = function ({ name, opts: { exitOn } }) {\n  if (!exitOn.includes(name)) {\n    return\n  }\n\n  // TODO: use `promisify` instead after\n  // https://github.com/sinonjs/fake-timers/issues/223 is fixed\n  // TODO: replace with `timers/promises` `setTimeout()` after dropping support\n  // for Node <15.0.0\n  setTimeout(() => {\n    // eslint-disable-next-line unicorn/no-process-exit, n/no-process-exit\n    process.exit(EXIT_STATUS)\n  }, EXIT_TIMEOUT)\n}\n\nconst EXIT_TIMEOUT = 3000\nconst EXIT_STATUS = 1\n\nexport const validateExitOn = function ({ exitOn }) {\n  if (exitOn === undefined) {\n    return\n  }\n\n  const invalidEvents = exitOn.filter((name) => !EVENTS.has(name))\n\n  if (invalidEvents.length === 0) {\n    return\n  }\n\n  throw new Error(\n    `Invalid option 'exitOn' '${invalidEvents.join(\n      ', ',\n    )}': must be one of ${EVENTS_ARR.join(', ')}`,\n  )\n}\n\nconst EVENTS_ARR = [\n  'uncaughtException',\n  'unhandledRejection',\n  'rejectionHandled',\n  'warning',\n]\nconst EVENTS = new Set(EVENTS_ARR)\n"], "file": "src/exit.js"}