{"version": 3, "sources": ["../../src/level.js"], "names": ["emitWarning", "filterObj", "multipleValidOptions", "mapObj", "result", "getLevel", "opts", "name", "error", "level", "undefined", "DEFAULT_LEVEL", "LEVELS", "has", "LEVELS_ARR", "join", "applyDefaultLevels", "default", "defaultLevel", "levelA", "isDefined", "defaultLevels", "eventName", "key", "value", "getExampleLevels", "getExampleLevel", "validateLevels", "Object", "entries", "for<PERSON>ach", "validateLevel", "isValidLevel", "Error", "Set", "uncaughtException", "warning", "unhandledRejection", "rejectionHandled"], "mappings": "AAAA,OAASA,WAAT,KAA4B,SAA5B;;AAEA,MAAOC,CAAAA,SAAP,KAAsB,YAAtB;AACA,OAASC,oBAAT,KAAqC,eAArC;AACA,MAAOC,CAAAA,MAAP,KAAmB,SAAnB;;AAEA,OAASC,MAAT,KAAuB,YAAvB;;;AAGA,MAAO,MAAMC,CAAAA,QAAQ,CAAG,SAAU,CAAEC,IAAF,CAAQC,IAAR,CAAcC,KAAd,CAAV,CAAiC;AACvD,KAAMC,CAAAA,KAAK,CAAGL,MAAM,CAACE,IAAI,CAACG,KAAL,CAAWF,IAAX,CAAD,CAAmBC,KAAnB,CAApB;;AAEA,GAAIC,KAAK,GAAK,SAAV,EAAuBA,KAAK,GAAKC,SAArC,CAAgD;AAC9C,MAAOC,CAAAA,aAAa,CAACJ,IAAD,CAApB;AACD;;AAED,GAAIK,MAAM,CAACC,GAAP,CAAWJ,KAAX,CAAJ,CAAuB;AACrB,MAAOA,CAAAA,KAAP;AACD;;AAEDT,WAAW;AACR,yBAAwBO,IAAK,gBAAeE,KAAM,kCAAiCK,UAAU,CAACC,IAAX;AAClF,IADkF;AAElF,EAHO,CAAX;;;AAMA,MAAOJ,CAAAA,aAAa,CAACJ,IAAD,CAApB;AACD,CAlBM;;;AAqBP,MAAO,MAAMS,CAAAA,kBAAkB,CAAG,SAAU;AAC1CV,IAAI,CAAE,CAAEG,KAAK,CAAE,CAAEQ,OAAO,CAAEC,YAAX,CAAyB,GAAGT,KAA5B,EAAsC,EAA/C,CADoC,CAAV;AAE/B;AACD,KAAMU,CAAAA,MAAM,CAAGlB,SAAS,CAACQ,KAAD,CAAQW,SAAR,CAAxB;;AAEA,GAAIF,YAAY,GAAKR,SAArB,CAAgC;AAC9B,MAAO,CAAE,GAAGC,aAAL,CAAoB,GAAGQ,MAAvB,CAAP;AACD;;AAED,KAAME,CAAAA,aAAa,CAAGlB,MAAM,CAACQ,aAAD,CAAgB,CAACW,SAAD,GAAe;AACzDA,SADyD;AAEzDJ,YAFyD,CAA/B,CAA5B;;AAIA,MAAO,CAAE,GAAGG,aAAL,CAAoB,GAAGF,MAAvB,CAAP;AACD,CAdM;;AAgBP,KAAMC,CAAAA,SAAS,CAAG,SAAUG,GAAV,CAAeC,KAAf,CAAsB;AACtC,MAAOA,CAAAA,KAAK,GAAKd,SAAjB;AACD,CAFD;;;AAKA,MAAO,MAAMe,CAAAA,gBAAgB,CAAG,UAAY;AAC1C,MAAOtB,CAAAA,MAAM,CAACQ,aAAD,CAAgBe,eAAhB,CAAb;AACD,CAFM;;AAIP,KAAMA,CAAAA,eAAe,CAAG,SAAUJ,SAAV,CAAqBb,KAArB,CAA4B;;AAElD,MAAO,CAACa,SAAD,CAAYpB,oBAAoB,CAACO,KAAD,CAAQ,IAAM,CAAE,CAAhB,CAAhC,CAAP;AACD,CAHD;;AAKA,MAAO,MAAMkB,CAAAA,cAAc,CAAG,SAAU,CAAElB,KAAF,CAAV,CAAqB;AACjDmB,MAAM,CAACC,OAAP,CAAepB,KAAf,EAAsBqB,OAAtB,CAA8BC,aAA9B;AACD,CAFM;;AAIP,KAAMA,CAAAA,aAAa,CAAG,SAAU,CAACxB,IAAD,CAAOE,KAAP,CAAV,CAAyB;AAC7C,GAAIuB,YAAY,CAAC,CAAEvB,KAAF,CAAD,CAAhB,CAA6B;AAC3B;AACD;;AAED,KAAM,IAAIwB,CAAAA,KAAJ;AACH,yBAAwB1B,IAAK,MAAKE,KAAM,mCAAkCK,UAAU,CAACC,IAAX;AACzE,IADyE;AAEzE,EAHE,CAAN;;AAKD,CAVD;;AAYA,KAAMiB,CAAAA,YAAY,CAAG,SAAU,CAAEvB,KAAF,CAAV,CAAqB;AACxC,MAAOG,CAAAA,MAAM,CAACC,GAAP,CAAWJ,KAAX,GAAqBA,KAAK,GAAKC,SAA/B,EAA4C,MAAOD,CAAAA,KAAP,GAAiB,UAApE;AACD,CAFD;;AAIA,KAAMK,CAAAA,UAAU,CAAG,CAAC,OAAD,CAAU,MAAV,CAAkB,MAAlB,CAA0B,OAA1B,CAAmC,QAAnC,CAA6C,SAA7C,CAAnB;AACA,KAAMF,CAAAA,MAAM,CAAG,GAAIsB,CAAAA,GAAJ,CAAQpB,UAAR,CAAf;;AAEA,KAAMH,CAAAA,aAAa,CAAG;AACpBM,OAAO,CAAE,OADW;AAEpBkB,iBAAiB,CAAE,OAFC;AAGpBC,OAAO,CAAE,MAHW;AAIpBC,kBAAkB,CAAE,OAJA;AAKpBC,gBAAgB,CAAE,OALE,CAAtB", "sourcesContent": ["import { emitWarning } from 'process'\n\nimport filterObj from 'filter-obj'\nimport { multipleValidOptions } from 'jest-validate'\nimport mapObj from 'map-obj'\n\nimport { result } from './utils.js'\n\n// Retrieve error's log level\nexport const getLevel = function ({ opts, name, error }) {\n  const level = result(opts.level[name], error)\n\n  if (level === 'default' || level === undefined) {\n    return DEFAULT_LEVEL[name]\n  }\n\n  if (LEVELS.has(level)) {\n    return level\n  }\n\n  emitWarning(\n    `Invalid option 'level.${name}' returning '${level}': function must return one of ${LEVELS_ARR.join(\n      ', ',\n    )}`,\n  )\n\n  return DEFAULT_LEVEL[name]\n}\n\n// Apply `opts.level.default` and default values to `opts.level`\nexport const applyDefaultLevels = function ({\n  opts: { level: { default: defaultLevel, ...level } = {} },\n}) {\n  const levelA = filterObj(level, isDefined)\n\n  if (defaultLevel === undefined) {\n    return { ...DEFAULT_LEVEL, ...levelA }\n  }\n\n  const defaultLevels = mapObj(DEFAULT_LEVEL, (eventName) => [\n    eventName,\n    defaultLevel,\n  ])\n  return { ...defaultLevels, ...levelA }\n}\n\nconst isDefined = function (key, value) {\n  return value !== undefined\n}\n\n// Use during options validation\nexport const getExampleLevels = function () {\n  return mapObj(DEFAULT_LEVEL, getExampleLevel)\n}\n\nconst getExampleLevel = function (eventName, level) {\n  // eslint-disable-next-line no-empty-function\n  return [eventName, multipleValidOptions(level, () => {})]\n}\n\nexport const validateLevels = function ({ level }) {\n  Object.entries(level).forEach(validateLevel)\n}\n\nconst validateLevel = function ([name, level]) {\n  if (isValidLevel({ level })) {\n    return\n  }\n\n  throw new Error(\n    `Invalid option 'level.${name}' '${level}': must be a function or one of ${LEVELS_ARR.join(\n      ', ',\n    )}`,\n  )\n}\n\nconst isValidLevel = function ({ level }) {\n  return LEVELS.has(level) || level === undefined || typeof level === 'function'\n}\n\nconst LEVELS_ARR = ['debug', 'info', 'warn', 'error', 'silent', 'default']\nconst LEVELS = new Set(LEVELS_ARR)\n\nconst DEFAULT_LEVEL = {\n  default: 'error',\n  uncaughtException: 'error',\n  warning: 'warn',\n  unhandledRejection: 'error',\n  rejectionHandled: 'error',\n}\n"], "file": "src/level.js"}