{"version": 3, "sources": ["../../src/main.js"], "names": ["process", "moize", "EVENTS", "emitLimitedWarning", "getOptions", "removeWarningListener", "restoreWarningListener", "logProcessErrors", "opts", "optsA", "listeners", "addListeners", "stopLogProcessErrors", "stopLogging", "Object", "entries", "map", "name", "eventFunc", "addListener", "previousEvents", "Set", "mEmitLimitedWarning", "maxSize", "Number", "POSITIVE_INFINITY", "eventListener", "bind", "undefined", "on", "for<PERSON>ach", "removeListener", "off"], "mappings": "AAAA,MAAOA,CAAAA,OAAP,KAAoB,SAApB;;AAEA,MAAOC,CAAAA,KAAP,KAAkB,OAAlB;;AAEA,OAASC,MAAT,KAAuB,kBAAvB;AACA,OAASC,kBAAT,KAAmC,YAAnC;AACA,OAASC,UAAT,KAA2B,mBAA3B;AACA,OAASC,qBAAT,CAAgCC,sBAAhC,KAA8D,eAA9D;;;AAGA,cAAe,SAASC,CAAAA,gBAAT,CAA0BC,IAA1B,CAAgC;AAC7C,KAAMC,CAAAA,KAAK,CAAGL,UAAU,CAAC,CAAEI,IAAF,CAAD,CAAxB;;AAEAH,qBAAqB;;AAErB,KAAMK,CAAAA,SAAS,CAAGC,YAAY,CAAC,CAAEH,IAAI,CAAEC,KAAR,CAAD,CAA9B;;;AAGA,KAAMG,CAAAA,oBAAoB,CAAG,IAAMC,WAAW,CAACH,SAAD,CAA9C;AACA,MAAOE,CAAAA,oBAAP;AACD;;AAED,KAAMD,CAAAA,YAAY,CAAG,SAAU,CAAEH,IAAF,CAAV,CAAoB;AACvC,MAAOM,CAAAA,MAAM,CAACC,OAAP,CAAeb,MAAf,EAAuBc,GAAvB,CAA2B,CAAC,CAACC,IAAD,CAAOC,SAAP,CAAD;AAChCC,WAAW,CAAC,CAAEX,IAAF,CAAQS,IAAR,CAAcC,SAAd,CAAD,CADN,CAAP;;AAGD,CAJD;;AAMA,KAAMC,CAAAA,WAAW,CAAG,SAAU,CAAEX,IAAF,CAAQS,IAAR,CAAcC,SAAd,CAAV,CAAqC;;;;;AAKvD,KAAME,CAAAA,cAAc,CAAG,GAAIC,CAAAA,GAA3B;;AAEA,KAAMC,CAAAA,mBAAmB,CAAGrB,KAAK,CAACE,kBAAD,CAAqB;AACpDoB,OAAO,CAAEC,MAAM,CAACC,iBADoC,CAArB,CAAjC;;;AAIA,KAAMC,CAAAA,aAAa,CAAGR,SAAS,CAACS,IAAV,CAAeC,SAAf,CAA0B;AAC9CpB,IAD8C;AAE9CS,IAF8C;AAG9CG,cAH8C;AAI9CE,mBAJ8C,CAA1B,CAAtB;;AAMAtB,OAAO,CAAC6B,EAAR,CAAWZ,IAAX,CAAiBS,aAAjB;;AAEA,MAAO,CAAEA,aAAF,CAAiBT,IAAjB,CAAP;AACD,CApBD;;;AAuBA,KAAMJ,CAAAA,WAAW,CAAG,SAAUH,SAAV,CAAqB;AACvCA,SAAS,CAACoB,OAAV,CAAkBC,cAAlB;AACAzB,sBAAsB;AACvB,CAHD;;AAKA,KAAMyB,CAAAA,cAAc,CAAG,SAAU,CAAEL,aAAF,CAAiBT,IAAjB,CAAV,CAAmC;AACxDjB,OAAO,CAACgC,GAAR,CAAYf,IAAZ,CAAkBS,aAAlB;AACD,CAFD", "sourcesContent": ["import process from 'process'\n\nimport moize from 'moize'\n\nimport { EVENTS } from './handle/main.js'\nimport { emitLimitedWarning } from './limit.js'\nimport { getOptions } from './options/main.js'\nimport { removeWarningListener, restoreWarningListener } from './warnings.js'\n\n// Add event handling for all process-related errors\nexport default function logProcessErrors(opts) {\n  const optsA = getOptions({ opts })\n\n  removeWarningListener()\n\n  const listeners = addListeners({ opts: optsA })\n\n  // Do not use `function.bind()` to keep the right `function.name`\n  const stopLogProcessErrors = () => stopLogging(listeners)\n  return stopLogProcessErrors\n}\n\nconst addListeners = function ({ opts }) {\n  return Object.entries(EVENTS).map(([name, eventFunc]) =>\n    addListener({ opts, name, eventFunc }),\n  )\n}\n\nconst addListener = function ({ opts, name, eventFunc }) {\n  // `previousEvents` is event-name-specific so that if events of a given event\n  // stopped being emitted, others still are.\n  // `previousEvents` can take up some memory, but it should be cleaned up\n  // by `removeListener()`, i.e. once `eventListener` is garbage collected.\n  const previousEvents = new Set()\n  // Should only emit the warning once per event name and per `init()`\n  const mEmitLimitedWarning = moize(emitLimitedWarning, {\n    maxSize: Number.POSITIVE_INFINITY,\n  })\n\n  const eventListener = eventFunc.bind(undefined, {\n    opts,\n    name,\n    previousEvents,\n    mEmitLimitedWarning,\n  })\n  process.on(name, eventListener)\n\n  return { eventListener, name }\n}\n\n// Remove all event handlers and restore previous `warning` listeners\nconst stopLogging = function (listeners) {\n  listeners.forEach(removeListener)\n  restoreWarningListener()\n}\n\nconst removeListener = function ({ eventListener, name }) {\n  process.off(name, eventListener)\n}\n"], "file": "src/main.js"}