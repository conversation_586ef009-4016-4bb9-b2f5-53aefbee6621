{"version": 3, "sources": ["../../src/repeat.js"], "names": ["inspect", "isRepeated", "event", "previousEvents", "fingerprint", "getFingerprint", "isRepeatedEvent", "has", "add", "entries", "EVENT_PROPS", "map", "propName", "serializeEntry", "eventA", "Object", "assign", "JSON", "stringify", "fingerprintA", "slice", "FINGERPRINT_MAX_LENGTH", "value", "undefined", "valueA", "serializeValue", "Error", "serializeError", "stableSerialize", "name", "stack", "stackA", "filterErrorStack", "split", "filter", "line", "STACK_TRACE_LINE_REGEXP", "test", "STACK_TRACE_MAX_LENGTH", "join", "INSPECT_OPTS", "getters", "sorted"], "mappings": "AAAA,OAASA,OAAT,KAAwB,MAAxB;;;;;;;;;AASA,MAAO,MAAMC,CAAAA,UAAU,CAAG,SAAU,CAAEC,KAAF,CAASC,cAAT,CAAV,CAAqC;AAC7D,KAAMC,CAAAA,WAAW,CAAGC,cAAc,CAAC,CAAEH,KAAF,CAAD,CAAlC;;AAEA,KAAMI,CAAAA,eAAe,CAAGH,cAAc,CAACI,GAAf,CAAmBH,WAAnB,CAAxB;;AAEA,GAAI,CAACE,eAAL,CAAsB;AACpBH,cAAc,CAACK,GAAf,CAAmBJ,WAAnB;AACD;;AAED,MAAOE,CAAAA,eAAP;AACD,CAVM;;;AAaP,KAAMD,CAAAA,cAAc,CAAG,SAAU,CAAEH,KAAF,CAAV,CAAqB;AAC1C,KAAMO,CAAAA,OAAO,CAAGC,WAAW,CAACC,GAAZ,CAAgB,CAACC,QAAD;AAC9BC,cAAc,CAAC,CAAEX,KAAF,CAASU,QAAT,CAAD,CADA,CAAhB;;AAGA,KAAME,CAAAA,MAAM,CAAGC,MAAM,CAACC,MAAP,CAAc,EAAd,CAAkB,GAAGP,OAArB,CAAf;;AAEA,KAAML,CAAAA,WAAW,CAAGa,IAAI,CAACC,SAAL,CAAeJ,MAAf,CAApB;;;;;;AAMA,KAAMK,CAAAA,YAAY,CAAGf,WAAW,CAACgB,KAAZ,CAAkB,CAAlB,CAAqBC,sBAArB,CAArB;AACA,MAAOF,CAAAA,YAAP;AACD,CAdD;;;;;AAmBA,KAAMT,CAAAA,WAAW,CAAG,CAAC,cAAD,CAAiB,UAAjB,CAA6B,WAA7B,CAA0C,OAA1C,CAApB;;AAEA,KAAMW,CAAAA,sBAAsB,CAAG,GAA/B;;AAEA,KAAMR,CAAAA,cAAc,CAAG,SAAU,CAAEX,KAAF,CAASU,QAAT,CAAV,CAA+B;AACpD,KAAMU,CAAAA,KAAK,CAAGpB,KAAK,CAACU,QAAD,CAAnB;;AAEA,GAAIU,KAAK,GAAKC,SAAd,CAAyB;AACvB;AACD;;AAED,KAAMC,CAAAA,MAAM,CAAGC,cAAc,CAAC,CAAEH,KAAF,CAAD,CAA7B;AACA,MAAO,CAAE,CAACV,QAAD,EAAYY,MAAd,CAAP;AACD,CATD;;AAWA,KAAMC,CAAAA,cAAc,CAAG,SAAU,CAAEH,KAAF,CAAV,CAAqB;AAC1C,GAAIA,KAAK,WAAYI,CAAAA,KAArB,CAA4B;AAC1B,MAAOC,CAAAA,cAAc,CAACL,KAAD,CAArB;AACD;;AAED,MAAOM,CAAAA,eAAe,CAACN,KAAD,CAAtB;AACD,CAND;;;;;;AAYA,KAAMK,CAAAA,cAAc,CAAG,SAAU,CAAEE,IAAF,CAAQC,KAAR,CAAV,CAA2B;AAChD,KAAMC,CAAAA,MAAM,CAAGC,gBAAgB,CAAC,CAAEF,KAAF,CAAD,CAA/B;AACA,MAAQ,GAAED,IAAK,KAAIE,MAAO,EAA1B;AACD,CAHD;;AAKA,KAAMC,CAAAA,gBAAgB,CAAG,SAAU,CAAEF,KAAF,CAAV,CAAqB;AAC5C,MAAOA,CAAAA,KAAK;AACTG,KADI,CACE,IADF;AAEJC,MAFI,CAEG,CAACC,IAAD,GAAUC,uBAAuB,CAACC,IAAxB,CAA6BF,IAA7B,CAFb;AAGJf,KAHI,CAGE,CAHF,CAGKkB,sBAHL;AAIJC,IAJI,CAIC,IAJD,CAAP;AAKD,CAND;;AAQA,KAAMH,CAAAA,uBAAuB,CAAG,UAAhC;AACA,KAAME,CAAAA,sBAAsB,CAAG,EAA/B;;;;;;;;;;;;AAYA,KAAMV,CAAAA,eAAe,CAAG,SAAUN,KAAV,CAAiB;AACvC,MAAOtB,CAAAA,OAAO,CAACsB,KAAD,CAAQkB,YAAR,CAAd;AACD,CAFD;;AAIA,KAAMA,CAAAA,YAAY,CAAG,CAAEC,OAAO,CAAE,IAAX,CAAiBC,MAAM,CAAE,IAAzB,CAArB", "sourcesContent": ["import { inspect } from 'util'\n\n// Events with the same `event` are only logged once because:\n//  - it makes logs clearer\n//  - it prevents creating too much CPU load or too many microtasks\n//  - it prevents creating too many logs, which can be expensive if logs are\n//    hosted remotely\n//  - it prevents infinite recursions if `opts.log|level()` triggers itself an\n//    event (while still reporting that event once)\nexport const isRepeated = function ({ event, previousEvents }) {\n  const fingerprint = getFingerprint({ event })\n\n  const isRepeatedEvent = previousEvents.has(fingerprint)\n\n  if (!isRepeatedEvent) {\n    previousEvents.add(fingerprint)\n  }\n\n  return isRepeatedEvent\n}\n\n// Serialize `event` into a short fingerprint\nconst getFingerprint = function ({ event }) {\n  const entries = EVENT_PROPS.map((propName) =>\n    serializeEntry({ event, propName }),\n  )\n  const eventA = Object.assign({}, ...entries)\n\n  const fingerprint = JSON.stringify(eventA)\n\n  // We truncate fingerprints to prevent consuming too much memory in case some\n  // `event` properties are huge.\n  // This introduces higher risk of false positives (see comment below).\n  // We do not hash as it would be too CPU-intensive if the value is huge.\n  const fingerprintA = fingerprint.slice(0, FINGERPRINT_MAX_LENGTH)\n  return fingerprintA\n}\n\n// We do not serialize `name` since this is already `name-wise`\n// Key order matters since fingerprint might be truncated: we serialize short\n// and non-dynamic values first.\nconst EVENT_PROPS = ['nextRejected', 'rejected', 'nextValue', 'value']\n\nconst FINGERPRINT_MAX_LENGTH = 1e4\n\nconst serializeEntry = function ({ event, propName }) {\n  const value = event[propName]\n\n  if (value === undefined) {\n    return\n  }\n\n  const valueA = serializeValue({ value })\n  return { [propName]: valueA }\n}\n\nconst serializeValue = function ({ value }) {\n  if (value instanceof Error) {\n    return serializeError(value)\n  }\n\n  return stableSerialize(value)\n}\n\n// We do not serialize `error.message` as it may contain dynamic values like\n// timestamps. This means errors are only `error.name` + `error.stack`, which\n// should be a good fingerprint.\n// Also we only keep first 10 callsites in case of infinitely recursive stack.\nconst serializeError = function ({ name, stack }) {\n  const stackA = filterErrorStack({ stack })\n  return `${name}\\n${stackA}`\n}\n\nconst filterErrorStack = function ({ stack }) {\n  return stack\n    .split('\\n')\n    .filter((line) => STACK_TRACE_LINE_REGEXP.test(line))\n    .slice(0, STACK_TRACE_MAX_LENGTH)\n    .join('\\n')\n}\n\nconst STACK_TRACE_LINE_REGEXP = /^\\s+at /u\nconst STACK_TRACE_MAX_LENGTH = 10\n\n// This is meant when serialization must be predictable.\n// We use `util.inspect()` instead of `JSON.stringify()` to support more\n// types and circular references.\n// `sorted` prevents the same event using different keys order from having\n// a different fingerprint.\n// Big arrays, objects or buffers will be truncated, which makes this call\n// less CPU-intensive and the result value smaller in memory. However it\n// introduces higher risk of false positives (event being flagged as repeated\n// even though it's different). Process errors should be exceptional, so this\n// is ok.\nconst stableSerialize = function (value) {\n  return inspect(value, INSPECT_OPTS)\n}\n\nconst INSPECT_OPTS = { getters: true, sorted: true }\n"], "file": "src/repeat.js"}