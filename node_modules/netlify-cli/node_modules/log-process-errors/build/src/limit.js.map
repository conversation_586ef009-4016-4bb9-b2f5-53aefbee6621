{"version": 3, "sources": ["../../src/limit.js"], "names": ["emitWarning", "isLimited", "previousEvents", "mEmitLimitedWarning", "name", "value", "isLimitedWarning", "isLimitedEvent", "length", "MAX_EVENTS", "emitLimitedWarning", "ERROR_MESSAGE", "ERROR_NAME", "ERROR_CODE", "code"], "mappings": "AAAA,OAASA,WAAT,KAA4B,SAA5B;;;;;;;;;;;;;AAaA,MAAO,MAAMC,CAAAA,SAAS,CAAG,SAAU;AACjCC,cADiC;AAEjCC,mBAFiC;AAGjCC,IAHiC;AAIjCC,KAJiC,CAAV;AAKtB;AACD,GAAIC,gBAAgB,CAAC,CAAEF,IAAF,CAAQC,KAAR,CAAD,CAApB,CAAuC;AACrC,MAAO,MAAP;AACD;;AAED,KAAME,CAAAA,cAAc,CAAG,CAAC,GAAGL,cAAJ,EAAoBM,MAApB,EAA8BC,UAArD;;AAEA,GAAIF,cAAJ,CAAoB;AAClBJ,mBAAmB,CAACC,IAAD,CAAnB;AACD;;AAED,MAAOG,CAAAA,cAAP;AACD,CAjBM;;;AAoBP,MAAO,MAAMG,CAAAA,kBAAkB,CAAG,SAAUN,IAAV,CAAgB;AAChDJ,WAAW,CAACW,aAAa,CAACP,IAAD,CAAd,CAAsBQ,UAAtB,CAAkCC,UAAlC,CAAX;AACD,CAFM;;;AAKP,KAAMP,CAAAA,gBAAgB,CAAG,SAAU,CAAEF,IAAF,CAAQC,KAAK,CAAG,EAAhB,CAAV,CAAgC;AACvD;AACED,IAAI,GAAK,SAAT,EAAsBC,KAAK,CAACD,IAAN,GAAeQ,UAArC,EAAmDP,KAAK,CAACS,IAAN,GAAeD,UADpE;;AAGD,CAJD;;AAMA,KAAMF,CAAAA,aAAa,CAAG,CAACP,IAAD;AACnB,wBAAuBK,UAAW,KAAIL,IAAK,8BAD9C;AAEA,KAAMQ,CAAAA,UAAU,CAAG,kBAAnB;AACA,KAAMC,CAAAA,UAAU,CAAG,eAAnB;;AAEA,KAAMJ,CAAAA,UAAU,CAAG,GAAnB", "sourcesContent": ["import { emitWarning } from 'process'\n\n// We only allow 100 events (per `event.name`) for the global process because:\n//  - process errors are exceptional and if more than 100 happen, this is\n//    probably due to some infinite recursion.\n//  - the `repeated` logic should prevent reaching the threshold\n//  - `previousEvents` might otherwise take too much memory and/or create a\n//    memory leak.\n//  - it prevents infinite recursions if `opts.log|level()` triggers itself an\n//    event.\n//    The `repeated` logic should prevent it most of the times, but it can still\n//    happen when `[next]Value` is not an `Error` instance and contain dynamic\n//    content.\nexport const isLimited = function ({\n  previousEvents,\n  mEmitLimitedWarning,\n  name,\n  value,\n}) {\n  if (isLimitedWarning({ name, value })) {\n    return false\n  }\n\n  const isLimitedEvent = [...previousEvents].length >= MAX_EVENTS\n\n  if (isLimitedEvent) {\n    mEmitLimitedWarning(name)\n  }\n\n  return isLimitedEvent\n}\n\n// Notify that limit has been reached with a `warning` event\nexport const emitLimitedWarning = function (name) {\n  emitWarning(ERROR_MESSAGE(name), ERROR_NAME, ERROR_CODE)\n}\n\n// The `warning` itself should not be skipped\nconst isLimitedWarning = function ({ name, value = {} }) {\n  return (\n    name === 'warning' && value.name === ERROR_NAME && value.code === ERROR_CODE\n  )\n}\n\nconst ERROR_MESSAGE = (name) =>\n  `Cannot log more than ${MAX_EVENTS} '${name}' until process is restarted`\nconst ERROR_NAME = 'LogProcessErrors'\nconst ERROR_CODE = 'TooManyErrors'\n\nconst MAX_EVENTS = 100\n"], "file": "src/limit.js"}