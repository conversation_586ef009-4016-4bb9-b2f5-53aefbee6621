import{multipleValidOptions}from"jest-validate";

import{RUNNERS}from"./runners.js";


export const applyTesting=function({opts,opts:{level,testing}}){
if(testing===undefined){
return opts;
}

const testOpts=RUNNERS[testing];

validateTesting({testOpts,testing});
validateTestOpts({opts,testOpts,testing});

return{
...opts,
...testOpts,

level:{default:"error",...level,...testOpts.level}};

};

const validateTesting=function({testOpts,testing}){
if(testOpts!==undefined){
return;
}

const runners=Object.keys(RUNNERS).join(", ");
throw new Error(
`Invalid option 'testing' '${testing}': must be one of ${runners}`);

};



const validateTestOpts=function({opts,testOpts,testing}){
const forbiddenOpts=new Set(Object.keys(testOpts).filter(isForbiddenOpt));

const invalidOpt=Object.keys(opts).find((optName)=>
forbiddenOpts.has(optName));


if(invalidOpt===undefined){
return;
}

throw new Error(
`Invalid option '${invalidOpt}': it must not be defined together with the option 'testing' '${testing}'`);

};


const isForbiddenOpt=function(optName){
return!ALLOWED_OPTS.has(optName);
};

const ALLOWED_OPTS=new Set(["level"]);


export const getExampleTesting=function(){
return multipleValidOptions(...Object.keys(RUNNERS));
};
//# sourceMappingURL=testing.js.map