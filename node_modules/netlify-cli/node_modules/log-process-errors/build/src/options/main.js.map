{"version": 3, "sources": ["../../../src/options/main.js"], "names": ["version", "colorsOption", "filterObj", "validate", "semver", "validateExitOn", "applyDefaultLevels", "getExampleLevels", "validateLevels", "defaultLog", "applyTesting", "getExampleTesting", "getOptions", "opts", "optsA", "isDefined", "exampleConfig", "EXAMPLE_OPTS", "validateOptions", "optsB", "level", "colors", "optsC", "DEFAULT_OPTS", "chalk", "key", "value", "undefined", "getDefaultExitOn", "isNewExitBehavior", "gte", "NEW_EXIT_MIN_VERSION", "log", "exitOn", "exampleFunction", "testing"], "mappings": "AAAA,OAASA,OAAT,KAAwB,SAAxB;;AAEA,MAAOC,CAAAA,YAAP,KAAyB,eAAzB;AACA,MAAOC,CAAAA,SAAP,KAAsB,YAAtB;AACA,OAASC,QAAT,KAAyB,eAAzB;AACA,MAAOC,CAAAA,MAAP,KAAmB,QAAnB;;AAEA,OAASC,cAAT,KAA+B,YAA/B;AACA;AACEC,kBADF;AAEEC,gBAFF;AAGEC,cAHF;AAIO,aAJP;AAKA,OAASC,UAAT,KAA2B,WAA3B;;AAEA,OAASC,YAAT,CAAuBC,iBAAvB,KAAgD,cAAhD;;;AAGA,MAAO,MAAMC,CAAAA,UAAU,CAAG,SAAU,CAAEC,IAAI,CAAG,EAAT,CAAV,CAAyB;AACjD,KAAMC,CAAAA,KAAK,CAAGZ,SAAS,CAACW,IAAD,CAAOE,SAAP,CAAvB;;AAEAZ,QAAQ,CAACW,KAAD,CAAQ,CAAEE,aAAa,CAAEC,YAAjB,CAAR,CAAR;AACAC,eAAe,CAACJ,KAAD,CAAf;;AAEA,KAAMK,CAAAA,KAAK,CAAGT,YAAY,CAAC,CAAEG,IAAI,CAAEC,KAAR,CAAD,CAA1B;AACA,KAAMM,CAAAA,KAAK,CAAGd,kBAAkB,CAAC,CAAEO,IAAI,CAAEM,KAAR,CAAD,CAAhC;AACA,KAAM,CAAEE,MAAF,CAAU,GAAGC,KAAb,EAAuB,CAAE,GAAGC,YAAL,CAAmB,GAAGJ,KAAtB,CAA6BC,KAA7B,CAA7B;;AAEA,KAAMI,CAAAA,KAAK,CAAGvB,YAAY,CAAC,CAAEoB,MAAF,CAAD,CAA1B;AACA,MAAO,CAAE,GAAGC,KAAL,CAAYE,KAAZ,CAAP;AACD,CAZM;;AAcP,KAAMT,CAAAA,SAAS,CAAG,SAAUU,GAAV,CAAeC,KAAf,CAAsB;AACtC,MAAOA,CAAAA,KAAK,GAAKC,SAAjB;AACD,CAFD;;;;AAMA,KAAMC,CAAAA,gBAAgB,CAAG,UAAY;AACnC,GAAIC,iBAAiB,EAArB,CAAyB;AACvB,MAAO,CAAC,mBAAD,CAAsB,oBAAtB,CAAP;AACD;;AAED,MAAO,CAAC,mBAAD,CAAP;AACD,CAND;;AAQA,KAAMA,CAAAA,iBAAiB,CAAG,UAAY;AACpC,MAAOzB,CAAAA,MAAM,CAAC0B,GAAP,CAAW9B,OAAX,CAAoB+B,oBAApB,CAAP;AACD,CAFD;;AAIA,KAAMA,CAAAA,oBAAoB,CAAG,QAA7B;;AAEA,KAAMR,CAAAA,YAAY,CAAG;AACnBS,GAAG,CAAEvB,UADc;AAEnBwB,MAAM,CAAEL,gBAAgB,EAFL,CAArB;;;;;AAOA,KAAMM,CAAAA,eAAe,CAAG,UAAY,CAAE,CAAtC;;AAEA,KAAMjB,CAAAA,YAAY,CAAG;AACnB,GAAGM,YADgB;AAEnBF,MAAM,CAAE,IAFW;AAGnBW,GAAG,CAAEE,eAHc;AAInBd,KAAK,CAAEb,gBAAgB,EAJJ;AAKnB4B,OAAO,CAAExB,iBAAiB,EALP,CAArB;;;;AASA,KAAMO,CAAAA,eAAe,CAAG,SAAU,CAAEe,MAAF,CAAUb,KAAK,CAAG,EAAlB,CAAV,CAAkC;AACxDZ,cAAc,CAAC,CAAEY,KAAF,CAAD,CAAd;AACAf,cAAc,CAAC,CAAE4B,MAAF,CAAD,CAAd;AACD,CAHD", "sourcesContent": ["import { version } from 'process'\n\nimport colorsOption from 'colors-option'\nimport filterObj from 'filter-obj'\nimport { validate } from 'jest-validate'\nimport semver from 'semver'\n\nimport { validateExitOn } from '../exit.js'\nimport {\n  applyDefaultLevels,\n  getExampleLevels,\n  validateLevels,\n} from '../level.js'\nimport { defaultLog } from '../log.js'\n\nimport { applyTesting, getExampleTesting } from './testing.js'\n\n// Validate options and assign default options\nexport const getOptions = function ({ opts = {} }) {\n  const optsA = filterObj(opts, isDefined)\n\n  validate(optsA, { exampleConfig: EXAMPLE_OPTS })\n  validateOptions(optsA)\n\n  const optsB = applyTesting({ opts: optsA })\n  const level = applyDefaultLevels({ opts: optsB })\n  const { colors, ...optsC } = { ...DEFAULT_OPTS, ...optsB, level }\n\n  const chalk = colorsOption({ colors })\n  return { ...optsC, chalk }\n}\n\nconst isDefined = function (key, value) {\n  return value !== undefined\n}\n\n// Since Node 15.0.0, `unhandledRejection` makes the process exit too\n// TODO: remove after dropping support for Node <15.0.0\nconst getDefaultExitOn = function () {\n  if (isNewExitBehavior()) {\n    return ['uncaughtException', 'unhandledRejection']\n  }\n\n  return ['uncaughtException']\n}\n\nconst isNewExitBehavior = function () {\n  return semver.gte(version, NEW_EXIT_MIN_VERSION)\n}\n\nconst NEW_EXIT_MIN_VERSION = '15.0.0'\n\nconst DEFAULT_OPTS = {\n  log: defaultLog,\n  exitOn: getDefaultExitOn(),\n}\n\n// `validate-jest` prints the function body\n// eslint-disable-next-line no-empty-function\nconst exampleFunction = function () {}\n\nconst EXAMPLE_OPTS = {\n  ...DEFAULT_OPTS,\n  colors: true,\n  log: exampleFunction,\n  level: getExampleLevels(),\n  testing: getExampleTesting(),\n}\n\n// Validation beyond what `jest-validate` can do\nconst validateOptions = function ({ exitOn, level = {} }) {\n  validateLevels({ level })\n  validateExitOn({ exitOn })\n}\n"], "file": "src/options/main.js"}