{"version": 3, "sources": ["../../../src/options/testing.js"], "names": ["multipleValidOptions", "RUNNERS", "applyTesting", "opts", "level", "testing", "undefined", "testOpts", "validateTesting", "validateTestOpts", "default", "runners", "Object", "keys", "join", "Error", "forbiddenOpts", "Set", "filter", "isForbiddenOpt", "invalidOpt", "find", "optName", "has", "ALLOWED_OPTS", "getExampleTesting"], "mappings": "AAAA,OAASA,oBAAT,KAAqC,eAArC;;AAEA,OAASC,OAAT,KAAwB,cAAxB;;;AAGA,MAAO,MAAMC,CAAAA,YAAY,CAAG,SAAU,CAAEC,IAAF,CAAQA,IAAI,CAAE,CAAEC,KAAF,CAASC,OAAT,CAAd,CAAV,CAA8C;AACxE,GAAIA,OAAO,GAAKC,SAAhB,CAA2B;AACzB,MAAOH,CAAAA,IAAP;AACD;;AAED,KAAMI,CAAAA,QAAQ,CAAGN,OAAO,CAACI,OAAD,CAAxB;;AAEAG,eAAe,CAAC,CAAED,QAAF,CAAYF,OAAZ,CAAD,CAAf;AACAI,gBAAgB,CAAC,CAAEN,IAAF,CAAQI,QAAR,CAAkBF,OAAlB,CAAD,CAAhB;;AAEA,MAAO;AACL,GAAGF,IADE;AAEL,GAAGI,QAFE;;AAILH,KAAK,CAAE,CAAEM,OAAO,CAAE,OAAX,CAAoB,GAAGN,KAAvB,CAA8B,GAAGG,QAAQ,CAACH,KAA1C,CAJF,CAAP;;AAMD,CAhBM;;AAkBP,KAAMI,CAAAA,eAAe,CAAG,SAAU,CAAED,QAAF,CAAYF,OAAZ,CAAV,CAAiC;AACvD,GAAIE,QAAQ,GAAKD,SAAjB,CAA4B;AAC1B;AACD;;AAED,KAAMK,CAAAA,OAAO,CAAGC,MAAM,CAACC,IAAP,CAAYZ,OAAZ,EAAqBa,IAArB,CAA0B,IAA1B,CAAhB;AACA,KAAM,IAAIC,CAAAA,KAAJ;AACH,6BAA4BV,OAAQ,qBAAoBM,OAAQ,EAD7D,CAAN;;AAGD,CATD;;;;AAaA,KAAMF,CAAAA,gBAAgB,CAAG,SAAU,CAAEN,IAAF,CAAQI,QAAR,CAAkBF,OAAlB,CAAV,CAAuC;AAC9D,KAAMW,CAAAA,aAAa,CAAG,GAAIC,CAAAA,GAAJ,CAAQL,MAAM,CAACC,IAAP,CAAYN,QAAZ,EAAsBW,MAAtB,CAA6BC,cAA7B,CAAR,CAAtB;;AAEA,KAAMC,CAAAA,UAAU,CAAGR,MAAM,CAACC,IAAP,CAAYV,IAAZ,EAAkBkB,IAAlB,CAAuB,CAACC,OAAD;AACxCN,aAAa,CAACO,GAAd,CAAkBD,OAAlB,CADiB,CAAnB;;;AAIA,GAAIF,UAAU,GAAKd,SAAnB,CAA8B;AAC5B;AACD;;AAED,KAAM,IAAIS,CAAAA,KAAJ;AACH,mBAAkBK,UAAW,iEAAgEf,OAAQ,GADlG,CAAN;;AAGD,CAdD;;;AAiBA,KAAMc,CAAAA,cAAc,CAAG,SAAUG,OAAV,CAAmB;AACxC,MAAO,CAACE,YAAY,CAACD,GAAb,CAAiBD,OAAjB,CAAR;AACD,CAFD;;AAIA,KAAME,CAAAA,YAAY,CAAG,GAAIP,CAAAA,GAAJ,CAAQ,CAAC,OAAD,CAAR,CAArB;;;AAGA,MAAO,MAAMQ,CAAAA,iBAAiB,CAAG,UAAY;AAC3C,MAAOzB,CAAAA,oBAAoB,CAAC,GAAGY,MAAM,CAACC,IAAP,CAAYZ,OAAZ,CAAJ,CAA3B;AACD,CAFM", "sourcesContent": ["import { multipleValidOptions } from 'jest-validate'\n\nimport { RUNNERS } from './runners.js'\n\n// Apply `options.testing` which is basically a preset of options.\nexport const applyTesting = function ({ opts, opts: { level, testing } }) {\n  if (testing === undefined) {\n    return opts\n  }\n\n  const testOpts = RUNNERS[testing]\n\n  validateTesting({ testOpts, testing })\n  validateTestOpts({ opts, testOpts, testing })\n\n  return {\n    ...opts,\n    ...testOpts,\n    // Users can override `level.default` but not the ones defined in `testOpts`\n    level: { default: 'error', ...level, ...testOpts.level },\n  }\n}\n\nconst validateTesting = function ({ testOpts, testing }) {\n  if (testOpts !== undefined) {\n    return\n  }\n\n  const runners = Object.keys(RUNNERS).join(', ')\n  throw new Error(\n    `Invalid option 'testing' '${testing}': must be one of ${runners}`,\n  )\n}\n\n// Presets override other options. We make sure users do not assume their\n// options are used when they are actually overriden.\nconst validateTestOpts = function ({ opts, testOpts, testing }) {\n  const forbiddenOpts = new Set(Object.keys(testOpts).filter(isForbiddenOpt))\n\n  const invalidOpt = Object.keys(opts).find((optName) =>\n    forbiddenOpts.has(optName),\n  )\n\n  if (invalidOpt === undefined) {\n    return\n  }\n\n  throw new Error(\n    `Invalid option '${invalidOpt}': it must not be defined together with the option 'testing' '${testing}'`,\n  )\n}\n\n// We allow overriding preset's `level` so users can filter events.\nconst isForbiddenOpt = function (optName) {\n  return !ALLOWED_OPTS.has(optName)\n}\n\nconst ALLOWED_OPTS = new Set(['level'])\n\n// Use during options validation\nexport const getExampleTesting = function () {\n  return multipleValidOptions(...Object.keys(RUNNERS))\n}\n"], "file": "src/options/testing.js"}