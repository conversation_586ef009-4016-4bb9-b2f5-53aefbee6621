{"version": 3, "sources": ["../../../src/options/runners.js"], "names": ["nextTick", "throwUncaughtException", "error", "tapeFailingTest", "tape", "test", "message", "t", "plan", "COMMON_OPTIONS", "log", "colors", "exitOn", "level", "uncaughtException", "unhandledRejection", "RUNNERS", "ava", "mocha", "jasmine", "node_tap"], "mappings": "AAAA,OAASA,QAAT,KAAyB,SAAzB;;;;AAIA,KAAMC,CAAAA,sBAAsB,CAAG,SAAUC,KAAV,CAAiB;AAC9CF,QAAQ,CAAC,IAAM;AACb,KAAME,CAAAA,KAAN;AACD,CAFO,CAAR;AAGD,CAJD;;;;AAQA,KAAMC,CAAAA,eAAe,CAAG,eAAgBD,KAAhB,CAAuB;;AAE7C,KAAME,CAAAA,IAAI,CAAG,KAAM,QAAO,MAAP,CAAnB;AACAA,IAAI,CAACC,IAAL,CAAUH,KAAK,CAACI,OAAhB,CAAyB,CAACC,CAAD,GAAO;AAC9BA,CAAC,CAACC,IAAF,CAAO,CAAP;AACAD,CAAC,CAACL,KAAF,CAAQA,KAAR;AACD,CAHD;AAID,CAPD;;;AAUA,KAAMO,CAAAA,cAAc,CAAG;AACrBC,GAAG,CAAET,sBADgB;;AAGrBU,MAAM,CAAE,KAHa;;AAKrBC,MAAM,CAAE,EALa;;;AAQrBC,KAAK,CAAE;AACLC,iBAAiB,CAAE,QADd;AAELC,kBAAkB,CAAE,QAFf,CARc,CAAvB;;;;AAcA,MAAO,MAAMC,CAAAA,OAAO,CAAG;AACrBC,GAAG,CAAER,cADgB;;AAGrBS,KAAK,CAAE,CAAE,GAAGT,cAAL,CAAqBI,KAAK,CAAE,CAAEC,iBAAiB,CAAE,QAArB,CAA5B,CAHc;AAIrBK,OAAO,CAAEV,cAJY;;AAMrBL,IAAI,CAAE,CAAE,GAAGK,cAAL,CAAqBI,KAAK,CAAE,EAA5B,CAAgCH,GAAG,CAAEP,eAArC,CANe;AAOrBiB,QAAQ,CAAEX,cAPW,CAAhB", "sourcesContent": ["import { nextTick } from 'process'\n\n// Make `opts.log()` propagate an `uncaughtException` so that test runner\n// reports the original process error as a test failure.\nconst throwUncaughtException = function (error) {\n  nextTick(() => {\n    throw error\n  })\n}\n\n// `tape` does not handle `uncaughtExceptions`. We create a new failing test\n// to do it instead.\nconst tapeFailingTest = async function (error) {\n  // This is an optional peerDependency. `package.json` does not support those.\n  const tape = await import('tape')\n  tape.test(error.message, (t) => {\n    t.plan(1)\n    t.error(error)\n  })\n}\n\n// Options common to most runners\nconst COMMON_OPTIONS = {\n  log: throwUncaughtException,\n  // Most runners do their own colorization\n  colors: false,\n  // Other tests should keep running\n  exitOn: [],\n  // All runners need to report `uncaughtException` for\n  // `throwUncaughtException()` to work. Most also report `unhandledRejection`.\n  level: {\n    uncaughtException: 'silent',\n    unhandledRejection: 'silent',\n  },\n}\n\nexport const RUNNERS = {\n  ava: COMMON_OPTIONS,\n  // <PERSON><PERSON> does not report `unhandledRejection`\n  mocha: { ...COMMON_OPTIONS, level: { uncaughtException: 'silent' } },\n  jasmine: COMMON_OPTIONS,\n  // Tape does not report `uncaughtException` nor `unhandledRejection`\n  tape: { ...COMMON_OPTIONS, level: {}, log: tapeFailingTest },\n  node_tap: COMMON_OPTIONS,\n}\n"], "file": "src/options/runners.js"}