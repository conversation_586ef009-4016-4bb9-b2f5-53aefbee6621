{"version": 3, "sources": ["../../src/warnings.js"], "names": ["process", "removeWarningListener", "warningListener", "undefined", "off", "restoreWarningListener", "getWarningListeners", "length", "on", "getWarningListener", "listeners"], "mappings": "AAAA,MAAOA,CAAAA,OAAP,KAAoB,SAApB;;;;;;;;AAQA,MAAO,MAAMC,CAAAA,qBAAqB,CAAG,UAAY;AAC/C,GAAIC,eAAe,GAAKC,SAAxB,CAAmC;AACjC;AACD;;;AAGDH,OAAO,CAACI,GAAR,CAAY,SAAZ,CAAuBF,eAAvB;AACD,CAPM;;;AAUP,MAAO,MAAMG,CAAAA,sBAAsB,CAAG,UAAY;AAChD,GAAIH,eAAe,GAAKC,SAAxB,CAAmC;AACjC;AACD;;;;AAID,GAAIG,mBAAmB,GAAGC,MAAtB,GAAiC,CAArC,CAAwC;AACtC;AACD;;AAEDP,OAAO,CAACQ,EAAR,CAAW,SAAX,CAAsBN,eAAtB;AACD,CAZM;;;;;;;;;AAqBP,KAAMO,CAAAA,kBAAkB,CAAG,UAAY;AACrC,MAAOH,CAAAA,mBAAmB,GAAG,CAAH,CAA1B;AACD,CAFD;;AAIA,KAAMA,CAAAA,mBAAmB,CAAG,UAAY;AACtC,MAAON,CAAAA,OAAO,CAACU,SAAR,CAAkB,SAAlB,CAAP;AACD,CAFD;;AAIA,KAAMR,CAAAA,eAAe,CAAGO,kBAAkB,EAA1C", "sourcesContent": ["import process from 'process'\n\n// By default Node.js adds a `warning` listener that prints `warning` events\n// on the console. This leads to duplicated events printing with this module.\n// So we remove it.\n// Alternative ways to do it would be to ask users to pass `--no-warnings`\n// CLI flag or `NODE_NO_WARNINGS=1` environment variable. But this is not as\n// developer-friendly.\nexport const removeWarningListener = function () {\n  if (warningListener === undefined) {\n    return\n  }\n\n  // This will be a noop if `init()` is called several times\n  process.off('warning', warningListener)\n}\n\n// When this module is undone, Node.js default `warning` listener is restored\nexport const restoreWarningListener = function () {\n  if (warningListener === undefined) {\n    return\n  }\n\n  // Do not restore if there is some user-defined listener, including if\n  // `init()` was called several times.\n  if (getWarningListeners().length !== 0) {\n    return\n  }\n\n  process.on('warning', warningListener)\n}\n\n// We assume the first `warning` listener is the Node.js default one.\n// Checking the function itself makes it rely on internal Node.js code, which\n// is brittle.\n// This can return `undefined` if `--no-warnings` was used\n// This needs be done at load time to ensure:\n//  - we are not catching user-defined listeners\n//  - this is idempotent, allowing this module to be called several times\nconst getWarningListener = function () {\n  return getWarningListeners()[0]\n}\n\nconst getWarningListeners = function () {\n  return process.listeners('warning')\n}\n\nconst warningListener = getWarningListener()\n"], "file": "src/warnings.js"}