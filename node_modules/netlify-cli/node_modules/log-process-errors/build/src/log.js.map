{"version": 3, "sources": ["../../src/log.js"], "names": ["defaultLog", "error", "level", "console"], "mappings": ";AACA,MAAO,MAAMA,CAAAA,UAAU,CAAG,SAAUC,KAAV,CAAiBC,KAAjB,CAAwB;;;;;;AAMhDC,OAAO,CAACD,KAAD,CAAP,CAAeD,KAAf;AACD,CAPM", "sourcesContent": ["// Default `opts.log`\nexport const defaultLog = function (error, level) {\n  // Note that `console` should be referenced inside this function, not outside,\n  // as user might monkey patch it.\n  // We defined `error[util.custom.inspect]`, so `error` will use it for pretty\n  // printing.\n  // eslint-disable-next-line no-restricted-globals, no-console\n  console[level](error)\n}\n"], "file": "src/log.js"}