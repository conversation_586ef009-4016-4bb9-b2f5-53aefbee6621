import process from"process";







export const removeWarningListener=function(){
if(warningListener===undefined){
return;
}


process.off("warning",warningListener);
};


export const restoreWarningListener=function(){
if(warningListener===undefined){
return;
}



if(getWarningListeners().length!==0){
return;
}

process.on("warning",warningListener);
};








const getWarningListener=function(){
return getWarningListeners()[0];
};

const getWarningListeners=function(){
return process.listeners("warning");
};

const warningListener=getWarningListener();
//# sourceMappingURL=warnings.js.map