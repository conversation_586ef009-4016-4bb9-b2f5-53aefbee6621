{"version": 3, "sources": ["../../../src/error/print.js"], "names": ["figures", "printError", "opts", "chalk", "gray", "level", "name", "message", "stack", "header", "<PERSON><PERSON><PERSON><PERSON>", "stackA", "italic", "inverse", "bold", "messageA", "details", "splitMessage", "COLOR", "SIGN", "LEVELS", "prefix", "headerA", "split", "detailsA", "join", "debug", "circleFilled", "info", "warn", "warning", "error", "cross"], "mappings": "AAAA,MAAOA,CAAAA,OAAP,KAAoB,SAApB;;;AAGA,MAAO,MAAMC,CAAAA,UAAU,CAAG,SAAU;AAClCC,IAAI,CAAE;AACJC,KADI;AAEJA,KAAK,CAAE,CAAEC,IAAF,CAFH,CAD4B;;AAKlCC,KALkC;AAMlCC,IANkC;AAOlCC,OAPkC;AAQlCC,KARkC,CAAV;AASvB;AACD,KAAMC,CAAAA,MAAM,CAAGC,SAAS,CAAC,CAAEL,KAAF,CAASC,IAAT,CAAeC,OAAf,CAAwBJ,KAAxB,CAAD,CAAxB;AACA,KAAMQ,CAAAA,MAAM,CAAGP,IAAI,CAACI,KAAD,CAAnB;AACA,MAAQ,GAAEC,MAAO,KAAIE,MAAO,EAA5B;AACD,CAbM;;;AAgBP,KAAMD,CAAAA,SAAS,CAAG,SAAU;AAC1BL,KAD0B;AAE1BC,IAF0B;AAG1BC,OAH0B;AAI1BJ,KAJ0B;AAK1BA,KAAK,CAAE,CAAES,MAAF,CAAUC,OAAV,CAAmBC,IAAnB,CALmB,CAAV;AAMf;AACD,KAAM,CAAEP,OAAO,CAAEQ,QAAX,CAAqBC,OAArB,EAAiCC,YAAY,CAAC,CAAEV,OAAF,CAAD,CAAnD;;AAEA,KAAM,CAAEW,KAAF,CAASC,IAAT,EAAkBC,MAAM,CAACf,KAAD,CAA9B;AACA,KAAMgB,CAAAA,MAAM,CAAI,IAAGF,IAAK,KAAIb,IAAK,IAAGM,MAAM,CAAE,IAAGG,QAAS,GAAd,CAAkB,GAA5D;AACA,KAAMN,CAAAA,MAAM,CAAI,GAAEI,OAAO,CAACC,IAAI,CAACO,MAAD,CAAL,CAAe,GAAEL,OAAQ,EAAlD;AACA,KAAMM,CAAAA,OAAO,CAAGnB,KAAK,CAACe,KAAD,CAAL,CAAaT,MAAb,CAAhB;AACA,MAAOa,CAAAA,OAAP;AACD,CAdD;;AAgBA,KAAML,CAAAA,YAAY,CAAG,SAAU,CAAEV,OAAF,CAAV,CAAuB;AAC1C,KAAM,CAACQ,QAAD,CAAW,GAAGC,OAAd,EAAyBT,OAAO,CAACgB,KAAR,CAAc,GAAd,CAA/B;AACA,KAAMC,CAAAA,QAAQ,CAAGR,OAAO,CAACS,IAAR,CAAa,GAAb,CAAjB;AACA,MAAO,CAAElB,OAAO,CAAEQ,QAAX,CAAqBC,OAAO,CAAEQ,QAA9B,CAAP;AACD,CAJD;;;AAOA,KAAMJ,CAAAA,MAAM,CAAG;AACbM,KAAK,CAAE,CAAER,KAAK,CAAE,MAAT,CAAiBC,IAAI,CAAEnB,OAAO,CAAC2B,YAA/B,CADM;AAEbC,IAAI,CAAE,CAAEV,KAAK,CAAE,OAAT,CAAkBC,IAAI,CAAEnB,OAAO,CAAC4B,IAAhC,CAFO;AAGbC,IAAI,CAAE,CAAEX,KAAK,CAAE,QAAT,CAAmBC,IAAI,CAAEnB,OAAO,CAAC8B,OAAjC,CAHO;AAIbC,KAAK,CAAE,CAAEb,KAAK,CAAE,KAAT,CAAgBC,IAAI,CAAEnB,OAAO,CAACgC,KAA9B,CAJM,CAAf", "sourcesContent": ["import figures from 'figures'\n\n// Pretty-print error on the console (which uses `util.inspect()`)\nexport const printError = function ({\n  opts: {\n    chalk,\n    chalk: { gray },\n  },\n  level,\n  name,\n  message,\n  stack,\n}) {\n  const header = getHeader({ level, name, message, chalk })\n  const stackA = gray(stack)\n  return `${header}\\n${stackA}`\n}\n\n// Add color, sign and `event.name` to first message line\nconst getHeader = function ({\n  level,\n  name,\n  message,\n  chalk,\n  chalk: { italic, inverse, bold },\n}) {\n  const { message: messageA, details } = splitMessage({ message })\n\n  const { COLOR, SIGN } = LEVELS[level]\n  const prefix = ` ${SIGN}  ${name} ${italic(`(${messageA})`)} `\n  const header = `${inverse(bold(prefix))}${details}`\n  const headerA = chalk[COLOR](header)\n  return headerA\n}\n\nconst splitMessage = function ({ message }) {\n  const [messageA, ...details] = message.split(':')\n  const detailsA = details.join(':')\n  return { message: messageA, details: detailsA }\n}\n\n// Each level is printed in a different way\nconst LEVELS = {\n  debug: { COLOR: 'blue', SIGN: figures.circleFilled },\n  info: { COLOR: 'green', SIGN: figures.info },\n  warn: { COLOR: 'yellow', SIGN: figures.warning },\n  error: { COLOR: 'red', SIGN: figures.cross },\n}\n"], "file": "src/error/print.js"}