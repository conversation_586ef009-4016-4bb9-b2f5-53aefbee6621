{"version": 3, "sources": ["../../../src/error/stack.js"], "names": ["getStack", "mainValue", "Error", "stack", "replace", "FIRST_LINE_REGEXP"], "mappings": ";;;AAGA,MAAO,MAAMA,CAAAA,QAAQ,CAAG,SAAUC,SAAV,CAAqB;AAC3C,GAAI,EAAEA,SAAS,WAAYC,CAAAA,KAAvB,CAAJ,CAAmC;AACjC,MAAO,EAAP;AACD;;AAED,MAAOD,CAAAA,SAAS,CAACE,KAAV,CAAgBC,OAAhB,CAAwBC,iBAAxB,CAA2C,EAA3C,CAAP;AACD,CANM;;AAQP,KAAMA,CAAAA,iBAAiB,CAAG,OAA1B", "sourcesContent": ["// Retrieve `error.stack` by re-using the original error's stack trace\n// Remove first line of `Error.stack` as it contains `Error.name|message`,\n// which is already present in the upper error's `message`\nexport const getStack = function (mainValue) {\n  if (!(mainValue instanceof Error)) {\n    return ''\n  }\n\n  return mainValue.stack.replace(FIRST_LINE_REGEXP, '')\n}\n\nconst FIRST_LINE_REGEXP = /.*\\n/u\n"], "file": "src/error/stack.js"}