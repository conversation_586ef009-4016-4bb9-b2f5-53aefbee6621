{"version": 3, "sources": ["../../../src/error/message.js"], "names": ["inspect", "getMessage", "event", "name", "MESSAGES", "uncaughtException", "value", "serialize", "warning", "code", "detail", "getWarningDetails", "undefined", "unhandledRejection", "rejectionHandled", "Error", "String", "INSPECT_OPTS", "getters"], "mappings": "AAAA,OAASA,OAAT,KAAwB,MAAxB;;;AAGA,MAAO,MAAMC,CAAAA,UAAU,CAAG,SAAU,CAAEC,KAAF,CAASC,IAAT,CAAV,CAA2B;AACnD,MAAOC,CAAAA,QAAQ,CAACD,IAAD,CAAR,CAAeD,KAAf,CAAP;AACD,CAFM;;AAIP,KAAMG,CAAAA,iBAAiB,CAAG,SAAU,CAAEC,KAAF,CAAV,CAAqB;AAC7C,MAAQ,2CAA0CC,SAAS,CAACD,KAAD,CAAQ,EAAnE;AACD,CAFD;;AAIA,KAAME,CAAAA,OAAO,CAAG,SAAU,CAAEF,KAAF,CAASA,KAAK,CAAE,CAAEG,IAAF,CAAQC,MAAR,CAAhB,CAAV,CAA8C;AAC5D,MAAQ,GAAEH,SAAS,CAACD,KAAD,CAAQ,GAAEK,iBAAiB,CAAC,CAAEF,IAAF,CAAQC,MAAR,CAAD,CAAmB,EAAjE;AACD,CAFD;;AAIA,KAAMC,CAAAA,iBAAiB,CAAG,SAAU,CAAEF,IAAF,CAAQC,MAAM,CAAG,EAAjB,CAAV,CAAiC;AACzD,GAAID,IAAI,GAAKG,SAAb,CAAwB;AACtB,MAAQ,MAAKH,IAAK,KAAIC,MAAO,EAA7B;AACD;;AAED,GAAIA,MAAM,GAAK,EAAf,CAAmB;AACjB,MAAQ,KAAIA,MAAO,EAAnB;AACD;;AAED,MAAO,EAAP;AACD,CAVD;;AAYA,KAAMG,CAAAA,kBAAkB,CAAG,SAAU,CAAEP,KAAF,CAAV,CAAqB;AAC9C,MAAQ,2CAA0CC,SAAS,CAACD,KAAD,CAAQ,EAAnE;AACD,CAFD;;AAIA,KAAMQ,CAAAA,gBAAgB,CAAG,SAAU,CAAER,KAAF,CAAV,CAAqB;AAC5C,MAAQ,gDAA+CC,SAAS,CAACD,KAAD,CAAQ,EAAxE;AACD,CAFD;;AAIA,KAAMF,CAAAA,QAAQ,CAAG;AACfC,iBADe;AAEfG,OAFe;AAGfK,kBAHe;AAIfC,gBAJe,CAAjB;;;;;AASA,KAAMP,CAAAA,SAAS,CAAG,SAAUD,KAAV,CAAiB;;AAEjC,GAAIA,KAAK,WAAYS,CAAAA,KAArB,CAA4B;AAC1B,MAAOC,CAAAA,MAAM,CAACV,KAAD,CAAb;AACD;;AAED,MAAON,CAAAA,OAAO,CAACM,KAAD,CAAQW,YAAR,CAAd;AACD,CAPD;;AASA,KAAMA,CAAAA,YAAY,CAAG,CAAEC,OAAO,CAAE,IAAX,CAArB", "sourcesContent": ["import { inspect } from 'util'\n\n// Retrieve the `error.message` using the `event` information\nexport const getMessage = function ({ event, name }) {\n  return MESSAGES[name](event)\n}\n\nconst uncaughtException = function ({ value }) {\n  return `an exception was thrown but not caught: ${serialize(value)}`\n}\n\nconst warning = function ({ value, value: { code, detail } }) {\n  return `${serialize(value)}${getWarningDetails({ code, detail })}`\n}\n\nconst getWarningDetails = function ({ code, detail = '' }) {\n  if (code !== undefined) {\n    return `\\n[${code}] ${detail}`\n  }\n\n  if (detail !== '') {\n    return `\\n${detail}`\n  }\n\n  return ''\n}\n\nconst unhandledRejection = function ({ value }) {\n  return `a promise was rejected but not handled: ${serialize(value)}`\n}\n\nconst rejectionHandled = function ({ value }) {\n  return `a promise was rejected and handled too late: ${serialize(value)}`\n}\n\nconst MESSAGES = {\n  uncaughtException,\n  warning,\n  unhandledRejection,\n  rejectionHandled,\n}\n\n// We use `util.inspect()` instead of `JSON.stringify()` or a third-party\n// library because it has nice output.\nconst serialize = function (value) {\n  // Do not print `Error.stack`, but print `Error.name` + `Error.message`\n  if (value instanceof Error) {\n    return String(value)\n  }\n\n  return inspect(value, INSPECT_OPTS)\n}\n\nconst INSPECT_OPTS = { getters: true }\n"], "file": "src/error/message.js"}