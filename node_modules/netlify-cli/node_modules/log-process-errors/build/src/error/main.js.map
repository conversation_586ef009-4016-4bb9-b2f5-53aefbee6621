{"version": 3, "sources": ["../../../src/error/main.js"], "names": ["inspect", "getMessage", "printError", "getStack", "custom", "getError", "name", "event", "message", "mainValue", "getMainValue", "staticProps", "getEventProps", "stackA", "error", "buildError", "stack", "value", "nextValue", "Error", "Object", "assign", "defineProperty", "capitalize", "enumerable", "writable", "configurable", "string", "firstLetter", "rest", "toUpperCase", "join", "addErrorPrint", "opts", "level", "bind", "undefined"], "mappings": "AAAA,OAASA,OAAT,KAAwB,MAAxB;;AAEA,OAASC,UAAT,KAA2B,cAA3B;AACA,OAASC,UAAT,KAA2B,YAA3B;AACA,OAASC,QAAT,KAAyB,YAAzB;;AAEA,KAAM,CAAEC,MAAF,EAAaJ,OAAnB;;;;AAIA,MAAO,MAAMK,CAAAA,QAAQ,CAAG,SAAU,CAAEC,IAAF,CAAQC,KAAR,CAAV,CAA2B;AACjD,KAAMC,CAAAA,OAAO,CAAGP,UAAU,CAAC,CAAEM,KAAF,CAASD,IAAT,CAAD,CAA1B;AACA,KAAMG,CAAAA,SAAS,CAAGC,YAAY,CAACH,KAAD,CAA9B;AACA,KAAMI,CAAAA,WAAW,CAAGC,aAAa,CAACH,SAAD,CAAjC;AACA,KAAMI,CAAAA,MAAM,CAAGV,QAAQ,CAACM,SAAD,CAAvB;AACA,KAAMK,CAAAA,KAAK,CAAGC,UAAU,CAAC,CAAET,IAAF,CAAQE,OAAR,CAAiBQ,KAAK,CAAEH,MAAxB,CAAgCF,WAAhC,CAAD,CAAxB;AACA,MAAO,CAAEG,KAAF,CAASE,KAAK,CAAEH,MAAhB,CAAwBJ,SAAxB,CAAP;AACD,CAPM;;;AAUP,KAAMC,CAAAA,YAAY,CAAG,SAAU,CAAEO,KAAF,CAASC,SAAS,CAAET,SAAS,CAAGQ,KAAhC,CAAV,CAAmD;AACtE,MAAOR,CAAAA,SAAP;AACD,CAFD;;;AAKA,KAAMG,CAAAA,aAAa,CAAG,SAAUH,SAAV,CAAqB;AACzC,GAAIA,SAAS,WAAYU,CAAAA,KAAzB,CAAgC;AAC9B,MAAO,CAAE,GAAGV,SAAL,CAAP;AACD;;AAED,MAAO,EAAP;AACD,CAND;;AAQA,KAAMM,CAAAA,UAAU,CAAG,SAAU,CAAET,IAAF,CAAQE,OAAR,CAAiBQ,KAAjB,CAAwBL,WAAxB,CAAV,CAAiD;AAClE,KAAMG,CAAAA,KAAK,CAAG,GAAIK,CAAAA,KAAJ,CAAUX,OAAV,CAAd;;AAEAY,MAAM,CAACC,MAAP,CAAcP,KAAd,CAAqBH,WAArB;;;AAGAS,MAAM,CAACE,cAAP,CAAsBR,KAAtB,CAA6B,MAA7B,CAAqC;AACnCG,KAAK,CAAEM,UAAU,CAACjB,IAAD,CADkB;AAEnCkB,UAAU,CAAE,KAFuB;AAGnCC,QAAQ,CAAE,IAHyB;AAInCC,YAAY,CAAE,IAJqB,CAArC;;;AAOAZ,KAAK,CAACE,KAAN,CAAe,GAAEF,KAAM,KAAIE,KAAM,EAAjC;AACA,MAAOF,CAAAA,KAAP;AACD,CAfD;;AAiBA,KAAMS,CAAAA,UAAU,CAAG,SAAUI,MAAV,CAAkB;AACnC,KAAM,CAACC,WAAD,CAAc,GAAGC,IAAjB,EAAyBF,MAA/B;AACA,MAAO,CAACC,WAAW,CAACE,WAAZ,EAAD,CAA4B,GAAGD,IAA/B,EAAqCE,IAArC,CAA0C,EAA1C,CAAP;AACD,CAHD;;;AAMA,MAAO,MAAMC,CAAAA,aAAa,CAAG,SAAU;AACrClB,KADqC;AAErCA,KAAK,CAAE,CAAEN,OAAF,CAF8B;AAGrCyB,IAHqC;AAIrCC,KAJqC;AAKrC5B,IALqC;AAMrCU,KANqC,CAAV;AAO1B;AACDF,KAAK,CAACV,MAAD,CAAL,CAAgBF,UAAU,CAACiC,IAAX,CAAgBC,SAAhB,CAA2B;AACzCH,IADyC;AAEzCC,KAFyC;AAGzC5B,IAHyC;AAIzCE,OAJyC;AAKzCQ,KALyC,CAA3B,CAAhB;;AAOD,CAfM", "sourcesContent": ["import { inspect } from 'util'\n\nimport { getMessage } from './message.js'\nimport { printError } from './print.js'\nimport { getStack } from './stack.js'\n\nconst { custom } = inspect\n\n// Retrieve `error` which sums up all information that can be gathered about\n// the event.\nexport const getError = function ({ name, event }) {\n  const message = getMessage({ event, name })\n  const mainValue = getMainValue(event)\n  const staticProps = getEventProps(mainValue)\n  const stackA = getStack(mainValue)\n  const error = buildError({ name, message, stack: stackA, staticProps })\n  return { error, stack: stackA, mainValue }\n}\n\n// Retrieve main thrown value, which is most likely an `Error` instance\nconst getMainValue = function ({ value, nextValue: mainValue = value }) {\n  return mainValue\n}\n\n// If event is an error, retrieve static properties except `name` and `message`\nconst getEventProps = function (mainValue) {\n  if (mainValue instanceof Error) {\n    return { ...mainValue }\n  }\n\n  return {}\n}\n\nconst buildError = function ({ name, message, stack, staticProps }) {\n  const error = new Error(message)\n  // eslint-disable-next-line fp/no-mutating-assign\n  Object.assign(error, staticProps)\n  // `error.name` should not be enumerable, to ensure it is correctly printed.\n  // eslint-disable-next-line fp/no-mutating-methods\n  Object.defineProperty(error, 'name', {\n    value: capitalize(name),\n    enumerable: false,\n    writable: true,\n    configurable: true,\n  })\n  // We removed the first line of `stack`, now we substitute it\n  error.stack = `${error}\\n${stack}`\n  return error\n}\n\nconst capitalize = function (string) {\n  const [firstLetter, ...rest] = string\n  return [firstLetter.toUpperCase(), ...rest].join('')\n}\n\n// This needs to be done later because `error` is used by `level`\nexport const addErrorPrint = function ({\n  error,\n  error: { message },\n  opts,\n  level,\n  name,\n  stack,\n}) {\n  error[custom] = printError.bind(undefined, {\n    opts,\n    level,\n    name,\n    message,\n    stack,\n  })\n}\n"], "file": "src/error/main.js"}