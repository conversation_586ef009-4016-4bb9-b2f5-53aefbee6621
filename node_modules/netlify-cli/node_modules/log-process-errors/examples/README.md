This directory contains examples of this library.

To execute them, first [install](../README.md#install) `log-process-errors`.
Then run:

```
node node_modules/log-process-errors/examples/FILE.js
```

You can edit the examples.

They can also be run directly
[in your browser](https://repl.it/@ehmicky/log-process-errors).

## Examples

- How process errors look by default [without](before.js) and
  [with `log-process-errors`](after.js)
- [Restoring Node.js default behavior](restore.js)
- [`log` option](log.js)
- [`level` option](level.js)
- [`exitOn` option](exit.js)
- [`colors` option](colors.js)
