{"name": "get-amd-module-type", "version": "5.0.1", "description": "Get the type of an AMD module used for an AST node or within a file", "main": "index.js", "files": ["index.js"], "scripts": {"lint": "xo", "fix": "xo --fix", "mocha": "mocha", "test": "npm run lint && npm run mocha", "test:ci": "c8 npm run mocha"}, "repository": {"type": "git", "url": "git+https://github.com/dependents/node-get-amd-module-type.git"}, "keywords": ["module", "type", "amd", "factory", "form"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dependents/node-get-amd-module-type/issues"}, "homepage": "https://github.com/dependents/node-get-amd-module-type", "engines": {"node": ">=14"}, "dependencies": {"ast-module-types": "^5.0.0", "node-source-walk": "^6.0.1"}, "devDependencies": {"c8": "^7.13.0", "mocha": "^10.2.0", "xo": "^0.54.2"}, "xo": {"space": true, "ignores": ["test/fixtures/*"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "curly": ["error", "multi-line"], "max-nested-callbacks": ["error", 5], "operator-linebreak": ["error", "after"], "object-curly-spacing": ["error", "always"], "prefer-template": "error", "space-before-function-paren": ["error", "never"], "unicorn/explicit-length-check": "off", "unicorn/prefer-module": "off", "unicorn/prefer-node-protocol": "off", "unicorn/prefer-top-level-await": "off", "unicorn/prevent-abbreviations": "off"}, "overrides": [{"files": "test/**", "envs": ["mocha"]}]}}