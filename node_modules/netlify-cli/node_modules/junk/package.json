{"name": "junk", "version": "4.0.1", "description": "Filter out system junk files like .DS_Store and Thumbs.db", "license": "MIT", "repository": "sindresorhus/junk", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12.20"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["junk", "trash", "garbage", "files", "os", "ignore", "exclude", "filter", "temp", "tmp", "system", "clean", "cleanup"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.42.0"}}