var i=Object.defineProperty;var e=(r,t)=>i(r,"name",{value:t,configurable:!0});import{fetch as l,Blob as a,File as m,FormData as p,Headers as s,Request as n,Response as b,AbortController as f}from"./node.mjs";import"node:fs";import"node:path";import"node:http";import"node:https";import"node:zlib";import"node:stream";import"node:buffer";import"node:util";import"./shared/node-fetch-native.1a4a356d.mjs";import"node:url";import"node:net";var c=Object.defineProperty,h=e((r,t)=>c(r,"name",{value:t,configurable:!0}),"a");function o(r,t){if(!(r in globalThis))try{globalThis[r]=t}catch{}}e(o,"e"),h(o,"polyfill"),o("fetch",l),o("Blob",a),o("File",m),o("FormData",p),o("Headers",s),o("Request",n),o("Response",b),o("AbortController",f);
