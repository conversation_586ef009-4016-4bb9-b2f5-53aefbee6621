var Ps=Object.defineProperty;var n=(i,o)=>Ps(i,"name",{value:o,configurable:!0});var ui=(i,o,a)=>{if(!o.has(i))throw TypeError("Cannot "+a)};var O=(i,o,a)=>(ui(i,o,"read from private field"),a?a.call(i):o.get(i)),be=(i,o,a)=>{if(o.has(i))throw TypeError("Cannot add the same private member more than once");o instanceof WeakSet?o.add(i):o.set(i,a)},X=(i,o,a,u)=>(ui(i,o,"write to private field"),u?u.call(i,a):o.set(i,a),a);var Pe,Wt,bt,Cr,Ve,qt,Ot,zt,ee,It,Ne,He,Ft;import Et from"node:http";import vs from"node:https";import st from"node:zlib";import me,{PassThrough as cr,pipeline as ut}from"node:stream";import{Buffer as x}from"node:buffer";import{types as dr,promisify as Es,deprecate as hr}from"node:util";import{c as En,g as As}from"./shared/node-fetch-native.1a4a356d.mjs";import{format as Bs}from"node:url";import{isIP as ks}from"node:net";import{statSync as li,createReadStream as Ws,promises as qs}from"node:fs";import{basename as Os}from"node:path";function zs(i){if(!/^data:/i.test(i))throw new TypeError('`uri` does not appear to be a Data URI (must begin with "data:")');i=i.replace(/\r?\n/g,"");const o=i.indexOf(",");if(o===-1||o<=4)throw new TypeError("malformed data: URI");const a=i.substring(5,o).split(";");let u="",f=!1;const d=a[0]||"text/plain";let b=d;for(let D=1;D<a.length;D++)a[D]==="base64"?f=!0:a[D]&&(b+=`;${a[D]}`,a[D].indexOf("charset=")===0&&(u=a[D].substring(8)));!a[0]&&!u.length&&(b+=";charset=US-ASCII",u="US-ASCII");const p=f?"base64":"ascii",E=unescape(i.substring(o+1)),w=Buffer.from(E,p);return w.type=d,w.typeFull=b,w.charset=u,w}n(zs,"dataUriToBuffer");var pr={exports:{}};/**
 * @license
 * web-streams-polyfill v3.3.2
 * Copyright 2024 Mattias Buelens, Diwank Singh Tomer and other contributors.
 * This code is released under the MIT license.
 * SPDX-License-Identifier: MIT
 */var fi;function Is(){return fi||(fi=1,function(i,o){(function(a,u){u(o)})(En,function(a){const u=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol:e=>`Symbol(${e})`;function f(){}n(f,"noop");function d(e){return typeof e=="object"&&e!==null||typeof e=="function"}n(d,"typeIsObject");const b=f;function p(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch{}}n(p,"setFunctionName");const E=Promise,w=Promise.prototype.then,D=Promise.reject.bind(E);function A(e){return new E(e)}n(A,"newPromise");function S(e){return A(t=>t(e))}n(S,"promiseResolvedWith");function m(e){return D(e)}n(m,"promiseRejectedWith");function R(e,t,r){return w.call(e,t,r)}n(R,"PerformPromiseThen");function q(e,t,r){R(R(e,t,r),void 0,b)}n(q,"uponPromise");function F(e,t){q(e,t)}n(F,"uponFulfillment");function Q(e,t){q(e,void 0,t)}n(Q,"uponRejection");function M(e,t,r){return R(e,t,r)}n(M,"transformPromiseWith");function ve(e){R(e,void 0,b)}n(ve,"setPromiseIsHandledToTrue");let z=n(e=>{if(typeof queueMicrotask=="function")z=queueMicrotask;else{const t=S(void 0);z=n(r=>R(t,r),"_queueMicrotask")}return z(e)},"_queueMicrotask");function j(e,t,r){if(typeof e!="function")throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}n(j,"reflectCall");function I(e,t,r){try{return S(j(e,t,r))}catch(s){return m(s)}}n(I,"promiseCall");const mt=16384,cn=class cn{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(t){const r=this._back;let s=r;r._elements.length===mt-1&&(s={_elements:[],_next:void 0}),r._elements.push(t),s!==r&&(this._back=s,r._next=s),++this._size}shift(){const t=this._front;let r=t;const s=this._cursor;let l=s+1;const c=t._elements,h=c[s];return l===mt&&(r=t._next,l=0),--this._size,this._cursor=l,t!==r&&(this._front=r),c[s]=void 0,h}forEach(t){let r=this._cursor,s=this._front,l=s._elements;for(;(r!==l.length||s._next!==void 0)&&!(r===l.length&&(s=s._next,l=s._elements,r=0,l.length===0));)t(l[r]),++r}peek(){const t=this._front,r=this._cursor;return t._elements[r]}};n(cn,"SimpleQueue");let U=cn;const xn=u("[[AbortSteps]]"),Nn=u("[[ErrorSteps]]"),Ar=u("[[CancelSteps]]"),Br=u("[[PullSteps]]"),kr=u("[[ReleaseSteps]]");function Hn(e,t){e._ownerReadableStream=t,t._reader=e,t._state==="readable"?qr(e):t._state==="closed"?Fi(e):Vn(e,t._storedError)}n(Hn,"ReadableStreamReaderGenericInitialize");function Wr(e,t){const r=e._ownerReadableStream;return ie(r,t)}n(Wr,"ReadableStreamReaderGenericCancel");function ge(e){const t=e._ownerReadableStream;t._state==="readable"?Or(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):ji(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),t._readableStreamController[kr](),t._reader=void 0,e._ownerReadableStream=void 0}n(ge,"ReadableStreamReaderGenericRelease");function jt(e){return new TypeError("Cannot "+e+" a stream using a released reader")}n(jt,"readerLockException");function qr(e){e._closedPromise=A((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r})}n(qr,"defaultReaderClosedPromiseInitialize");function Vn(e,t){qr(e),Or(e,t)}n(Vn,"defaultReaderClosedPromiseInitializeAsRejected");function Fi(e){qr(e),Qn(e)}n(Fi,"defaultReaderClosedPromiseInitializeAsResolved");function Or(e,t){e._closedPromise_reject!==void 0&&(ve(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}n(Or,"defaultReaderClosedPromiseReject");function ji(e,t){Vn(e,t)}n(ji,"defaultReaderClosedPromiseResetToRejected");function Qn(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}n(Qn,"defaultReaderClosedPromiseResolve");const Yn=Number.isFinite||function(e){return typeof e=="number"&&isFinite(e)},Li=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function $i(e){return typeof e=="object"||typeof e=="function"}n($i,"isDictionary");function le(e,t){if(e!==void 0&&!$i(e))throw new TypeError(`${t} is not an object.`)}n(le,"assertDictionary");function Z(e,t){if(typeof e!="function")throw new TypeError(`${t} is not a function.`)}n(Z,"assertFunction");function Di(e){return typeof e=="object"&&e!==null||typeof e=="function"}n(Di,"isObject");function Gn(e,t){if(!Di(e))throw new TypeError(`${t} is not an object.`)}n(Gn,"assertObject");function _e(e,t,r){if(e===void 0)throw new TypeError(`Parameter ${t} is required in '${r}'.`)}n(_e,"assertRequiredArgument");function zr(e,t,r){if(e===void 0)throw new TypeError(`${t} is required in '${r}'.`)}n(zr,"assertRequiredField");function Ir(e){return Number(e)}n(Ir,"convertUnrestrictedDouble");function Zn(e){return e===0?0:e}n(Zn,"censorNegativeZero");function Mi(e){return Zn(Li(e))}n(Mi,"integerPart");function Fr(e,t){const s=Number.MAX_SAFE_INTEGER;let l=Number(e);if(l=Zn(l),!Yn(l))throw new TypeError(`${t} is not a finite number`);if(l=Mi(l),l<0||l>s)throw new TypeError(`${t} is outside the accepted range of 0 to ${s}, inclusive`);return!Yn(l)||l===0?0:l}n(Fr,"convertUnsignedLongLongWithEnforceRange");function jr(e,t){if(!We(e))throw new TypeError(`${t} is not a ReadableStream.`)}n(jr,"assertReadableStream");function Qe(e){return new fe(e)}n(Qe,"AcquireReadableStreamDefaultReader");function Kn(e,t){e._reader._readRequests.push(t)}n(Kn,"ReadableStreamAddReadRequest");function Lr(e,t,r){const l=e._reader._readRequests.shift();r?l._closeSteps():l._chunkSteps(t)}n(Lr,"ReadableStreamFulfillReadRequest");function Lt(e){return e._reader._readRequests.length}n(Lt,"ReadableStreamGetNumReadRequests");function Jn(e){const t=e._reader;return!(t===void 0||!Ee(t))}n(Jn,"ReadableStreamHasDefaultReader");const dn=class dn{constructor(t){if(_e(t,1,"ReadableStreamDefaultReader"),jr(t,"First parameter"),qe(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");Hn(this,t),this._readRequests=new U}get closed(){return Ee(this)?this._closedPromise:m($t("closed"))}cancel(t=void 0){return Ee(this)?this._ownerReadableStream===void 0?m(jt("cancel")):Wr(this,t):m($t("cancel"))}read(){if(!Ee(this))return m($t("read"));if(this._ownerReadableStream===void 0)return m(jt("read from"));let t,r;const s=A((c,h)=>{t=c,r=h});return yt(this,{_chunkSteps:c=>t({value:c,done:!1}),_closeSteps:()=>t({value:void 0,done:!0}),_errorSteps:c=>r(c)}),s}releaseLock(){if(!Ee(this))throw $t("releaseLock");this._ownerReadableStream!==void 0&&Ui(this)}};n(dn,"ReadableStreamDefaultReader");let fe=dn;Object.defineProperties(fe.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),p(fe.prototype.cancel,"cancel"),p(fe.prototype.read,"read"),p(fe.prototype.releaseLock,"releaseLock"),typeof u.toStringTag=="symbol"&&Object.defineProperty(fe.prototype,u.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});function Ee(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_readRequests")?!1:e instanceof fe}n(Ee,"IsReadableStreamDefaultReader");function yt(e,t){const r=e._ownerReadableStream;r._disturbed=!0,r._state==="closed"?t._closeSteps():r._state==="errored"?t._errorSteps(r._storedError):r._readableStreamController[Br](t)}n(yt,"ReadableStreamDefaultReaderRead");function Ui(e){ge(e);const t=new TypeError("Reader was released");Xn(e,t)}n(Ui,"ReadableStreamDefaultReaderRelease");function Xn(e,t){const r=e._readRequests;e._readRequests=new U,r.forEach(s=>{s._errorSteps(t)})}n(Xn,"ReadableStreamDefaultReaderErrorReadRequests");function $t(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}n($t,"defaultReaderBrandCheckException");const eo=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype),hn=class hn{constructor(t,r){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=t,this._preventCancel=r}next(){const t=n(()=>this._nextSteps(),"nextSteps");return this._ongoingPromise=this._ongoingPromise?M(this._ongoingPromise,t,t):t(),this._ongoingPromise}return(t){const r=n(()=>this._returnSteps(t),"returnSteps");return this._ongoingPromise?M(this._ongoingPromise,r,r):r()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});const t=this._reader;let r,s;const l=A((h,y)=>{r=h,s=y});return yt(t,{_chunkSteps:h=>{this._ongoingPromise=void 0,z(()=>r({value:h,done:!1}))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,ge(t),r({value:void 0,done:!0})},_errorSteps:h=>{this._ongoingPromise=void 0,this._isFinished=!0,ge(t),s(h)}}),l}_returnSteps(t){if(this._isFinished)return Promise.resolve({value:t,done:!0});this._isFinished=!0;const r=this._reader;if(!this._preventCancel){const s=Wr(r,t);return ge(r),M(s,()=>({value:t,done:!0}))}return ge(r),S({value:t,done:!0})}};n(hn,"ReadableStreamAsyncIteratorImpl");let Dt=hn;const to={next(){return ro(this)?this._asyncIteratorImpl.next():m(no("next"))},return(e){return ro(this)?this._asyncIteratorImpl.return(e):m(no("return"))}};eo!==void 0&&Object.setPrototypeOf(to,eo);function xi(e,t){const r=Qe(e),s=new Dt(r,t),l=Object.create(to);return l._asyncIteratorImpl=s,l}n(xi,"AcquireReadableStreamAsyncIterator");function ro(e){if(!d(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof Dt}catch{return!1}}n(ro,"IsReadableStreamAsyncIterator");function no(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}n(no,"streamAsyncIteratorBrandCheckException");const oo=Number.isNaN||function(e){return e!==e};function gt(e){return e.slice()}n(gt,"CreateArrayFromList");function io(e,t,r,s,l){new Uint8Array(e).set(new Uint8Array(r,s,l),t)}n(io,"CopyDataBlockBytes");let Se=n(e=>(typeof e.transfer=="function"?Se=n(t=>t.transfer(),"TransferArrayBuffer"):typeof structuredClone=="function"?Se=n(t=>structuredClone(t,{transfer:[t]}),"TransferArrayBuffer"):Se=n(t=>t,"TransferArrayBuffer"),Se(e)),"TransferArrayBuffer"),Ae=n(e=>(typeof e.detached=="boolean"?Ae=n(t=>t.detached,"IsDetachedBuffer"):Ae=n(t=>t.byteLength===0,"IsDetachedBuffer"),Ae(e)),"IsDetachedBuffer");function ao(e,t,r){if(e.slice)return e.slice(t,r);const s=r-t,l=new ArrayBuffer(s);return io(l,0,e,t,s),l}n(ao,"ArrayBufferSlice");function Mt(e,t){const r=e[t];if(r!=null){if(typeof r!="function")throw new TypeError(`${String(t)} is not a function`);return r}}n(Mt,"GetMethod");function Ni(e){const t={[u.iterator]:()=>e.iterator},r=async function*(){return yield*t}(),s=r.next;return{iterator:r,nextMethod:s,done:!1}}n(Ni,"CreateAsyncFromSyncIterator");function so(e,t="sync",r){if(r===void 0)if(t==="async"){if(r=Mt(e,u.asyncIterator),r===void 0){const c=Mt(e,u.iterator),h=so(e,"sync",c);return Ni(h)}}else r=Mt(e,u.iterator);if(r===void 0)throw new TypeError("The object is not iterable");const s=j(r,e,[]);if(!d(s))throw new TypeError("The iterator method must return an object");const l=s.next;return{iterator:s,nextMethod:l,done:!1}}n(so,"GetIterator");function Hi(e){const t=j(e.nextMethod,e.iterator,[]);if(!d(t))throw new TypeError("The iterator.next() method must return an object");return t}n(Hi,"IteratorNext");function Vi(e){return!!e.done}n(Vi,"IteratorComplete");function Qi(e){return e.value}n(Qi,"IteratorValue");function Yi(e){return!(typeof e!="number"||oo(e)||e<0)}n(Yi,"IsNonNegativeNumber");function uo(e){const t=ao(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}n(uo,"CloneAsUint8Array");function $r(e){const t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}n($r,"DequeueValue");function Dr(e,t,r){if(!Yi(r)||r===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r}n(Dr,"EnqueueValueWithSize");function Gi(e){return e._queue.peek().value}n(Gi,"PeekQueueValue");function Be(e){e._queue=new U,e._queueTotalSize=0}n(Be,"ResetQueue");function lo(e){return e===DataView}n(lo,"isDataViewConstructor");function Zi(e){return lo(e.constructor)}n(Zi,"isDataView");function Ki(e){return lo(e)?1:e.BYTES_PER_ELEMENT}n(Ki,"arrayBufferViewElementSize");const pn=class pn{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!Mr(this))throw Vr("view");return this._view}respond(t){if(!Mr(this))throw Vr("respond");if(_e(t,1,"respond"),t=Fr(t,"First parameter"),this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");if(Ae(this._view.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");Ht(this._associatedReadableByteStreamController,t)}respondWithNewView(t){if(!Mr(this))throw Vr("respondWithNewView");if(_e(t,1,"respondWithNewView"),!ArrayBuffer.isView(t))throw new TypeError("You can only respond with array buffer views");if(this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");if(Ae(t.buffer))throw new TypeError("The given view's buffer has been detached and so cannot be used as a response");Vt(this._associatedReadableByteStreamController,t)}};n(pn,"ReadableStreamBYOBRequest");let we=pn;Object.defineProperties(we.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),p(we.prototype.respond,"respond"),p(we.prototype.respondWithNewView,"respondWithNewView"),typeof u.toStringTag=="symbol"&&Object.defineProperty(we.prototype,u.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});const bn=class bn{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!ze(this))throw St("byobRequest");return Hr(this)}get desiredSize(){if(!ze(this))throw St("desiredSize");return So(this)}close(){if(!ze(this))throw St("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");const t=this._controlledReadableByteStream._state;if(t!=="readable")throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be closed`);_t(this)}enqueue(t){if(!ze(this))throw St("enqueue");if(_e(t,1,"enqueue"),!ArrayBuffer.isView(t))throw new TypeError("chunk must be an array buffer view");if(t.byteLength===0)throw new TypeError("chunk must have non-zero byteLength");if(t.buffer.byteLength===0)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");const r=this._controlledReadableByteStream._state;if(r!=="readable")throw new TypeError(`The stream (in ${r} state) is not in the readable state and cannot be enqueued to`);Nt(this,t)}error(t=void 0){if(!ze(this))throw St("error");K(this,t)}[Ar](t){fo(this),Be(this);const r=this._cancelAlgorithm(t);return xt(this),r}[Br](t){const r=this._controlledReadableByteStream;if(this._queueTotalSize>0){_o(this,t);return}const s=this._autoAllocateChunkSize;if(s!==void 0){let l;try{l=new ArrayBuffer(s)}catch(h){t._errorSteps(h);return}const c={buffer:l,bufferByteLength:s,byteOffset:0,byteLength:s,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(c)}Kn(r,t),Ie(this)}[kr](){if(this._pendingPullIntos.length>0){const t=this._pendingPullIntos.peek();t.readerType="none",this._pendingPullIntos=new U,this._pendingPullIntos.push(t)}}};n(bn,"ReadableByteStreamController");let te=bn;Object.defineProperties(te.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),p(te.prototype.close,"close"),p(te.prototype.enqueue,"enqueue"),p(te.prototype.error,"error"),typeof u.toStringTag=="symbol"&&Object.defineProperty(te.prototype,u.toStringTag,{value:"ReadableByteStreamController",configurable:!0});function ze(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")?!1:e instanceof te}n(ze,"IsReadableByteStreamController");function Mr(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")?!1:e instanceof we}n(Mr,"IsReadableStreamBYOBRequest");function Ie(e){if(!ra(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;const r=e._pullAlgorithm();q(r,()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Ie(e)),null),s=>(K(e,s),null))}n(Ie,"ReadableByteStreamControllerCallPullIfNeeded");function fo(e){xr(e),e._pendingPullIntos=new U}n(fo,"ReadableByteStreamControllerClearPendingPullIntos");function Ur(e,t){let r=!1;e._state==="closed"&&(r=!0);const s=co(t);t.readerType==="default"?Lr(e,s,r):ua(e,s,r)}n(Ur,"ReadableByteStreamControllerCommitPullIntoDescriptor");function co(e){const t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}n(co,"ReadableByteStreamControllerConvertPullIntoDescriptor");function Ut(e,t,r,s){e._queue.push({buffer:t,byteOffset:r,byteLength:s}),e._queueTotalSize+=s}n(Ut,"ReadableByteStreamControllerEnqueueChunkToQueue");function ho(e,t,r,s){let l;try{l=ao(t,r,r+s)}catch(c){throw K(e,c),c}Ut(e,l,0,s)}n(ho,"ReadableByteStreamControllerEnqueueClonedChunkToQueue");function po(e,t){t.bytesFilled>0&&ho(e,t.buffer,t.byteOffset,t.bytesFilled),Ye(e)}n(po,"ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue");function bo(e,t){const r=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),s=t.bytesFilled+r;let l=r,c=!1;const h=s%t.elementSize,y=s-h;y>=t.minimumFill&&(l=y-t.bytesFilled,c=!0);const T=e._queue;for(;l>0;){const g=T.peek(),C=Math.min(l,g.byteLength),P=t.byteOffset+t.bytesFilled;io(t.buffer,P,g.buffer,g.byteOffset,C),g.byteLength===C?T.shift():(g.byteOffset+=C,g.byteLength-=C),e._queueTotalSize-=C,mo(e,C,t),l-=C}return c}n(bo,"ReadableByteStreamControllerFillPullIntoDescriptorFromQueue");function mo(e,t,r){r.bytesFilled+=t}n(mo,"ReadableByteStreamControllerFillHeadPullIntoDescriptor");function yo(e){e._queueTotalSize===0&&e._closeRequested?(xt(e),vt(e._controlledReadableByteStream)):Ie(e)}n(yo,"ReadableByteStreamControllerHandleQueueDrain");function xr(e){e._byobRequest!==null&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}n(xr,"ReadableByteStreamControllerInvalidateBYOBRequest");function Nr(e){for(;e._pendingPullIntos.length>0;){if(e._queueTotalSize===0)return;const t=e._pendingPullIntos.peek();bo(e,t)&&(Ye(e),Ur(e._controlledReadableByteStream,t))}}n(Nr,"ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue");function Ji(e){const t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(e._queueTotalSize===0)return;const r=t._readRequests.shift();_o(e,r)}}n(Ji,"ReadableByteStreamControllerProcessReadRequestsUsingQueue");function Xi(e,t,r,s){const l=e._controlledReadableByteStream,c=t.constructor,h=Ki(c),{byteOffset:y,byteLength:T}=t,g=r*h;let C;try{C=Se(t.buffer)}catch(B){s._errorSteps(B);return}const P={buffer:C,bufferByteLength:C.byteLength,byteOffset:y,byteLength:T,bytesFilled:0,minimumFill:g,elementSize:h,viewConstructor:c,readerType:"byob"};if(e._pendingPullIntos.length>0){e._pendingPullIntos.push(P),To(l,s);return}if(l._state==="closed"){const B=new c(P.buffer,P.byteOffset,0);s._closeSteps(B);return}if(e._queueTotalSize>0){if(bo(e,P)){const B=co(P);yo(e),s._chunkSteps(B);return}if(e._closeRequested){const B=new TypeError("Insufficient bytes to fill elements in the given buffer");K(e,B),s._errorSteps(B);return}}e._pendingPullIntos.push(P),To(l,s),Ie(e)}n(Xi,"ReadableByteStreamControllerPullInto");function ea(e,t){t.readerType==="none"&&Ye(e);const r=e._controlledReadableByteStream;if(Qr(r))for(;Co(r)>0;){const s=Ye(e);Ur(r,s)}}n(ea,"ReadableByteStreamControllerRespondInClosedState");function ta(e,t,r){if(mo(e,t,r),r.readerType==="none"){po(e,r),Nr(e);return}if(r.bytesFilled<r.minimumFill)return;Ye(e);const s=r.bytesFilled%r.elementSize;if(s>0){const l=r.byteOffset+r.bytesFilled;ho(e,r.buffer,l-s,s)}r.bytesFilled-=s,Ur(e._controlledReadableByteStream,r),Nr(e)}n(ta,"ReadableByteStreamControllerRespondInReadableState");function go(e,t){const r=e._pendingPullIntos.peek();xr(e),e._controlledReadableByteStream._state==="closed"?ea(e,r):ta(e,t,r),Ie(e)}n(go,"ReadableByteStreamControllerRespondInternal");function Ye(e){return e._pendingPullIntos.shift()}n(Ye,"ReadableByteStreamControllerShiftPendingPullInto");function ra(e){const t=e._controlledReadableByteStream;return t._state!=="readable"||e._closeRequested||!e._started?!1:!!(Jn(t)&&Lt(t)>0||Qr(t)&&Co(t)>0||So(e)>0)}n(ra,"ReadableByteStreamControllerShouldCallPull");function xt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}n(xt,"ReadableByteStreamControllerClearAlgorithms");function _t(e){const t=e._controlledReadableByteStream;if(!(e._closeRequested||t._state!=="readable")){if(e._queueTotalSize>0){e._closeRequested=!0;return}if(e._pendingPullIntos.length>0){const r=e._pendingPullIntos.peek();if(r.bytesFilled%r.elementSize!==0){const s=new TypeError("Insufficient bytes to fill elements in the given buffer");throw K(e,s),s}}xt(e),vt(t)}}n(_t,"ReadableByteStreamControllerClose");function Nt(e,t){const r=e._controlledReadableByteStream;if(e._closeRequested||r._state!=="readable")return;const{buffer:s,byteOffset:l,byteLength:c}=t;if(Ae(s))throw new TypeError("chunk's buffer is detached and so cannot be enqueued");const h=Se(s);if(e._pendingPullIntos.length>0){const y=e._pendingPullIntos.peek();if(Ae(y.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");xr(e),y.buffer=Se(y.buffer),y.readerType==="none"&&po(e,y)}if(Jn(r))if(Ji(e),Lt(r)===0)Ut(e,h,l,c);else{e._pendingPullIntos.length>0&&Ye(e);const y=new Uint8Array(h,l,c);Lr(r,y,!1)}else Qr(r)?(Ut(e,h,l,c),Nr(e)):Ut(e,h,l,c);Ie(e)}n(Nt,"ReadableByteStreamControllerEnqueue");function K(e,t){const r=e._controlledReadableByteStream;r._state==="readable"&&(fo(e),Be(e),xt(e),Yo(r,t))}n(K,"ReadableByteStreamControllerError");function _o(e,t){const r=e._queue.shift();e._queueTotalSize-=r.byteLength,yo(e);const s=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(s)}n(_o,"ReadableByteStreamControllerFillReadRequestFromQueue");function Hr(e){if(e._byobRequest===null&&e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),s=Object.create(we.prototype);oa(s,e,r),e._byobRequest=s}return e._byobRequest}n(Hr,"ReadableByteStreamControllerGetBYOBRequest");function So(e){const t=e._controlledReadableByteStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}n(So,"ReadableByteStreamControllerGetDesiredSize");function Ht(e,t){const r=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t!==0)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(t===0)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range")}r.buffer=Se(r.buffer),go(e,t)}n(Ht,"ReadableByteStreamControllerRespond");function Vt(e,t){const r=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t.byteLength!==0)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(t.byteLength===0)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");const l=t.byteLength;r.buffer=Se(t.buffer),go(e,l)}n(Vt,"ReadableByteStreamControllerRespondWithNewView");function wo(e,t,r,s,l,c,h){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,Be(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=c,t._pullAlgorithm=s,t._cancelAlgorithm=l,t._autoAllocateChunkSize=h,t._pendingPullIntos=new U,e._readableStreamController=t;const y=r();q(S(y),()=>(t._started=!0,Ie(t),null),T=>(K(t,T),null))}n(wo,"SetUpReadableByteStreamController");function na(e,t,r){const s=Object.create(te.prototype);let l,c,h;t.start!==void 0?l=n(()=>t.start(s),"startAlgorithm"):l=n(()=>{},"startAlgorithm"),t.pull!==void 0?c=n(()=>t.pull(s),"pullAlgorithm"):c=n(()=>S(void 0),"pullAlgorithm"),t.cancel!==void 0?h=n(T=>t.cancel(T),"cancelAlgorithm"):h=n(()=>S(void 0),"cancelAlgorithm");const y=t.autoAllocateChunkSize;if(y===0)throw new TypeError("autoAllocateChunkSize must be greater than 0");wo(e,s,l,c,h,r,y)}n(na,"SetUpReadableByteStreamControllerFromUnderlyingSource");function oa(e,t,r){e._associatedReadableByteStreamController=t,e._view=r}n(oa,"SetUpReadableStreamBYOBRequest");function Vr(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}n(Vr,"byobRequestBrandCheckException");function St(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}n(St,"byteStreamControllerBrandCheckException");function ia(e,t){le(e,t);const r=e?.mode;return{mode:r===void 0?void 0:aa(r,`${t} has member 'mode' that`)}}n(ia,"convertReaderOptions");function aa(e,t){if(e=`${e}`,e!=="byob")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}n(aa,"convertReadableStreamReaderMode");function sa(e,t){var r;le(e,t);const s=(r=e?.min)!==null&&r!==void 0?r:1;return{min:Fr(s,`${t} has member 'min' that`)}}n(sa,"convertByobReadOptions");function Ro(e){return new ce(e)}n(Ro,"AcquireReadableStreamBYOBReader");function To(e,t){e._reader._readIntoRequests.push(t)}n(To,"ReadableStreamAddReadIntoRequest");function ua(e,t,r){const l=e._reader._readIntoRequests.shift();r?l._closeSteps(t):l._chunkSteps(t)}n(ua,"ReadableStreamFulfillReadIntoRequest");function Co(e){return e._reader._readIntoRequests.length}n(Co,"ReadableStreamGetNumReadIntoRequests");function Qr(e){const t=e._reader;return!(t===void 0||!Fe(t))}n(Qr,"ReadableStreamHasBYOBReader");const mn=class mn{constructor(t){if(_e(t,1,"ReadableStreamBYOBReader"),jr(t,"First parameter"),qe(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!ze(t._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");Hn(this,t),this._readIntoRequests=new U}get closed(){return Fe(this)?this._closedPromise:m(Qt("closed"))}cancel(t=void 0){return Fe(this)?this._ownerReadableStream===void 0?m(jt("cancel")):Wr(this,t):m(Qt("cancel"))}read(t,r={}){if(!Fe(this))return m(Qt("read"));if(!ArrayBuffer.isView(t))return m(new TypeError("view must be an array buffer view"));if(t.byteLength===0)return m(new TypeError("view must have non-zero byteLength"));if(t.buffer.byteLength===0)return m(new TypeError("view's buffer must have non-zero byteLength"));if(Ae(t.buffer))return m(new TypeError("view's buffer has been detached"));let s;try{s=sa(r,"options")}catch(g){return m(g)}const l=s.min;if(l===0)return m(new TypeError("options.min must be greater than 0"));if(Zi(t)){if(l>t.byteLength)return m(new RangeError("options.min must be less than or equal to view's byteLength"))}else if(l>t.length)return m(new RangeError("options.min must be less than or equal to view's length"));if(this._ownerReadableStream===void 0)return m(jt("read from"));let c,h;const y=A((g,C)=>{c=g,h=C});return Po(this,t,l,{_chunkSteps:g=>c({value:g,done:!1}),_closeSteps:g=>c({value:g,done:!0}),_errorSteps:g=>h(g)}),y}releaseLock(){if(!Fe(this))throw Qt("releaseLock");this._ownerReadableStream!==void 0&&la(this)}};n(mn,"ReadableStreamBYOBReader");let ce=mn;Object.defineProperties(ce.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),p(ce.prototype.cancel,"cancel"),p(ce.prototype.read,"read"),p(ce.prototype.releaseLock,"releaseLock"),typeof u.toStringTag=="symbol"&&Object.defineProperty(ce.prototype,u.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});function Fe(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")?!1:e instanceof ce}n(Fe,"IsReadableStreamBYOBReader");function Po(e,t,r,s){const l=e._ownerReadableStream;l._disturbed=!0,l._state==="errored"?s._errorSteps(l._storedError):Xi(l._readableStreamController,t,r,s)}n(Po,"ReadableStreamBYOBReaderRead");function la(e){ge(e);const t=new TypeError("Reader was released");vo(e,t)}n(la,"ReadableStreamBYOBReaderRelease");function vo(e,t){const r=e._readIntoRequests;e._readIntoRequests=new U,r.forEach(s=>{s._errorSteps(t)})}n(vo,"ReadableStreamBYOBReaderErrorReadIntoRequests");function Qt(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}n(Qt,"byobReaderBrandCheckException");function wt(e,t){const{highWaterMark:r}=e;if(r===void 0)return t;if(oo(r)||r<0)throw new RangeError("Invalid highWaterMark");return r}n(wt,"ExtractHighWaterMark");function Yt(e){const{size:t}=e;return t||(()=>1)}n(Yt,"ExtractSizeAlgorithm");function Gt(e,t){le(e,t);const r=e?.highWaterMark,s=e?.size;return{highWaterMark:r===void 0?void 0:Ir(r),size:s===void 0?void 0:fa(s,`${t} has member 'size' that`)}}n(Gt,"convertQueuingStrategy");function fa(e,t){return Z(e,t),r=>Ir(e(r))}n(fa,"convertQueuingStrategySize");function ca(e,t){le(e,t);const r=e?.abort,s=e?.close,l=e?.start,c=e?.type,h=e?.write;return{abort:r===void 0?void 0:da(r,e,`${t} has member 'abort' that`),close:s===void 0?void 0:ha(s,e,`${t} has member 'close' that`),start:l===void 0?void 0:pa(l,e,`${t} has member 'start' that`),write:h===void 0?void 0:ba(h,e,`${t} has member 'write' that`),type:c}}n(ca,"convertUnderlyingSink");function da(e,t,r){return Z(e,r),s=>I(e,t,[s])}n(da,"convertUnderlyingSinkAbortCallback");function ha(e,t,r){return Z(e,r),()=>I(e,t,[])}n(ha,"convertUnderlyingSinkCloseCallback");function pa(e,t,r){return Z(e,r),s=>j(e,t,[s])}n(pa,"convertUnderlyingSinkStartCallback");function ba(e,t,r){return Z(e,r),(s,l)=>I(e,t,[s,l])}n(ba,"convertUnderlyingSinkWriteCallback");function Eo(e,t){if(!Ge(e))throw new TypeError(`${t} is not a WritableStream.`)}n(Eo,"assertWritableStream");function ma(e){if(typeof e!="object"||e===null)return!1;try{return typeof e.aborted=="boolean"}catch{return!1}}n(ma,"isAbortSignal");const ya=typeof AbortController=="function";function ga(){if(ya)return new AbortController}n(ga,"createAbortController");const yn=class yn{constructor(t={},r={}){t===void 0?t=null:Gn(t,"First parameter");const s=Gt(r,"Second parameter"),l=ca(t,"First parameter");if(Bo(this),l.type!==void 0)throw new RangeError("Invalid type is specified");const h=Yt(s),y=wt(s,1);qa(this,l,y,h)}get locked(){if(!Ge(this))throw er("locked");return Ze(this)}abort(t=void 0){return Ge(this)?Ze(this)?m(new TypeError("Cannot abort a stream that already has a writer")):Zt(this,t):m(er("abort"))}close(){return Ge(this)?Ze(this)?m(new TypeError("Cannot close a stream that already has a writer")):he(this)?m(new TypeError("Cannot close an already-closing stream")):ko(this):m(er("close"))}getWriter(){if(!Ge(this))throw er("getWriter");return Ao(this)}};n(yn,"WritableStream");let de=yn;Object.defineProperties(de.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),p(de.prototype.abort,"abort"),p(de.prototype.close,"close"),p(de.prototype.getWriter,"getWriter"),typeof u.toStringTag=="symbol"&&Object.defineProperty(de.prototype,u.toStringTag,{value:"WritableStream",configurable:!0});function Ao(e){return new re(e)}n(Ao,"AcquireWritableStreamDefaultWriter");function _a(e,t,r,s,l=1,c=()=>1){const h=Object.create(de.prototype);Bo(h);const y=Object.create(ke.prototype);return Fo(h,y,e,t,r,s,l,c),h}n(_a,"CreateWritableStream");function Bo(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new U,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}n(Bo,"InitializeWritableStream");function Ge(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")?!1:e instanceof de}n(Ge,"IsWritableStream");function Ze(e){return e._writer!==void 0}n(Ze,"IsWritableStreamLocked");function Zt(e,t){var r;if(e._state==="closed"||e._state==="errored")return S(void 0);e._writableStreamController._abortReason=t,(r=e._writableStreamController._abortController)===null||r===void 0||r.abort(t);const s=e._state;if(s==="closed"||s==="errored")return S(void 0);if(e._pendingAbortRequest!==void 0)return e._pendingAbortRequest._promise;let l=!1;s==="erroring"&&(l=!0,t=void 0);const c=A((h,y)=>{e._pendingAbortRequest={_promise:void 0,_resolve:h,_reject:y,_reason:t,_wasAlreadyErroring:l}});return e._pendingAbortRequest._promise=c,l||Gr(e,t),c}n(Zt,"WritableStreamAbort");function ko(e){const t=e._state;if(t==="closed"||t==="errored")return m(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));const r=A((l,c)=>{const h={_resolve:l,_reject:c};e._closeRequest=h}),s=e._writer;return s!==void 0&&e._backpressure&&t==="writable"&&nn(s),Oa(e._writableStreamController),r}n(ko,"WritableStreamClose");function Sa(e){return A((r,s)=>{const l={_resolve:r,_reject:s};e._writeRequests.push(l)})}n(Sa,"WritableStreamAddWriteRequest");function Yr(e,t){if(e._state==="writable"){Gr(e,t);return}Zr(e)}n(Yr,"WritableStreamDealWithRejection");function Gr(e,t){const r=e._writableStreamController;e._state="erroring",e._storedError=t;const s=e._writer;s!==void 0&&qo(s,t),!Pa(e)&&r._started&&Zr(e)}n(Gr,"WritableStreamStartErroring");function Zr(e){e._state="errored",e._writableStreamController[Nn]();const t=e._storedError;if(e._writeRequests.forEach(l=>{l._reject(t)}),e._writeRequests=new U,e._pendingAbortRequest===void 0){Kt(e);return}const r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring){r._reject(t),Kt(e);return}const s=e._writableStreamController[xn](r._reason);q(s,()=>(r._resolve(),Kt(e),null),l=>(r._reject(l),Kt(e),null))}n(Zr,"WritableStreamFinishErroring");function wa(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}n(wa,"WritableStreamFinishInFlightWrite");function Ra(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,Yr(e,t)}n(Ra,"WritableStreamFinishInFlightWriteWithError");function Ta(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,e._state==="erroring"&&(e._storedError=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";const r=e._writer;r!==void 0&&Do(r)}n(Ta,"WritableStreamFinishInFlightClose");function Ca(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),Yr(e,t)}n(Ca,"WritableStreamFinishInFlightCloseWithError");function he(e){return!(e._closeRequest===void 0&&e._inFlightCloseRequest===void 0)}n(he,"WritableStreamCloseQueuedOrInFlight");function Pa(e){return!(e._inFlightWriteRequest===void 0&&e._inFlightCloseRequest===void 0)}n(Pa,"WritableStreamHasOperationMarkedInFlight");function va(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0}n(va,"WritableStreamMarkCloseRequestInFlight");function Ea(e){e._inFlightWriteRequest=e._writeRequests.shift()}n(Ea,"WritableStreamMarkFirstWriteRequestInFlight");function Kt(e){e._closeRequest!==void 0&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);const t=e._writer;t!==void 0&&tn(t,e._storedError)}n(Kt,"WritableStreamRejectCloseAndClosedPromiseIfNeeded");function Kr(e,t){const r=e._writer;r!==void 0&&t!==e._backpressure&&(t?Da(r):nn(r)),e._backpressure=t}n(Kr,"WritableStreamUpdateBackpressure");const gn=class gn{constructor(t){if(_e(t,1,"WritableStreamDefaultWriter"),Eo(t,"First parameter"),Ze(t))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=t,t._writer=this;const r=t._state;if(r==="writable")!he(t)&&t._backpressure?rr(this):Mo(this),tr(this);else if(r==="erroring")rn(this,t._storedError),tr(this);else if(r==="closed")Mo(this),La(this);else{const s=t._storedError;rn(this,s),$o(this,s)}}get closed(){return je(this)?this._closedPromise:m(Le("closed"))}get desiredSize(){if(!je(this))throw Le("desiredSize");if(this._ownerWritableStream===void 0)throw Tt("desiredSize");return Wa(this)}get ready(){return je(this)?this._readyPromise:m(Le("ready"))}abort(t=void 0){return je(this)?this._ownerWritableStream===void 0?m(Tt("abort")):Aa(this,t):m(Le("abort"))}close(){if(!je(this))return m(Le("close"));const t=this._ownerWritableStream;return t===void 0?m(Tt("close")):he(t)?m(new TypeError("Cannot close an already-closing stream")):Wo(this)}releaseLock(){if(!je(this))throw Le("releaseLock");this._ownerWritableStream!==void 0&&Oo(this)}write(t=void 0){return je(this)?this._ownerWritableStream===void 0?m(Tt("write to")):zo(this,t):m(Le("write"))}};n(gn,"WritableStreamDefaultWriter");let re=gn;Object.defineProperties(re.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),p(re.prototype.abort,"abort"),p(re.prototype.close,"close"),p(re.prototype.releaseLock,"releaseLock"),p(re.prototype.write,"write"),typeof u.toStringTag=="symbol"&&Object.defineProperty(re.prototype,u.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});function je(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")?!1:e instanceof re}n(je,"IsWritableStreamDefaultWriter");function Aa(e,t){const r=e._ownerWritableStream;return Zt(r,t)}n(Aa,"WritableStreamDefaultWriterAbort");function Wo(e){const t=e._ownerWritableStream;return ko(t)}n(Wo,"WritableStreamDefaultWriterClose");function Ba(e){const t=e._ownerWritableStream,r=t._state;return he(t)||r==="closed"?S(void 0):r==="errored"?m(t._storedError):Wo(e)}n(Ba,"WritableStreamDefaultWriterCloseWithErrorPropagation");function ka(e,t){e._closedPromiseState==="pending"?tn(e,t):$a(e,t)}n(ka,"WritableStreamDefaultWriterEnsureClosedPromiseRejected");function qo(e,t){e._readyPromiseState==="pending"?Uo(e,t):Ma(e,t)}n(qo,"WritableStreamDefaultWriterEnsureReadyPromiseRejected");function Wa(e){const t=e._ownerWritableStream,r=t._state;return r==="errored"||r==="erroring"?null:r==="closed"?0:jo(t._writableStreamController)}n(Wa,"WritableStreamDefaultWriterGetDesiredSize");function Oo(e){const t=e._ownerWritableStream,r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");qo(e,r),ka(e,r),t._writer=void 0,e._ownerWritableStream=void 0}n(Oo,"WritableStreamDefaultWriterRelease");function zo(e,t){const r=e._ownerWritableStream,s=r._writableStreamController,l=za(s,t);if(r!==e._ownerWritableStream)return m(Tt("write to"));const c=r._state;if(c==="errored")return m(r._storedError);if(he(r)||c==="closed")return m(new TypeError("The stream is closing or closed and cannot be written to"));if(c==="erroring")return m(r._storedError);const h=Sa(r);return Ia(s,t,l),h}n(zo,"WritableStreamDefaultWriterWrite");const Io={},_n=class _n{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!Jr(this))throw en("abortReason");return this._abortReason}get signal(){if(!Jr(this))throw en("signal");if(this._abortController===void 0)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(t=void 0){if(!Jr(this))throw en("error");this._controlledWritableStream._state==="writable"&&Lo(this,t)}[xn](t){const r=this._abortAlgorithm(t);return Jt(this),r}[Nn](){Be(this)}};n(_n,"WritableStreamDefaultController");let ke=_n;Object.defineProperties(ke.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),typeof u.toStringTag=="symbol"&&Object.defineProperty(ke.prototype,u.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});function Jr(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")?!1:e instanceof ke}n(Jr,"IsWritableStreamDefaultController");function Fo(e,t,r,s,l,c,h,y){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,Be(t),t._abortReason=void 0,t._abortController=ga(),t._started=!1,t._strategySizeAlgorithm=y,t._strategyHWM=h,t._writeAlgorithm=s,t._closeAlgorithm=l,t._abortAlgorithm=c;const T=Xr(t);Kr(e,T);const g=r(),C=S(g);q(C,()=>(t._started=!0,Xt(t),null),P=>(t._started=!0,Yr(e,P),null))}n(Fo,"SetUpWritableStreamDefaultController");function qa(e,t,r,s){const l=Object.create(ke.prototype);let c,h,y,T;t.start!==void 0?c=n(()=>t.start(l),"startAlgorithm"):c=n(()=>{},"startAlgorithm"),t.write!==void 0?h=n(g=>t.write(g,l),"writeAlgorithm"):h=n(()=>S(void 0),"writeAlgorithm"),t.close!==void 0?y=n(()=>t.close(),"closeAlgorithm"):y=n(()=>S(void 0),"closeAlgorithm"),t.abort!==void 0?T=n(g=>t.abort(g),"abortAlgorithm"):T=n(()=>S(void 0),"abortAlgorithm"),Fo(e,l,c,h,y,T,r,s)}n(qa,"SetUpWritableStreamDefaultControllerFromUnderlyingSink");function Jt(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}n(Jt,"WritableStreamDefaultControllerClearAlgorithms");function Oa(e){Dr(e,Io,0),Xt(e)}n(Oa,"WritableStreamDefaultControllerClose");function za(e,t){try{return e._strategySizeAlgorithm(t)}catch(r){return Rt(e,r),1}}n(za,"WritableStreamDefaultControllerGetChunkSize");function jo(e){return e._strategyHWM-e._queueTotalSize}n(jo,"WritableStreamDefaultControllerGetDesiredSize");function Ia(e,t,r){try{Dr(e,t,r)}catch(l){Rt(e,l);return}const s=e._controlledWritableStream;if(!he(s)&&s._state==="writable"){const l=Xr(e);Kr(s,l)}Xt(e)}n(Ia,"WritableStreamDefaultControllerWrite");function Xt(e){const t=e._controlledWritableStream;if(!e._started||t._inFlightWriteRequest!==void 0)return;if(t._state==="erroring"){Zr(t);return}if(e._queue.length===0)return;const s=Gi(e);s===Io?Fa(e):ja(e,s)}n(Xt,"WritableStreamDefaultControllerAdvanceQueueIfNeeded");function Rt(e,t){e._controlledWritableStream._state==="writable"&&Lo(e,t)}n(Rt,"WritableStreamDefaultControllerErrorIfNeeded");function Fa(e){const t=e._controlledWritableStream;va(t),$r(e);const r=e._closeAlgorithm();Jt(e),q(r,()=>(Ta(t),null),s=>(Ca(t,s),null))}n(Fa,"WritableStreamDefaultControllerProcessClose");function ja(e,t){const r=e._controlledWritableStream;Ea(r);const s=e._writeAlgorithm(t);q(s,()=>{wa(r);const l=r._state;if($r(e),!he(r)&&l==="writable"){const c=Xr(e);Kr(r,c)}return Xt(e),null},l=>(r._state==="writable"&&Jt(e),Ra(r,l),null))}n(ja,"WritableStreamDefaultControllerProcessWrite");function Xr(e){return jo(e)<=0}n(Xr,"WritableStreamDefaultControllerGetBackpressure");function Lo(e,t){const r=e._controlledWritableStream;Jt(e),Gr(r,t)}n(Lo,"WritableStreamDefaultControllerError");function er(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}n(er,"streamBrandCheckException$2");function en(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}n(en,"defaultControllerBrandCheckException$2");function Le(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}n(Le,"defaultWriterBrandCheckException");function Tt(e){return new TypeError("Cannot "+e+" a stream using a released writer")}n(Tt,"defaultWriterLockException");function tr(e){e._closedPromise=A((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"})}n(tr,"defaultWriterClosedPromiseInitialize");function $o(e,t){tr(e),tn(e,t)}n($o,"defaultWriterClosedPromiseInitializeAsRejected");function La(e){tr(e),Do(e)}n(La,"defaultWriterClosedPromiseInitializeAsResolved");function tn(e,t){e._closedPromise_reject!==void 0&&(ve(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}n(tn,"defaultWriterClosedPromiseReject");function $a(e,t){$o(e,t)}n($a,"defaultWriterClosedPromiseResetToRejected");function Do(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}n(Do,"defaultWriterClosedPromiseResolve");function rr(e){e._readyPromise=A((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r}),e._readyPromiseState="pending"}n(rr,"defaultWriterReadyPromiseInitialize");function rn(e,t){rr(e),Uo(e,t)}n(rn,"defaultWriterReadyPromiseInitializeAsRejected");function Mo(e){rr(e),nn(e)}n(Mo,"defaultWriterReadyPromiseInitializeAsResolved");function Uo(e,t){e._readyPromise_reject!==void 0&&(ve(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}n(Uo,"defaultWriterReadyPromiseReject");function Da(e){rr(e)}n(Da,"defaultWriterReadyPromiseReset");function Ma(e,t){rn(e,t)}n(Ma,"defaultWriterReadyPromiseResetToRejected");function nn(e){e._readyPromise_resolve!==void 0&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}n(nn,"defaultWriterReadyPromiseResolve");function Ua(){if(typeof globalThis<"u")return globalThis;if(typeof self<"u")return self;if(typeof En<"u")return En}n(Ua,"getGlobals");const on=Ua();function xa(e){if(!(typeof e=="function"||typeof e=="object")||e.name!=="DOMException")return!1;try{return new e,!0}catch{return!1}}n(xa,"isDOMExceptionConstructor");function Na(){const e=on?.DOMException;return xa(e)?e:void 0}n(Na,"getFromGlobal");function Ha(){const e=n(function(r,s){this.message=r||"",this.name=s||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)},"DOMException");return p(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}n(Ha,"createPolyfill");const Va=Na()||Ha();function xo(e,t,r,s,l,c){const h=Qe(e),y=Ao(t);e._disturbed=!0;let T=!1,g=S(void 0);return A((C,P)=>{let B;if(c!==void 0){if(B=n(()=>{const _=c.reason!==void 0?c.reason:new Va("Aborted","AbortError"),v=[];s||v.push(()=>t._state==="writable"?Zt(t,_):S(void 0)),l||v.push(()=>e._state==="readable"?ie(e,_):S(void 0)),H(()=>Promise.all(v.map(k=>k())),!0,_)},"abortAlgorithm"),c.aborted){B();return}c.addEventListener("abort",B)}function ae(){return A((_,v)=>{function k(Y){Y?_():R(nt(),k,v)}n(k,"next"),k(!1)})}n(ae,"pipeLoop");function nt(){return T?S(!0):R(y._readyPromise,()=>A((_,v)=>{yt(h,{_chunkSteps:k=>{g=R(zo(y,k),void 0,f),_(!1)},_closeSteps:()=>_(!0),_errorSteps:v})}))}if(n(nt,"pipeStep"),Re(e,h._closedPromise,_=>(s?J(!0,_):H(()=>Zt(t,_),!0,_),null)),Re(t,y._closedPromise,_=>(l?J(!0,_):H(()=>ie(e,_),!0,_),null)),N(e,h._closedPromise,()=>(r?J():H(()=>Ba(y)),null)),he(t)||t._state==="closed"){const _=new TypeError("the destination writable stream closed before all data could be piped to it");l?J(!0,_):H(()=>ie(e,_),!0,_)}ve(ae());function Oe(){const _=g;return R(g,()=>_!==g?Oe():void 0)}n(Oe,"waitForWritesToFinish");function Re(_,v,k){_._state==="errored"?k(_._storedError):Q(v,k)}n(Re,"isOrBecomesErrored");function N(_,v,k){_._state==="closed"?k():F(v,k)}n(N,"isOrBecomesClosed");function H(_,v,k){if(T)return;T=!0,t._state==="writable"&&!he(t)?F(Oe(),Y):Y();function Y(){return q(_(),()=>Te(v,k),ot=>Te(!0,ot)),null}n(Y,"doTheRest")}n(H,"shutdownWithAction");function J(_,v){T||(T=!0,t._state==="writable"&&!he(t)?F(Oe(),()=>Te(_,v)):Te(_,v))}n(J,"shutdown");function Te(_,v){return Oo(y),ge(h),c!==void 0&&c.removeEventListener("abort",B),_?P(v):C(void 0),null}n(Te,"finalize")})}n(xo,"ReadableStreamPipeTo");const Sn=class Sn{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!nr(this))throw ir("desiredSize");return an(this)}close(){if(!nr(this))throw ir("close");if(!Je(this))throw new TypeError("The stream is not in a state that permits close");$e(this)}enqueue(t=void 0){if(!nr(this))throw ir("enqueue");if(!Je(this))throw new TypeError("The stream is not in a state that permits enqueue");return Ke(this,t)}error(t=void 0){if(!nr(this))throw ir("error");oe(this,t)}[Ar](t){Be(this);const r=this._cancelAlgorithm(t);return or(this),r}[Br](t){const r=this._controlledReadableStream;if(this._queue.length>0){const s=$r(this);this._closeRequested&&this._queue.length===0?(or(this),vt(r)):Ct(this),t._chunkSteps(s)}else Kn(r,t),Ct(this)}[kr](){}};n(Sn,"ReadableStreamDefaultController");let ne=Sn;Object.defineProperties(ne.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),p(ne.prototype.close,"close"),p(ne.prototype.enqueue,"enqueue"),p(ne.prototype.error,"error"),typeof u.toStringTag=="symbol"&&Object.defineProperty(ne.prototype,u.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});function nr(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")?!1:e instanceof ne}n(nr,"IsReadableStreamDefaultController");function Ct(e){if(!No(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;const r=e._pullAlgorithm();q(r,()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Ct(e)),null),s=>(oe(e,s),null))}n(Ct,"ReadableStreamDefaultControllerCallPullIfNeeded");function No(e){const t=e._controlledReadableStream;return!Je(e)||!e._started?!1:!!(qe(t)&&Lt(t)>0||an(e)>0)}n(No,"ReadableStreamDefaultControllerShouldCallPull");function or(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}n(or,"ReadableStreamDefaultControllerClearAlgorithms");function $e(e){if(!Je(e))return;const t=e._controlledReadableStream;e._closeRequested=!0,e._queue.length===0&&(or(e),vt(t))}n($e,"ReadableStreamDefaultControllerClose");function Ke(e,t){if(!Je(e))return;const r=e._controlledReadableStream;if(qe(r)&&Lt(r)>0)Lr(r,t,!1);else{let s;try{s=e._strategySizeAlgorithm(t)}catch(l){throw oe(e,l),l}try{Dr(e,t,s)}catch(l){throw oe(e,l),l}}Ct(e)}n(Ke,"ReadableStreamDefaultControllerEnqueue");function oe(e,t){const r=e._controlledReadableStream;r._state==="readable"&&(Be(e),or(e),Yo(r,t))}n(oe,"ReadableStreamDefaultControllerError");function an(e){const t=e._controlledReadableStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}n(an,"ReadableStreamDefaultControllerGetDesiredSize");function Qa(e){return!No(e)}n(Qa,"ReadableStreamDefaultControllerHasBackpressure");function Je(e){const t=e._controlledReadableStream._state;return!e._closeRequested&&t==="readable"}n(Je,"ReadableStreamDefaultControllerCanCloseOrEnqueue");function Ho(e,t,r,s,l,c,h){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,Be(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=h,t._strategyHWM=c,t._pullAlgorithm=s,t._cancelAlgorithm=l,e._readableStreamController=t;const y=r();q(S(y),()=>(t._started=!0,Ct(t),null),T=>(oe(t,T),null))}n(Ho,"SetUpReadableStreamDefaultController");function Ya(e,t,r,s){const l=Object.create(ne.prototype);let c,h,y;t.start!==void 0?c=n(()=>t.start(l),"startAlgorithm"):c=n(()=>{},"startAlgorithm"),t.pull!==void 0?h=n(()=>t.pull(l),"pullAlgorithm"):h=n(()=>S(void 0),"pullAlgorithm"),t.cancel!==void 0?y=n(T=>t.cancel(T),"cancelAlgorithm"):y=n(()=>S(void 0),"cancelAlgorithm"),Ho(e,l,c,h,y,r,s)}n(Ya,"SetUpReadableStreamDefaultControllerFromUnderlyingSource");function ir(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}n(ir,"defaultControllerBrandCheckException$1");function Ga(e,t){return ze(e._readableStreamController)?Ka(e):Za(e)}n(Ga,"ReadableStreamTee");function Za(e,t){const r=Qe(e);let s=!1,l=!1,c=!1,h=!1,y,T,g,C,P;const B=A(N=>{P=N});function ae(){return s?(l=!0,S(void 0)):(s=!0,yt(r,{_chunkSteps:H=>{z(()=>{l=!1;const J=H,Te=H;c||Ke(g._readableStreamController,J),h||Ke(C._readableStreamController,Te),s=!1,l&&ae()})},_closeSteps:()=>{s=!1,c||$e(g._readableStreamController),h||$e(C._readableStreamController),(!c||!h)&&P(void 0)},_errorSteps:()=>{s=!1}}),S(void 0))}n(ae,"pullAlgorithm");function nt(N){if(c=!0,y=N,h){const H=gt([y,T]),J=ie(e,H);P(J)}return B}n(nt,"cancel1Algorithm");function Oe(N){if(h=!0,T=N,c){const H=gt([y,T]),J=ie(e,H);P(J)}return B}n(Oe,"cancel2Algorithm");function Re(){}return n(Re,"startAlgorithm"),g=Pt(Re,ae,nt),C=Pt(Re,ae,Oe),Q(r._closedPromise,N=>(oe(g._readableStreamController,N),oe(C._readableStreamController,N),(!c||!h)&&P(void 0),null)),[g,C]}n(Za,"ReadableStreamDefaultTee");function Ka(e){let t=Qe(e),r=!1,s=!1,l=!1,c=!1,h=!1,y,T,g,C,P;const B=A(_=>{P=_});function ae(_){Q(_._closedPromise,v=>(_!==t||(K(g._readableStreamController,v),K(C._readableStreamController,v),(!c||!h)&&P(void 0)),null))}n(ae,"forwardReaderError");function nt(){Fe(t)&&(ge(t),t=Qe(e),ae(t)),yt(t,{_chunkSteps:v=>{z(()=>{s=!1,l=!1;const k=v;let Y=v;if(!c&&!h)try{Y=uo(v)}catch(ot){K(g._readableStreamController,ot),K(C._readableStreamController,ot),P(ie(e,ot));return}c||Nt(g._readableStreamController,k),h||Nt(C._readableStreamController,Y),r=!1,s?Re():l&&N()})},_closeSteps:()=>{r=!1,c||_t(g._readableStreamController),h||_t(C._readableStreamController),g._readableStreamController._pendingPullIntos.length>0&&Ht(g._readableStreamController,0),C._readableStreamController._pendingPullIntos.length>0&&Ht(C._readableStreamController,0),(!c||!h)&&P(void 0)},_errorSteps:()=>{r=!1}})}n(nt,"pullWithDefaultReader");function Oe(_,v){Ee(t)&&(ge(t),t=Ro(e),ae(t));const k=v?C:g,Y=v?g:C;Po(t,_,1,{_chunkSteps:it=>{z(()=>{s=!1,l=!1;const at=v?h:c;if(v?c:h)at||Vt(k._readableStreamController,it);else{let si;try{si=uo(it)}catch(vn){K(k._readableStreamController,vn),K(Y._readableStreamController,vn),P(ie(e,vn));return}at||Vt(k._readableStreamController,it),Nt(Y._readableStreamController,si)}r=!1,s?Re():l&&N()})},_closeSteps:it=>{r=!1;const at=v?h:c,fr=v?c:h;at||_t(k._readableStreamController),fr||_t(Y._readableStreamController),it!==void 0&&(at||Vt(k._readableStreamController,it),!fr&&Y._readableStreamController._pendingPullIntos.length>0&&Ht(Y._readableStreamController,0)),(!at||!fr)&&P(void 0)},_errorSteps:()=>{r=!1}})}n(Oe,"pullWithBYOBReader");function Re(){if(r)return s=!0,S(void 0);r=!0;const _=Hr(g._readableStreamController);return _===null?nt():Oe(_._view,!1),S(void 0)}n(Re,"pull1Algorithm");function N(){if(r)return l=!0,S(void 0);r=!0;const _=Hr(C._readableStreamController);return _===null?nt():Oe(_._view,!0),S(void 0)}n(N,"pull2Algorithm");function H(_){if(c=!0,y=_,h){const v=gt([y,T]),k=ie(e,v);P(k)}return B}n(H,"cancel1Algorithm");function J(_){if(h=!0,T=_,c){const v=gt([y,T]),k=ie(e,v);P(k)}return B}n(J,"cancel2Algorithm");function Te(){}return n(Te,"startAlgorithm"),g=Qo(Te,Re,H),C=Qo(Te,N,J),ae(t),[g,C]}n(Ka,"ReadableByteStreamTee");function Ja(e){return d(e)&&typeof e.getReader<"u"}n(Ja,"isReadableStreamLike");function Xa(e){return Ja(e)?ts(e.getReader()):es(e)}n(Xa,"ReadableStreamFrom");function es(e){let t;const r=so(e,"async"),s=f;function l(){let h;try{h=Hi(r)}catch(T){return m(T)}const y=S(h);return M(y,T=>{if(!d(T))throw new TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(Vi(T))$e(t._readableStreamController);else{const C=Qi(T);Ke(t._readableStreamController,C)}})}n(l,"pullAlgorithm");function c(h){const y=r.iterator;let T;try{T=Mt(y,"return")}catch(P){return m(P)}if(T===void 0)return S(void 0);let g;try{g=j(T,y,[h])}catch(P){return m(P)}const C=S(g);return M(C,P=>{if(!d(P))throw new TypeError("The promise returned by the iterator.return() method must fulfill with an object")})}return n(c,"cancelAlgorithm"),t=Pt(s,l,c,0),t}n(es,"ReadableStreamFromIterable");function ts(e){let t;const r=f;function s(){let c;try{c=e.read()}catch(h){return m(h)}return M(c,h=>{if(!d(h))throw new TypeError("The promise returned by the reader.read() method must fulfill with an object");if(h.done)$e(t._readableStreamController);else{const y=h.value;Ke(t._readableStreamController,y)}})}n(s,"pullAlgorithm");function l(c){try{return S(e.cancel(c))}catch(h){return m(h)}}return n(l,"cancelAlgorithm"),t=Pt(r,s,l,0),t}n(ts,"ReadableStreamFromDefaultReader");function rs(e,t){le(e,t);const r=e,s=r?.autoAllocateChunkSize,l=r?.cancel,c=r?.pull,h=r?.start,y=r?.type;return{autoAllocateChunkSize:s===void 0?void 0:Fr(s,`${t} has member 'autoAllocateChunkSize' that`),cancel:l===void 0?void 0:ns(l,r,`${t} has member 'cancel' that`),pull:c===void 0?void 0:os(c,r,`${t} has member 'pull' that`),start:h===void 0?void 0:is(h,r,`${t} has member 'start' that`),type:y===void 0?void 0:as(y,`${t} has member 'type' that`)}}n(rs,"convertUnderlyingDefaultOrByteSource");function ns(e,t,r){return Z(e,r),s=>I(e,t,[s])}n(ns,"convertUnderlyingSourceCancelCallback");function os(e,t,r){return Z(e,r),s=>I(e,t,[s])}n(os,"convertUnderlyingSourcePullCallback");function is(e,t,r){return Z(e,r),s=>j(e,t,[s])}n(is,"convertUnderlyingSourceStartCallback");function as(e,t){if(e=`${e}`,e!=="bytes")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}n(as,"convertReadableStreamType");function ss(e,t){return le(e,t),{preventCancel:!!e?.preventCancel}}n(ss,"convertIteratorOptions");function Vo(e,t){le(e,t);const r=e?.preventAbort,s=e?.preventCancel,l=e?.preventClose,c=e?.signal;return c!==void 0&&us(c,`${t} has member 'signal' that`),{preventAbort:!!r,preventCancel:!!s,preventClose:!!l,signal:c}}n(Vo,"convertPipeOptions");function us(e,t){if(!ma(e))throw new TypeError(`${t} is not an AbortSignal.`)}n(us,"assertAbortSignal");function ls(e,t){le(e,t);const r=e?.readable;zr(r,"readable","ReadableWritablePair"),jr(r,`${t} has member 'readable' that`);const s=e?.writable;return zr(s,"writable","ReadableWritablePair"),Eo(s,`${t} has member 'writable' that`),{readable:r,writable:s}}n(ls,"convertReadableWritablePair");const wn=class wn{constructor(t={},r={}){t===void 0?t=null:Gn(t,"First parameter");const s=Gt(r,"Second parameter"),l=rs(t,"First parameter");if(sn(this),l.type==="bytes"){if(s.size!==void 0)throw new RangeError("The strategy for a byte stream cannot have a size function");const c=wt(s,0);na(this,l,c)}else{const c=Yt(s),h=wt(s,1);Ya(this,l,h,c)}}get locked(){if(!We(this))throw De("locked");return qe(this)}cancel(t=void 0){return We(this)?qe(this)?m(new TypeError("Cannot cancel a stream that already has a reader")):ie(this,t):m(De("cancel"))}getReader(t=void 0){if(!We(this))throw De("getReader");return ia(t,"First parameter").mode===void 0?Qe(this):Ro(this)}pipeThrough(t,r={}){if(!We(this))throw De("pipeThrough");_e(t,1,"pipeThrough");const s=ls(t,"First parameter"),l=Vo(r,"Second parameter");if(qe(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(Ze(s.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");const c=xo(this,s.writable,l.preventClose,l.preventAbort,l.preventCancel,l.signal);return ve(c),s.readable}pipeTo(t,r={}){if(!We(this))return m(De("pipeTo"));if(t===void 0)return m("Parameter 1 is required in 'pipeTo'.");if(!Ge(t))return m(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let s;try{s=Vo(r,"Second parameter")}catch(l){return m(l)}return qe(this)?m(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):Ze(t)?m(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):xo(this,t,s.preventClose,s.preventAbort,s.preventCancel,s.signal)}tee(){if(!We(this))throw De("tee");const t=Ga(this);return gt(t)}values(t=void 0){if(!We(this))throw De("values");const r=ss(t,"First parameter");return xi(this,r.preventCancel)}static from(t){return Xa(t)}};n(wn,"ReadableStream");let L=wn;Object.defineProperties(L,{from:{enumerable:!0}}),Object.defineProperties(L.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),p(L.from,"from"),p(L.prototype.cancel,"cancel"),p(L.prototype.getReader,"getReader"),p(L.prototype.pipeThrough,"pipeThrough"),p(L.prototype.pipeTo,"pipeTo"),p(L.prototype.tee,"tee"),p(L.prototype.values,"values"),typeof u.toStringTag=="symbol"&&Object.defineProperty(L.prototype,u.toStringTag,{value:"ReadableStream",configurable:!0}),typeof u.asyncIterator=="symbol"&&Object.defineProperty(L.prototype,u.asyncIterator,{value:L.prototype.values,writable:!0,configurable:!0});function Pt(e,t,r,s=1,l=()=>1){const c=Object.create(L.prototype);sn(c);const h=Object.create(ne.prototype);return Ho(c,h,e,t,r,s,l),c}n(Pt,"CreateReadableStream");function Qo(e,t,r){const s=Object.create(L.prototype);sn(s);const l=Object.create(te.prototype);return wo(s,l,e,t,r,0,void 0),s}n(Qo,"CreateReadableByteStream");function sn(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}n(sn,"InitializeReadableStream");function We(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")?!1:e instanceof L}n(We,"IsReadableStream");function qe(e){return e._reader!==void 0}n(qe,"IsReadableStreamLocked");function ie(e,t){if(e._disturbed=!0,e._state==="closed")return S(void 0);if(e._state==="errored")return m(e._storedError);vt(e);const r=e._reader;if(r!==void 0&&Fe(r)){const l=r._readIntoRequests;r._readIntoRequests=new U,l.forEach(c=>{c._closeSteps(void 0)})}const s=e._readableStreamController[Ar](t);return M(s,f)}n(ie,"ReadableStreamCancel");function vt(e){e._state="closed";const t=e._reader;if(t!==void 0&&(Qn(t),Ee(t))){const r=t._readRequests;t._readRequests=new U,r.forEach(s=>{s._closeSteps()})}}n(vt,"ReadableStreamClose");function Yo(e,t){e._state="errored",e._storedError=t;const r=e._reader;r!==void 0&&(Or(r,t),Ee(r)?Xn(r,t):vo(r,t))}n(Yo,"ReadableStreamError");function De(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}n(De,"streamBrandCheckException$1");function Go(e,t){le(e,t);const r=e?.highWaterMark;return zr(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:Ir(r)}}n(Go,"convertQueuingStrategyInit");const Zo=n(e=>e.byteLength,"byteLengthSizeFunction");p(Zo,"size");const Rn=class Rn{constructor(t){_e(t,1,"ByteLengthQueuingStrategy"),t=Go(t,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!Jo(this))throw Ko("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!Jo(this))throw Ko("size");return Zo}};n(Rn,"ByteLengthQueuingStrategy");let Xe=Rn;Object.defineProperties(Xe.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof u.toStringTag=="symbol"&&Object.defineProperty(Xe.prototype,u.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});function Ko(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}n(Ko,"byteLengthBrandCheckException");function Jo(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")?!1:e instanceof Xe}n(Jo,"IsByteLengthQueuingStrategy");const Xo=n(()=>1,"countSizeFunction");p(Xo,"size");const Tn=class Tn{constructor(t){_e(t,1,"CountQueuingStrategy"),t=Go(t,"First parameter"),this._countQueuingStrategyHighWaterMark=t.highWaterMark}get highWaterMark(){if(!ti(this))throw ei("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!ti(this))throw ei("size");return Xo}};n(Tn,"CountQueuingStrategy");let et=Tn;Object.defineProperties(et.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof u.toStringTag=="symbol"&&Object.defineProperty(et.prototype,u.toStringTag,{value:"CountQueuingStrategy",configurable:!0});function ei(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}n(ei,"countBrandCheckException");function ti(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")?!1:e instanceof et}n(ti,"IsCountQueuingStrategy");function fs(e,t){le(e,t);const r=e?.cancel,s=e?.flush,l=e?.readableType,c=e?.start,h=e?.transform,y=e?.writableType;return{cancel:r===void 0?void 0:ps(r,e,`${t} has member 'cancel' that`),flush:s===void 0?void 0:cs(s,e,`${t} has member 'flush' that`),readableType:l,start:c===void 0?void 0:ds(c,e,`${t} has member 'start' that`),transform:h===void 0?void 0:hs(h,e,`${t} has member 'transform' that`),writableType:y}}n(fs,"convertTransformer");function cs(e,t,r){return Z(e,r),s=>I(e,t,[s])}n(cs,"convertTransformerFlushCallback");function ds(e,t,r){return Z(e,r),s=>j(e,t,[s])}n(ds,"convertTransformerStartCallback");function hs(e,t,r){return Z(e,r),(s,l)=>I(e,t,[s,l])}n(hs,"convertTransformerTransformCallback");function ps(e,t,r){return Z(e,r),s=>I(e,t,[s])}n(ps,"convertTransformerCancelCallback");const Cn=class Cn{constructor(t={},r={},s={}){t===void 0&&(t=null);const l=Gt(r,"Second parameter"),c=Gt(s,"Third parameter"),h=fs(t,"First parameter");if(h.readableType!==void 0)throw new RangeError("Invalid readableType specified");if(h.writableType!==void 0)throw new RangeError("Invalid writableType specified");const y=wt(c,0),T=Yt(c),g=wt(l,1),C=Yt(l);let P;const B=A(ae=>{P=ae});bs(this,B,g,C,y,T),ys(this,h),h.start!==void 0?P(h.start(this._transformStreamController)):P(void 0)}get readable(){if(!ri(this))throw ai("readable");return this._readable}get writable(){if(!ri(this))throw ai("writable");return this._writable}};n(Cn,"TransformStream");let tt=Cn;Object.defineProperties(tt.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),typeof u.toStringTag=="symbol"&&Object.defineProperty(tt.prototype,u.toStringTag,{value:"TransformStream",configurable:!0});function bs(e,t,r,s,l,c){function h(){return t}n(h,"startAlgorithm");function y(B){return Ss(e,B)}n(y,"writeAlgorithm");function T(B){return ws(e,B)}n(T,"abortAlgorithm");function g(){return Rs(e)}n(g,"closeAlgorithm"),e._writable=_a(h,y,g,T,r,s);function C(){return Ts(e)}n(C,"pullAlgorithm");function P(B){return Cs(e,B)}n(P,"cancelAlgorithm"),e._readable=Pt(h,C,P,l,c),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,ar(e,!0),e._transformStreamController=void 0}n(bs,"InitializeTransformStream");function ri(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")?!1:e instanceof tt}n(ri,"IsTransformStream");function ni(e,t){oe(e._readable._readableStreamController,t),un(e,t)}n(ni,"TransformStreamError");function un(e,t){ur(e._transformStreamController),Rt(e._writable._writableStreamController,t),ln(e)}n(un,"TransformStreamErrorWritableAndUnblockWrite");function ln(e){e._backpressure&&ar(e,!1)}n(ln,"TransformStreamUnblockWrite");function ar(e,t){e._backpressureChangePromise!==void 0&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=A(r=>{e._backpressureChangePromise_resolve=r}),e._backpressure=t}n(ar,"TransformStreamSetBackpressure");const Pn=class Pn{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!sr(this))throw lr("desiredSize");const t=this._controlledTransformStream._readable._readableStreamController;return an(t)}enqueue(t=void 0){if(!sr(this))throw lr("enqueue");oi(this,t)}error(t=void 0){if(!sr(this))throw lr("error");gs(this,t)}terminate(){if(!sr(this))throw lr("terminate");_s(this)}};n(Pn,"TransformStreamDefaultController");let pe=Pn;Object.defineProperties(pe.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),p(pe.prototype.enqueue,"enqueue"),p(pe.prototype.error,"error"),p(pe.prototype.terminate,"terminate"),typeof u.toStringTag=="symbol"&&Object.defineProperty(pe.prototype,u.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});function sr(e){return!d(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")?!1:e instanceof pe}n(sr,"IsTransformStreamDefaultController");function ms(e,t,r,s,l){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=s,t._cancelAlgorithm=l,t._finishPromise=void 0,t._finishPromise_resolve=void 0,t._finishPromise_reject=void 0}n(ms,"SetUpTransformStreamDefaultController");function ys(e,t){const r=Object.create(pe.prototype);let s,l,c;t.transform!==void 0?s=n(h=>t.transform(h,r),"transformAlgorithm"):s=n(h=>{try{return oi(r,h),S(void 0)}catch(y){return m(y)}},"transformAlgorithm"),t.flush!==void 0?l=n(()=>t.flush(r),"flushAlgorithm"):l=n(()=>S(void 0),"flushAlgorithm"),t.cancel!==void 0?c=n(h=>t.cancel(h),"cancelAlgorithm"):c=n(()=>S(void 0),"cancelAlgorithm"),ms(e,r,s,l,c)}n(ys,"SetUpTransformStreamDefaultControllerFromTransformer");function ur(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}n(ur,"TransformStreamDefaultControllerClearAlgorithms");function oi(e,t){const r=e._controlledTransformStream,s=r._readable._readableStreamController;if(!Je(s))throw new TypeError("Readable side is not in a state that permits enqueue");try{Ke(s,t)}catch(c){throw un(r,c),r._readable._storedError}Qa(s)!==r._backpressure&&ar(r,!0)}n(oi,"TransformStreamDefaultControllerEnqueue");function gs(e,t){ni(e._controlledTransformStream,t)}n(gs,"TransformStreamDefaultControllerError");function ii(e,t){const r=e._transformAlgorithm(t);return M(r,void 0,s=>{throw ni(e._controlledTransformStream,s),s})}n(ii,"TransformStreamDefaultControllerPerformTransform");function _s(e){const t=e._controlledTransformStream,r=t._readable._readableStreamController;$e(r);const s=new TypeError("TransformStream terminated");un(t,s)}n(_s,"TransformStreamDefaultControllerTerminate");function Ss(e,t){const r=e._transformStreamController;if(e._backpressure){const s=e._backpressureChangePromise;return M(s,()=>{const l=e._writable;if(l._state==="erroring")throw l._storedError;return ii(r,t)})}return ii(r,t)}n(Ss,"TransformStreamDefaultSinkWriteAlgorithm");function ws(e,t){const r=e._transformStreamController;if(r._finishPromise!==void 0)return r._finishPromise;const s=e._readable;r._finishPromise=A((c,h)=>{r._finishPromise_resolve=c,r._finishPromise_reject=h});const l=r._cancelAlgorithm(t);return ur(r),q(l,()=>(s._state==="errored"?rt(r,s._storedError):(oe(s._readableStreamController,t),fn(r)),null),c=>(oe(s._readableStreamController,c),rt(r,c),null)),r._finishPromise}n(ws,"TransformStreamDefaultSinkAbortAlgorithm");function Rs(e){const t=e._transformStreamController;if(t._finishPromise!==void 0)return t._finishPromise;const r=e._readable;t._finishPromise=A((l,c)=>{t._finishPromise_resolve=l,t._finishPromise_reject=c});const s=t._flushAlgorithm();return ur(t),q(s,()=>(r._state==="errored"?rt(t,r._storedError):($e(r._readableStreamController),fn(t)),null),l=>(oe(r._readableStreamController,l),rt(t,l),null)),t._finishPromise}n(Rs,"TransformStreamDefaultSinkCloseAlgorithm");function Ts(e){return ar(e,!1),e._backpressureChangePromise}n(Ts,"TransformStreamDefaultSourcePullAlgorithm");function Cs(e,t){const r=e._transformStreamController;if(r._finishPromise!==void 0)return r._finishPromise;const s=e._writable;r._finishPromise=A((c,h)=>{r._finishPromise_resolve=c,r._finishPromise_reject=h});const l=r._cancelAlgorithm(t);return ur(r),q(l,()=>(s._state==="errored"?rt(r,s._storedError):(Rt(s._writableStreamController,t),ln(e),fn(r)),null),c=>(Rt(s._writableStreamController,c),ln(e),rt(r,c),null)),r._finishPromise}n(Cs,"TransformStreamDefaultSourceCancelAlgorithm");function lr(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}n(lr,"defaultControllerBrandCheckException");function fn(e){e._finishPromise_resolve!==void 0&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}n(fn,"defaultControllerFinishPromiseResolve");function rt(e,t){e._finishPromise_reject!==void 0&&(ve(e._finishPromise),e._finishPromise_reject(t),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}n(rt,"defaultControllerFinishPromiseReject");function ai(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}n(ai,"streamBrandCheckException"),a.ByteLengthQueuingStrategy=Xe,a.CountQueuingStrategy=et,a.ReadableByteStreamController=te,a.ReadableStream=L,a.ReadableStreamBYOBReader=ce,a.ReadableStreamBYOBRequest=we,a.ReadableStreamDefaultController=ne,a.ReadableStreamDefaultReader=fe,a.TransformStream=tt,a.TransformStreamDefaultController=pe,a.WritableStream=de,a.WritableStreamDefaultController=ke,a.WritableStreamDefaultWriter=re})}(pr,pr.exports)),pr.exports}n(Is,"requirePonyfill_es2018");const Fs=65536;if(!globalThis.ReadableStream)try{const i=require("node:process"),{emitWarning:o}=i;try{i.emitWarning=()=>{},Object.assign(globalThis,require("node:stream/web")),i.emitWarning=o}catch(a){throw i.emitWarning=o,a}}catch{Object.assign(globalThis,Is())}try{const{Blob:i}=require("buffer");i&&!i.prototype.stream&&(i.prototype.stream=n(function(a){let u=0;const f=this;return new ReadableStream({type:"bytes",async pull(d){const p=await f.slice(u,Math.min(f.size,u+Fs)).arrayBuffer();u+=p.byteLength,d.enqueue(new Uint8Array(p)),u===f.size&&d.close()}})},"name"))}catch{}/*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */const ci=65536;async function*An(i,o=!0){for(const a of i)if("stream"in a)yield*a.stream();else if(ArrayBuffer.isView(a))if(o){let u=a.byteOffset;const f=a.byteOffset+a.byteLength;for(;u!==f;){const d=Math.min(f-u,ci),b=a.buffer.slice(u,u+d);u+=b.byteLength,yield new Uint8Array(b)}}else yield a;else{let u=0,f=a;for(;u!==f.size;){const b=await f.slice(u,Math.min(f.size,u+ci)).arrayBuffer();u+=b.byteLength,yield new Uint8Array(b)}}}n(An,"toIterator");const di=(Ve=class{constructor(o=[],a={}){be(this,Pe,[]);be(this,Wt,"");be(this,bt,0);be(this,Cr,"transparent");if(typeof o!="object"||o===null)throw new TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if(typeof o[Symbol.iterator]!="function")throw new TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if(typeof a!="object"&&typeof a!="function")throw new TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");a===null&&(a={});const u=new TextEncoder;for(const d of o){let b;ArrayBuffer.isView(d)?b=new Uint8Array(d.buffer.slice(d.byteOffset,d.byteOffset+d.byteLength)):d instanceof ArrayBuffer?b=new Uint8Array(d.slice(0)):d instanceof Ve?b=d:b=u.encode(`${d}`),X(this,bt,O(this,bt)+(ArrayBuffer.isView(b)?b.byteLength:b.size)),O(this,Pe).push(b)}X(this,Cr,`${a.endings===void 0?"transparent":a.endings}`);const f=a.type===void 0?"":String(a.type);X(this,Wt,/^[\x20-\x7E]*$/.test(f)?f:"")}get size(){return O(this,bt)}get type(){return O(this,Wt)}async text(){const o=new TextDecoder;let a="";for await(const u of An(O(this,Pe),!1))a+=o.decode(u,{stream:!0});return a+=o.decode(),a}async arrayBuffer(){const o=new Uint8Array(this.size);let a=0;for await(const u of An(O(this,Pe),!1))o.set(u,a),a+=u.length;return o.buffer}stream(){const o=An(O(this,Pe),!0);return new globalThis.ReadableStream({type:"bytes",async pull(a){const u=await o.next();u.done?a.close():a.enqueue(u.value)},async cancel(){await o.return()}})}slice(o=0,a=this.size,u=""){const{size:f}=this;let d=o<0?Math.max(f+o,0):Math.min(o,f),b=a<0?Math.max(f+a,0):Math.min(a,f);const p=Math.max(b-d,0),E=O(this,Pe),w=[];let D=0;for(const S of E){if(D>=p)break;const m=ArrayBuffer.isView(S)?S.byteLength:S.size;if(d&&m<=d)d-=m,b-=m;else{let R;ArrayBuffer.isView(S)?(R=S.subarray(d,Math.min(m,b)),D+=R.byteLength):(R=S.slice(d,Math.min(m,b)),D+=R.size),b-=m,w.push(R),d=0}}const A=new Ve([],{type:String(u).toLowerCase()});return X(A,bt,p),X(A,Pe,w),A}get[Symbol.toStringTag](){return"Blob"}static[Symbol.hasInstance](o){return o&&typeof o=="object"&&typeof o.constructor=="function"&&(typeof o.stream=="function"||typeof o.arrayBuffer=="function")&&/^(Blob|File)$/.test(o[Symbol.toStringTag])}},Pe=new WeakMap,Wt=new WeakMap,bt=new WeakMap,Cr=new WeakMap,n(Ve,"Blob"),Ve);Object.defineProperties(di.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}});const js=di,lt=js,Ls=(zt=class extends lt{constructor(a,u,f={}){if(arguments.length<2)throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);super(a,f);be(this,qt,0);be(this,Ot,"");f===null&&(f={});const d=f.lastModified===void 0?Date.now():Number(f.lastModified);Number.isNaN(d)||X(this,qt,d),X(this,Ot,String(u))}get name(){return O(this,Ot)}get lastModified(){return O(this,qt)}get[Symbol.toStringTag](){return"File"}static[Symbol.hasInstance](a){return!!a&&a instanceof lt&&/^(File)$/.test(a[Symbol.toStringTag])}},qt=new WeakMap,Ot=new WeakMap,n(zt,"File"),zt),$s=Ls,Bn=$s;/*! formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */var{toStringTag:At,iterator:Ds,hasInstance:Ms}=Symbol,hi=Math.random,Us="append,set,get,getAll,delete,keys,values,entries,forEach,constructor".split(","),pi=n((i,o,a)=>(i+="",/^(Blob|File)$/.test(o&&o[At])?[(a=a!==void 0?a+"":o[At]=="File"?o.name:"blob",i),o.name!==a||o[At]=="blob"?new Bn([o],a,o):o]:[i,o+""]),"f"),kn=n((i,o)=>(o?i:i.replace(/\r?\n|\r/g,`\r
`)).replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22"),"e$1"),Me=n((i,o,a)=>{if(o.length<a)throw new TypeError(`Failed to execute '${i}' on 'FormData': ${a} arguments required, but only ${o.length} present.`)},"x");const br=(It=class{constructor(...o){be(this,ee,[]);if(o.length)throw new TypeError("Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.")}get[At](){return"FormData"}[Ds](){return this.entries()}static[Ms](o){return o&&typeof o=="object"&&o[At]==="FormData"&&!Us.some(a=>typeof o[a]!="function")}append(...o){Me("append",arguments,2),O(this,ee).push(pi(...o))}delete(o){Me("delete",arguments,1),o+="",X(this,ee,O(this,ee).filter(([a])=>a!==o))}get(o){Me("get",arguments,1),o+="";for(var a=O(this,ee),u=a.length,f=0;f<u;f++)if(a[f][0]===o)return a[f][1];return null}getAll(o,a){return Me("getAll",arguments,1),a=[],o+="",O(this,ee).forEach(u=>u[0]===o&&a.push(u[1])),a}has(o){return Me("has",arguments,1),o+="",O(this,ee).some(a=>a[0]===o)}forEach(o,a){Me("forEach",arguments,1);for(var[u,f]of this)o.call(a,f,u,this)}set(...o){Me("set",arguments,2);var a=[],u=!0;o=pi(...o),O(this,ee).forEach(f=>{f[0]===o[0]?u&&(u=!a.push(o)):a.push(f)}),u&&a.push(o),X(this,ee,a)}*entries(){yield*O(this,ee)}*keys(){for(var[o]of this)yield o}*values(){for(var[,o]of this)yield o}},ee=new WeakMap,n(It,"FormData"),It);function xs(i,o=lt){var a=`${hi()}${hi()}`.replace(/\./g,"").slice(-28).padStart(32,"-"),u=[],f=`--${a}\r
Content-Disposition: form-data; name="`;return i.forEach((d,b)=>typeof d=="string"?u.push(f+kn(b)+`"\r
\r
${d.replace(/\r(?!\n)|(?<!\r)\n/g,`\r
`)}\r
`):u.push(f+kn(b)+`"; filename="${kn(d.name,1)}"\r
Content-Type: ${d.type||"application/octet-stream"}\r
\r
`,d,`\r
`)),u.push(`--${a}--`),new o(u,{type:"multipart/form-data; boundary="+a})}n(xs,"formDataToBlob");const Ln=class Ln extends Error{constructor(o,a){super(o),Error.captureStackTrace(this,this.constructor),this.type=a}get name(){return this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}};n(Ln,"FetchBaseError");let ft=Ln;const $n=class $n extends ft{constructor(o,a,u){super(o,a),u&&(this.code=this.errno=u.code,this.erroredSysCall=u.syscall)}};n($n,"FetchError");let G=$n;const mr=Symbol.toStringTag,bi=n(i=>typeof i=="object"&&typeof i.append=="function"&&typeof i.delete=="function"&&typeof i.get=="function"&&typeof i.getAll=="function"&&typeof i.has=="function"&&typeof i.set=="function"&&typeof i.sort=="function"&&i[mr]==="URLSearchParams","isURLSearchParameters"),yr=n(i=>i&&typeof i=="object"&&typeof i.arrayBuffer=="function"&&typeof i.type=="string"&&typeof i.stream=="function"&&typeof i.constructor=="function"&&/^(Blob|File)$/.test(i[mr]),"isBlob"),Ns=n(i=>typeof i=="object"&&(i[mr]==="AbortSignal"||i[mr]==="EventTarget"),"isAbortSignal"),Hs=n((i,o)=>{const a=new URL(o).hostname,u=new URL(i).hostname;return a===u||a.endsWith(`.${u}`)},"isDomainOrSubdomain"),Vs=n((i,o)=>{const a=new URL(o).protocol,u=new URL(i).protocol;return a===u},"isSameProtocol"),Qs=Es(me.pipeline),V=Symbol("Body internals"),Dn=class Dn{constructor(o,{size:a=0}={}){let u=null;o===null?o=null:bi(o)?o=x.from(o.toString()):yr(o)||x.isBuffer(o)||(dr.isAnyArrayBuffer(o)?o=x.from(o):ArrayBuffer.isView(o)?o=x.from(o.buffer,o.byteOffset,o.byteLength):o instanceof me||(o instanceof br?(o=xs(o),u=o.type.split("=")[1]):o=x.from(String(o))));let f=o;x.isBuffer(o)?f=me.Readable.from(o):yr(o)&&(f=me.Readable.from(o.stream())),this[V]={body:o,stream:f,boundary:u,disturbed:!1,error:null},this.size=a,o instanceof me&&o.on("error",d=>{const b=d instanceof ft?d:new G(`Invalid response body while trying to fetch ${this.url}: ${d.message}`,"system",d);this[V].error=b})}get body(){return this[V].stream}get bodyUsed(){return this[V].disturbed}async arrayBuffer(){const{buffer:o,byteOffset:a,byteLength:u}=await Wn(this);return o.slice(a,a+u)}async formData(){const o=this.headers.get("content-type");if(o.startsWith("application/x-www-form-urlencoded")){const u=new br,f=new URLSearchParams(await this.text());for(const[d,b]of f)u.append(d,b);return u}const{toFormData:a}=await import("./chunks/multipart-parser.mjs");return a(this.body,o)}async blob(){const o=this.headers&&this.headers.get("content-type")||this[V].body&&this[V].body.type||"",a=await this.arrayBuffer();return new lt([a],{type:o})}async json(){const o=await this.text();return JSON.parse(o)}async text(){const o=await Wn(this);return new TextDecoder().decode(o)}buffer(){return Wn(this)}};n(Dn,"Body");let Ue=Dn;Ue.prototype.buffer=hr(Ue.prototype.buffer,"Please use 'response.arrayBuffer()' instead of 'response.buffer()'","node-fetch#buffer"),Object.defineProperties(Ue.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0},data:{get:hr(()=>{},"data doesn't exist, use json(), text(), arrayBuffer(), or body instead","https://github.com/node-fetch/node-fetch/issues/1000 (response)")}});async function Wn(i){if(i[V].disturbed)throw new TypeError(`body used already for: ${i.url}`);if(i[V].disturbed=!0,i[V].error)throw i[V].error;const{body:o}=i;if(o===null)return x.alloc(0);if(!(o instanceof me))return x.alloc(0);const a=[];let u=0;try{for await(const f of o){if(i.size>0&&u+f.length>i.size){const d=new G(`content size at ${i.url} over limit: ${i.size}`,"max-size");throw o.destroy(d),d}u+=f.length,a.push(f)}}catch(f){throw f instanceof ft?f:new G(`Invalid response body while trying to fetch ${i.url}: ${f.message}`,"system",f)}if(o.readableEnded===!0||o._readableState.ended===!0)try{return a.every(f=>typeof f=="string")?x.from(a.join("")):x.concat(a,u)}catch(f){throw new G(`Could not create Buffer from response body for ${i.url}: ${f.message}`,"system",f)}else throw new G(`Premature close of server response while trying to fetch ${i.url}`)}n(Wn,"consumeBody");const qn=n((i,o)=>{let a,u,{body:f}=i[V];if(i.bodyUsed)throw new Error("cannot clone body after it is used");return f instanceof me&&typeof f.getBoundary!="function"&&(a=new cr({highWaterMark:o}),u=new cr({highWaterMark:o}),f.pipe(a),f.pipe(u),i[V].stream=a,f=u),f},"clone"),Ys=hr(i=>i.getBoundary(),"form-data doesn't follow the spec and requires special treatment. Use alternative package","https://github.com/node-fetch/node-fetch/issues/1167"),mi=n((i,o)=>i===null?null:typeof i=="string"?"text/plain;charset=UTF-8":bi(i)?"application/x-www-form-urlencoded;charset=UTF-8":yr(i)?i.type||null:x.isBuffer(i)||dr.isAnyArrayBuffer(i)||ArrayBuffer.isView(i)?null:i instanceof br?`multipart/form-data; boundary=${o[V].boundary}`:i&&typeof i.getBoundary=="function"?`multipart/form-data;boundary=${Ys(i)}`:i instanceof me?null:"text/plain;charset=UTF-8","extractContentType"),Gs=n(i=>{const{body:o}=i[V];return o===null?0:yr(o)?o.size:x.isBuffer(o)?o.length:o&&typeof o.getLengthSync=="function"&&o.hasKnownLength&&o.hasKnownLength()?o.getLengthSync():null},"getTotalBytes"),Zs=n(async(i,{body:o})=>{o===null?i.end():await Qs(o,i)},"writeToStream"),gr=typeof Et.validateHeaderName=="function"?Et.validateHeaderName:i=>{if(!/^[\^`\-\w!#$%&'*+.|~]+$/.test(i)){const o=new TypeError(`Header name must be a valid HTTP token [${i}]`);throw Object.defineProperty(o,"code",{value:"ERR_INVALID_HTTP_TOKEN"}),o}},On=typeof Et.validateHeaderValue=="function"?Et.validateHeaderValue:(i,o)=>{if(/[^\t\u0020-\u007E\u0080-\u00FF]/.test(o)){const a=new TypeError(`Invalid character in header content ["${i}"]`);throw Object.defineProperty(a,"code",{value:"ERR_INVALID_CHAR"}),a}},Pr=class Pr extends URLSearchParams{constructor(o){let a=[];if(o instanceof Pr){const u=o.raw();for(const[f,d]of Object.entries(u))a.push(...d.map(b=>[f,b]))}else if(o!=null)if(typeof o=="object"&&!dr.isBoxedPrimitive(o)){const u=o[Symbol.iterator];if(u==null)a.push(...Object.entries(o));else{if(typeof u!="function")throw new TypeError("Header pairs must be iterable");a=[...o].map(f=>{if(typeof f!="object"||dr.isBoxedPrimitive(f))throw new TypeError("Each header pair must be an iterable object");return[...f]}).map(f=>{if(f.length!==2)throw new TypeError("Each header pair must be a name/value tuple");return[...f]})}}else throw new TypeError("Failed to construct 'Headers': The provided value is not of type '(sequence<sequence<ByteString>> or record<ByteString, ByteString>)");return a=a.length>0?a.map(([u,f])=>(gr(u),On(u,String(f)),[String(u).toLowerCase(),String(f)])):void 0,super(a),new Proxy(this,{get(u,f,d){switch(f){case"append":case"set":return(b,p)=>(gr(b),On(b,String(p)),URLSearchParams.prototype[f].call(u,String(b).toLowerCase(),String(p)));case"delete":case"has":case"getAll":return b=>(gr(b),URLSearchParams.prototype[f].call(u,String(b).toLowerCase()));case"keys":return()=>(u.sort(),new Set(URLSearchParams.prototype.keys.call(u)).keys());default:return Reflect.get(u,f,d)}}})}get[Symbol.toStringTag](){return this.constructor.name}toString(){return Object.prototype.toString.call(this)}get(o){const a=this.getAll(o);if(a.length===0)return null;let u=a.join(", ");return/^content-encoding$/i.test(o)&&(u=u.toLowerCase()),u}forEach(o,a=void 0){for(const u of this.keys())Reflect.apply(o,a,[this.get(u),u,this])}*values(){for(const o of this.keys())yield this.get(o)}*entries(){for(const o of this.keys())yield[o,this.get(o)]}[Symbol.iterator](){return this.entries()}raw(){return[...this.keys()].reduce((o,a)=>(o[a]=this.getAll(a),o),{})}[Symbol.for("nodejs.util.inspect.custom")](){return[...this.keys()].reduce((o,a)=>{const u=this.getAll(a);return a==="host"?o[a]=u[0]:o[a]=u.length>1?u:u[0],o},{})}};n(Pr,"Headers");let ye=Pr;Object.defineProperties(ye.prototype,["get","entries","forEach","values"].reduce((i,o)=>(i[o]={enumerable:!0},i),{}));function Ks(i=[]){return new ye(i.reduce((o,a,u,f)=>(u%2===0&&o.push(f.slice(u,u+2)),o),[]).filter(([o,a])=>{try{return gr(o),On(o,String(a)),!0}catch{return!1}}))}n(Ks,"fromRawHeaders");const Js=new Set([301,302,303,307,308]),zn=n(i=>Js.has(i),"isRedirect"),se=Symbol("Response internals"),xe=class xe extends Ue{constructor(o=null,a={}){super(o,a);const u=a.status!=null?a.status:200,f=new ye(a.headers);if(o!==null&&!f.has("Content-Type")){const d=mi(o,this);d&&f.append("Content-Type",d)}this[se]={type:"default",url:a.url,status:u,statusText:a.statusText||"",headers:f,counter:a.counter,highWaterMark:a.highWaterMark}}get type(){return this[se].type}get url(){return this[se].url||""}get status(){return this[se].status}get ok(){return this[se].status>=200&&this[se].status<300}get redirected(){return this[se].counter>0}get statusText(){return this[se].statusText}get headers(){return this[se].headers}get highWaterMark(){return this[se].highWaterMark}clone(){return new xe(qn(this,this.highWaterMark),{type:this.type,url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected,size:this.size,highWaterMark:this.highWaterMark})}static redirect(o,a=302){if(!zn(a))throw new RangeError('Failed to execute "redirect" on "response": Invalid status code');return new xe(null,{headers:{location:new URL(o).toString()},status:a})}static error(){const o=new xe(null,{status:0,statusText:""});return o[se].type="error",o}static json(o=void 0,a={}){const u=JSON.stringify(o);if(u===void 0)throw new TypeError("data is not JSON serializable");const f=new ye(a&&a.headers);return f.has("content-type")||f.set("content-type","application/json"),new xe(u,{...a,headers:f})}get[Symbol.toStringTag](){return"Response"}};n(xe,"Response");let ue=xe;Object.defineProperties(ue.prototype,{type:{enumerable:!0},url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});const Xs=n(i=>{if(i.search)return i.search;const o=i.href.length-1,a=i.hash||(i.href[o]==="#"?"#":"");return i.href[o-a.length]==="?"?"?":""},"getSearch");function yi(i,o=!1){return i==null||(i=new URL(i),/^(about|blob|data):$/.test(i.protocol))?"no-referrer":(i.username="",i.password="",i.hash="",o&&(i.pathname="",i.search=""),i)}n(yi,"stripURLForUseAsAReferrer");const gi=new Set(["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"]),eu="strict-origin-when-cross-origin";function tu(i){if(!gi.has(i))throw new TypeError(`Invalid referrerPolicy: ${i}`);return i}n(tu,"validateReferrerPolicy");function ru(i){if(/^(http|ws)s:$/.test(i.protocol))return!0;const o=i.host.replace(/(^\[)|(]$)/g,""),a=ks(o);return a===4&&/^127\./.test(o)||a===6&&/^(((0+:){7})|(::(0+:){0,6}))0*1$/.test(o)?!0:i.host==="localhost"||i.host.endsWith(".localhost")?!1:i.protocol==="file:"}n(ru,"isOriginPotentiallyTrustworthy");function ct(i){return/^about:(blank|srcdoc)$/.test(i)||i.protocol==="data:"||/^(blob|filesystem):$/.test(i.protocol)?!0:ru(i)}n(ct,"isUrlPotentiallyTrustworthy");function nu(i,{referrerURLCallback:o,referrerOriginCallback:a}={}){if(i.referrer==="no-referrer"||i.referrerPolicy==="")return null;const u=i.referrerPolicy;if(i.referrer==="about:client")return"no-referrer";const f=i.referrer;let d=yi(f),b=yi(f,!0);d.toString().length>4096&&(d=b),o&&(d=o(d)),a&&(b=a(b));const p=new URL(i.url);switch(u){case"no-referrer":return"no-referrer";case"origin":return b;case"unsafe-url":return d;case"strict-origin":return ct(d)&&!ct(p)?"no-referrer":b.toString();case"strict-origin-when-cross-origin":return d.origin===p.origin?d:ct(d)&&!ct(p)?"no-referrer":b;case"same-origin":return d.origin===p.origin?d:"no-referrer";case"origin-when-cross-origin":return d.origin===p.origin?d:b;case"no-referrer-when-downgrade":return ct(d)&&!ct(p)?"no-referrer":d;default:throw new TypeError(`Invalid referrerPolicy: ${u}`)}}n(nu,"determineRequestsReferrer");function ou(i){const o=(i.get("referrer-policy")||"").split(/[,\s]+/);let a="";for(const u of o)u&&gi.has(u)&&(a=u);return a}n(ou,"parseReferrerPolicyFromHeader");const $=Symbol("Request internals"),Bt=n(i=>typeof i=="object"&&typeof i[$]=="object","isRequest"),iu=hr(()=>{},".data is not a valid RequestInit property, use .body instead","https://github.com/node-fetch/node-fetch/issues/1000 (request)"),vr=class vr extends Ue{constructor(o,a={}){let u;if(Bt(o)?u=new URL(o.url):(u=new URL(o),o={}),u.username!==""||u.password!=="")throw new TypeError(`${u} is an url with embedded credentials.`);let f=a.method||o.method||"GET";if(/^(delete|get|head|options|post|put)$/i.test(f)&&(f=f.toUpperCase()),!Bt(a)&&"data"in a&&iu(),(a.body!=null||Bt(o)&&o.body!==null)&&(f==="GET"||f==="HEAD"))throw new TypeError("Request with GET/HEAD method cannot have body");const d=a.body?a.body:Bt(o)&&o.body!==null?qn(o):null;super(d,{size:a.size||o.size||0});const b=new ye(a.headers||o.headers||{});if(d!==null&&!b.has("Content-Type")){const w=mi(d,this);w&&b.set("Content-Type",w)}let p=Bt(o)?o.signal:null;if("signal"in a&&(p=a.signal),p!=null&&!Ns(p))throw new TypeError("Expected signal to be an instanceof AbortSignal or EventTarget");let E=a.referrer==null?o.referrer:a.referrer;if(E==="")E="no-referrer";else if(E){const w=new URL(E);E=/^about:(\/\/)?client$/.test(w)?"client":w}else E=void 0;this[$]={method:f,redirect:a.redirect||o.redirect||"follow",headers:b,parsedURL:u,signal:p,referrer:E},this.follow=a.follow===void 0?o.follow===void 0?20:o.follow:a.follow,this.compress=a.compress===void 0?o.compress===void 0?!0:o.compress:a.compress,this.counter=a.counter||o.counter||0,this.agent=a.agent||o.agent,this.highWaterMark=a.highWaterMark||o.highWaterMark||16384,this.insecureHTTPParser=a.insecureHTTPParser||o.insecureHTTPParser||!1,this.referrerPolicy=a.referrerPolicy||o.referrerPolicy||""}get method(){return this[$].method}get url(){return Bs(this[$].parsedURL)}get headers(){return this[$].headers}get redirect(){return this[$].redirect}get signal(){return this[$].signal}get referrer(){if(this[$].referrer==="no-referrer")return"";if(this[$].referrer==="client")return"about:client";if(this[$].referrer)return this[$].referrer.toString()}get referrerPolicy(){return this[$].referrerPolicy}set referrerPolicy(o){this[$].referrerPolicy=tu(o)}clone(){return new vr(this)}get[Symbol.toStringTag](){return"Request"}};n(vr,"Request");let dt=vr;Object.defineProperties(dt.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0},referrer:{enumerable:!0},referrerPolicy:{enumerable:!0}});const au=n(i=>{const{parsedURL:o}=i[$],a=new ye(i[$].headers);a.has("Accept")||a.set("Accept","*/*");let u=null;if(i.body===null&&/^(post|put)$/i.test(i.method)&&(u="0"),i.body!==null){const p=Gs(i);typeof p=="number"&&!Number.isNaN(p)&&(u=String(p))}u&&a.set("Content-Length",u),i.referrerPolicy===""&&(i.referrerPolicy=eu),i.referrer&&i.referrer!=="no-referrer"?i[$].referrer=nu(i):i[$].referrer="no-referrer",i[$].referrer instanceof URL&&a.set("Referer",i.referrer),a.has("User-Agent")||a.set("User-Agent","node-fetch"),i.compress&&!a.has("Accept-Encoding")&&a.set("Accept-Encoding","gzip, deflate, br");let{agent:f}=i;typeof f=="function"&&(f=f(o));const d=Xs(o),b={path:o.pathname+d,method:i.method,headers:a[Symbol.for("nodejs.util.inspect.custom")](),insecureHTTPParser:i.insecureHTTPParser,agent:f};return{parsedURL:o,options:b}},"getNodeRequestOptions"),Mn=class Mn extends ft{constructor(o,a="aborted"){super(o,a)}};n(Mn,"AbortError");let _r=Mn;/*! node-domexception. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */if(!globalThis.DOMException)try{const{MessageChannel:i}=require("worker_threads"),o=new i().port1,a=new ArrayBuffer;o.postMessage(a,[a,a])}catch(i){i.constructor.name==="DOMException"&&(globalThis.DOMException=i.constructor)}var su=globalThis.DOMException;const uu=As(su),{stat:In}=qs,lu=n((i,o)=>_i(li(i),i,o),"blobFromSync"),fu=n((i,o)=>In(i).then(a=>_i(a,i,o)),"blobFrom"),cu=n((i,o)=>In(i).then(a=>Si(a,i,o)),"fileFrom"),du=n((i,o)=>Si(li(i),i,o),"fileFromSync"),_i=n((i,o,a="")=>new lt([new Sr({path:o,size:i.size,lastModified:i.mtimeMs,start:0})],{type:a}),"fromBlob"),Si=n((i,o,a="")=>new Bn([new Sr({path:o,size:i.size,lastModified:i.mtimeMs,start:0})],Os(o),{type:a,lastModified:i.mtimeMs}),"fromFile"),Er=class Er{constructor(o){be(this,Ne,void 0);be(this,He,void 0);X(this,Ne,o.path),X(this,He,o.start),this.size=o.size,this.lastModified=o.lastModified}slice(o,a){return new Er({path:O(this,Ne),lastModified:this.lastModified,size:a-o,start:O(this,He)+o})}async*stream(){const{mtimeMs:o}=await In(O(this,Ne));if(o>this.lastModified)throw new uu("The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.","NotReadableError");yield*Ws(O(this,Ne),{start:O(this,He),end:O(this,He)+this.size-1})}get[Symbol.toStringTag](){return"Blob"}};Ne=new WeakMap,He=new WeakMap,n(Er,"BlobDataItem");let Sr=Er;const hu=new Set(["data:","http:","https:"]);async function wi(i,o){return new Promise((a,u)=>{const f=new dt(i,o),{parsedURL:d,options:b}=au(f);if(!hu.has(d.protocol))throw new TypeError(`node-fetch cannot load ${i}. URL scheme "${d.protocol.replace(/:$/,"")}" is not supported.`);if(d.protocol==="data:"){const R=zs(f.url),q=new ue(R,{headers:{"Content-Type":R.typeFull}});a(q);return}const p=(d.protocol==="https:"?vs:Et).request,{signal:E}=f;let w=null;const D=n(()=>{const R=new _r("The operation was aborted.");u(R),f.body&&f.body instanceof me.Readable&&f.body.destroy(R),!(!w||!w.body)&&w.body.emit("error",R)},"abort");if(E&&E.aborted){D();return}const A=n(()=>{D(),m()},"abortAndFinalize"),S=p(d.toString(),b);E&&E.addEventListener("abort",A);const m=n(()=>{S.abort(),E&&E.removeEventListener("abort",A)},"finalize");S.on("error",R=>{u(new G(`request to ${f.url} failed, reason: ${R.message}`,"system",R)),m()}),pu(S,R=>{w&&w.body&&w.body.destroy(R)}),process.version<"v14"&&S.on("socket",R=>{let q;R.prependListener("end",()=>{q=R._eventsCount}),R.prependListener("close",F=>{if(w&&q<R._eventsCount&&!F){const Q=new Error("Premature close");Q.code="ERR_STREAM_PREMATURE_CLOSE",w.body.emit("error",Q)}})}),S.on("response",R=>{S.setTimeout(0);const q=Ks(R.rawHeaders);if(zn(R.statusCode)){const z=q.get("Location");let j=null;try{j=z===null?null:new URL(z,f.url)}catch{if(f.redirect!=="manual"){u(new G(`uri requested responds with an invalid redirect URL: ${z}`,"invalid-redirect")),m();return}}switch(f.redirect){case"error":u(new G(`uri requested responds with a redirect, redirect mode is set to error: ${f.url}`,"no-redirect")),m();return;case"manual":break;case"follow":{if(j===null)break;if(f.counter>=f.follow){u(new G(`maximum redirect reached at: ${f.url}`,"max-redirect")),m();return}const I={headers:new ye(f.headers),follow:f.follow,counter:f.counter+1,agent:f.agent,compress:f.compress,method:f.method,body:qn(f),signal:f.signal,size:f.size,referrer:f.referrer,referrerPolicy:f.referrerPolicy};if(!Hs(f.url,j)||!Vs(f.url,j))for(const U of["authorization","www-authenticate","cookie","cookie2"])I.headers.delete(U);if(R.statusCode!==303&&f.body&&o.body instanceof me.Readable){u(new G("Cannot follow redirect with body being a readable stream","unsupported-redirect")),m();return}(R.statusCode===303||(R.statusCode===301||R.statusCode===302)&&f.method==="POST")&&(I.method="GET",I.body=void 0,I.headers.delete("content-length"));const mt=ou(q);mt&&(I.referrerPolicy=mt),a(wi(new dt(j,I))),m();return}default:return u(new TypeError(`Redirect option '${f.redirect}' is not a valid value of RequestRedirect`))}}E&&R.once("end",()=>{E.removeEventListener("abort",A)});let F=ut(R,new cr,z=>{z&&u(z)});process.version<"v12.10"&&R.on("aborted",A);const Q={url:f.url,status:R.statusCode,statusText:R.statusMessage,headers:q,size:f.size,counter:f.counter,highWaterMark:f.highWaterMark},M=q.get("Content-Encoding");if(!f.compress||f.method==="HEAD"||M===null||R.statusCode===204||R.statusCode===304){w=new ue(F,Q),a(w);return}const ve={flush:st.Z_SYNC_FLUSH,finishFlush:st.Z_SYNC_FLUSH};if(M==="gzip"||M==="x-gzip"){F=ut(F,st.createGunzip(ve),z=>{z&&u(z)}),w=new ue(F,Q),a(w);return}if(M==="deflate"||M==="x-deflate"){const z=ut(R,new cr,j=>{j&&u(j)});z.once("data",j=>{(j[0]&15)===8?F=ut(F,st.createInflate(),I=>{I&&u(I)}):F=ut(F,st.createInflateRaw(),I=>{I&&u(I)}),w=new ue(F,Q),a(w)}),z.once("end",()=>{w||(w=new ue(F,Q),a(w))});return}if(M==="br"){F=ut(F,st.createBrotliDecompress(),z=>{z&&u(z)}),w=new ue(F,Q),a(w);return}w=new ue(F,Q),a(w)}),Zs(S,f).catch(u)})}n(wi,"fetch$1");function pu(i,o){const a=x.from(`0\r
\r
`);let u=!1,f=!1,d;i.on("response",b=>{const{headers:p}=b;u=p["transfer-encoding"]==="chunked"&&!p["content-length"]}),i.on("socket",b=>{const p=n(()=>{if(u&&!f){const w=new Error("Premature close");w.code="ERR_STREAM_PREMATURE_CLOSE",o(w)}},"onSocketClose"),E=n(w=>{f=x.compare(w.slice(-5),a)===0,!f&&d&&(f=x.compare(d.slice(-3),a.slice(0,3))===0&&x.compare(w.slice(-2),a.slice(3))===0),d=w},"onData");b.prependListener("close",p),b.on("data",E),i.on("close",()=>{b.removeListener("close",p),b.removeListener("data",E)})})}n(pu,"fixResponseChunkedTransferBadEnding");const Ri=new WeakMap,Fn=new WeakMap;function W(i){const o=Ri.get(i);return console.assert(o!=null,"'this' is expected an Event object, but got",i),o}n(W,"pd");function Ti(i){if(i.passiveListener!=null){typeof console<"u"&&typeof console.error=="function"&&console.error("Unable to preventDefault inside passive event listener invocation.",i.passiveListener);return}i.event.cancelable&&(i.canceled=!0,typeof i.event.preventDefault=="function"&&i.event.preventDefault())}n(Ti,"setCancelFlag");function ht(i,o){Ri.set(this,{eventTarget:i,event:o,eventPhase:2,currentTarget:i,canceled:!1,stopped:!1,immediateStopped:!1,passiveListener:null,timeStamp:o.timeStamp||Date.now()}),Object.defineProperty(this,"isTrusted",{value:!1,enumerable:!0});const a=Object.keys(o);for(let u=0;u<a.length;++u){const f=a[u];f in this||Object.defineProperty(this,f,Ci(f))}}n(ht,"Event"),ht.prototype={get type(){return W(this).event.type},get target(){return W(this).eventTarget},get currentTarget(){return W(this).currentTarget},composedPath(){const i=W(this).currentTarget;return i==null?[]:[i]},get NONE(){return 0},get CAPTURING_PHASE(){return 1},get AT_TARGET(){return 2},get BUBBLING_PHASE(){return 3},get eventPhase(){return W(this).eventPhase},stopPropagation(){const i=W(this);i.stopped=!0,typeof i.event.stopPropagation=="function"&&i.event.stopPropagation()},stopImmediatePropagation(){const i=W(this);i.stopped=!0,i.immediateStopped=!0,typeof i.event.stopImmediatePropagation=="function"&&i.event.stopImmediatePropagation()},get bubbles(){return!!W(this).event.bubbles},get cancelable(){return!!W(this).event.cancelable},preventDefault(){Ti(W(this))},get defaultPrevented(){return W(this).canceled},get composed(){return!!W(this).event.composed},get timeStamp(){return W(this).timeStamp},get srcElement(){return W(this).eventTarget},get cancelBubble(){return W(this).stopped},set cancelBubble(i){if(!i)return;const o=W(this);o.stopped=!0,typeof o.event.cancelBubble=="boolean"&&(o.event.cancelBubble=!0)},get returnValue(){return!W(this).canceled},set returnValue(i){i||Ti(W(this))},initEvent(){}},Object.defineProperty(ht.prototype,"constructor",{value:ht,configurable:!0,writable:!0}),typeof window<"u"&&typeof window.Event<"u"&&(Object.setPrototypeOf(ht.prototype,window.Event.prototype),Fn.set(window.Event.prototype,ht));function Ci(i){return{get(){return W(this).event[i]},set(o){W(this).event[i]=o},configurable:!0,enumerable:!0}}n(Ci,"defineRedirectDescriptor");function bu(i){return{value(){const o=W(this).event;return o[i].apply(o,arguments)},configurable:!0,enumerable:!0}}n(bu,"defineCallDescriptor");function mu(i,o){const a=Object.keys(o);if(a.length===0)return i;function u(f,d){i.call(this,f,d)}n(u,"CustomEvent"),u.prototype=Object.create(i.prototype,{constructor:{value:u,configurable:!0,writable:!0}});for(let f=0;f<a.length;++f){const d=a[f];if(!(d in i.prototype)){const p=typeof Object.getOwnPropertyDescriptor(o,d).value=="function";Object.defineProperty(u.prototype,d,p?bu(d):Ci(d))}}return u}n(mu,"defineWrapper");function Pi(i){if(i==null||i===Object.prototype)return ht;let o=Fn.get(i);return o==null&&(o=mu(Pi(Object.getPrototypeOf(i)),i),Fn.set(i,o)),o}n(Pi,"getWrapper");function yu(i,o){const a=Pi(Object.getPrototypeOf(o));return new a(i,o)}n(yu,"wrapEvent");function gu(i){return W(i).immediateStopped}n(gu,"isStopped");function _u(i,o){W(i).eventPhase=o}n(_u,"setEventPhase");function Su(i,o){W(i).currentTarget=o}n(Su,"setCurrentTarget");function vi(i,o){W(i).passiveListener=o}n(vi,"setPassiveListener");const Ei=new WeakMap,Ai=1,Bi=2,wr=3;function Rr(i){return i!==null&&typeof i=="object"}n(Rr,"isObject");function kt(i){const o=Ei.get(i);if(o==null)throw new TypeError("'this' is expected an EventTarget object, but got another value.");return o}n(kt,"getListeners");function wu(i){return{get(){let a=kt(this).get(i);for(;a!=null;){if(a.listenerType===wr)return a.listener;a=a.next}return null},set(o){typeof o!="function"&&!Rr(o)&&(o=null);const a=kt(this);let u=null,f=a.get(i);for(;f!=null;)f.listenerType===wr?u!==null?u.next=f.next:f.next!==null?a.set(i,f.next):a.delete(i):u=f,f=f.next;if(o!==null){const d={listener:o,listenerType:wr,passive:!1,once:!1,next:null};u===null?a.set(i,d):u.next=d}},configurable:!0,enumerable:!0}}n(wu,"defineEventAttributeDescriptor");function ki(i,o){Object.defineProperty(i,`on${o}`,wu(o))}n(ki,"defineEventAttribute");function Wi(i){function o(){Ce.call(this)}n(o,"CustomEventTarget"),o.prototype=Object.create(Ce.prototype,{constructor:{value:o,configurable:!0,writable:!0}});for(let a=0;a<i.length;++a)ki(o.prototype,i[a]);return o}n(Wi,"defineCustomEventTarget");function Ce(){if(this instanceof Ce){Ei.set(this,new Map);return}if(arguments.length===1&&Array.isArray(arguments[0]))return Wi(arguments[0]);if(arguments.length>0){const i=new Array(arguments.length);for(let o=0;o<arguments.length;++o)i[o]=arguments[o];return Wi(i)}throw new TypeError("Cannot call a class as a function")}n(Ce,"EventTarget"),Ce.prototype={addEventListener(i,o,a){if(o==null)return;if(typeof o!="function"&&!Rr(o))throw new TypeError("'listener' should be a function or an object.");const u=kt(this),f=Rr(a),b=(f?!!a.capture:!!a)?Ai:Bi,p={listener:o,listenerType:b,passive:f&&!!a.passive,once:f&&!!a.once,next:null};let E=u.get(i);if(E===void 0){u.set(i,p);return}let w=null;for(;E!=null;){if(E.listener===o&&E.listenerType===b)return;w=E,E=E.next}w.next=p},removeEventListener(i,o,a){if(o==null)return;const u=kt(this),d=(Rr(a)?!!a.capture:!!a)?Ai:Bi;let b=null,p=u.get(i);for(;p!=null;){if(p.listener===o&&p.listenerType===d){b!==null?b.next=p.next:p.next!==null?u.set(i,p.next):u.delete(i);return}b=p,p=p.next}},dispatchEvent(i){if(i==null||typeof i.type!="string")throw new TypeError('"event.type" should be a string.');const o=kt(this),a=i.type;let u=o.get(a);if(u==null)return!0;const f=yu(this,i);let d=null;for(;u!=null;){if(u.once?d!==null?d.next=u.next:u.next!==null?o.set(a,u.next):o.delete(a):d=u,vi(f,u.passive?u.listener:null),typeof u.listener=="function")try{u.listener.call(this,f)}catch(b){typeof console<"u"&&typeof console.error=="function"&&console.error(b)}else u.listenerType!==wr&&typeof u.listener.handleEvent=="function"&&u.listener.handleEvent(f);if(gu(f))break;u=u.next}return vi(f,null),_u(f,0),Su(f,null),!f.defaultPrevented}},Object.defineProperty(Ce.prototype,"constructor",{value:Ce,configurable:!0,writable:!0}),typeof window<"u"&&typeof window.EventTarget<"u"&&Object.setPrototypeOf(Ce.prototype,window.EventTarget.prototype);const Un=class Un extends Ce{constructor(){throw super(),new TypeError("AbortSignal cannot be constructed directly")}get aborted(){const o=Tr.get(this);if(typeof o!="boolean")throw new TypeError(`Expected 'this' to be an 'AbortSignal' object, but got ${this===null?"null":typeof this}`);return o}};n(Un,"AbortSignal");let pt=Un;ki(pt.prototype,"abort");function Ru(){const i=Object.create(pt.prototype);return Ce.call(i),Tr.set(i,!1),i}n(Ru,"createAbortSignal");function Tu(i){Tr.get(i)===!1&&(Tr.set(i,!0),i.dispatchEvent({type:"abort"}))}n(Tu,"abortSignal");const Tr=new WeakMap;Object.defineProperties(pt.prototype,{aborted:{enumerable:!0}}),typeof Symbol=="function"&&typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(pt.prototype,Symbol.toStringTag,{configurable:!0,value:"AbortSignal"});let jn=(Ft=class{constructor(){qi.set(this,Ru())}get signal(){return Oi(this)}abort(){Tu(Oi(this))}},n(Ft,"AbortController"),Ft);const qi=new WeakMap;function Oi(i){const o=qi.get(i);if(o==null)throw new TypeError(`Expected 'this' to be an 'AbortController' object, but got ${i===null?"null":typeof i}`);return o}n(Oi,"getSignal"),Object.defineProperties(jn.prototype,{signal:{enumerable:!0},abort:{enumerable:!0}}),typeof Symbol=="function"&&typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(jn.prototype,Symbol.toStringTag,{configurable:!0,value:"AbortController"});var Cu=Object.defineProperty,Pu=n((i,o)=>Cu(i,"name",{value:o,configurable:!0}),"e");const zi=wi;Ii();function Ii(){!globalThis.process?.versions?.node&&!globalThis.process?.env.DISABLE_NODE_FETCH_NATIVE_WARN&&console.warn("[node-fetch-native] Node.js compatible build of `node-fetch-native` is being used in a non-Node.js environment. Please make sure you are using proper export conditions or report this issue to https://github.com/unjs/node-fetch-native. You can set `process.env.DISABLE_NODE_FETCH_NATIVE_WARN` to disable this warning.")}n(Ii,"s"),Pu(Ii,"checkNodeEnvironment");export{jn as AbortController,_r as AbortError,lt as Blob,G as FetchError,Bn as File,br as FormData,ye as Headers,dt as Request,ue as Response,fu as blobFrom,lu as blobFromSync,zi as default,zi as fetch,cu as fileFrom,du as fileFromSync,zn as isRedirect};
