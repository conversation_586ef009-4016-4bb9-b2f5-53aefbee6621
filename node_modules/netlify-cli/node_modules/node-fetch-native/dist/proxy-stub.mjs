var n=Object.defineProperty;var e=(t,r)=>n(t,"name",{value:r,configurable:!0});var i=Object.defineProperty,c=e((t,r)=>i(t,"name",{value:r,configurable:!0}),"e");function a(){return{agent:void 0,dispatcher:void 0}}e(a,"createProxy"),c(a,"createProxy");function o(){return globalThis.fetch}e(o,"createFetch"),c(o,"createFetch");const h=globalThis.fetch;export{o as createFetch,a as createProxy,h as fetch};
