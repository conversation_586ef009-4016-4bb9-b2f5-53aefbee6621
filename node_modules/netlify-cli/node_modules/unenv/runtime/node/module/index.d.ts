/// <reference types="node" />
import type nodeModule from "node:module";
export declare const builtinModules: typeof nodeModule.builtinModules;
export declare const createRequire: any;
export declare const runMain: any;
export declare const isBuiltin: any;
export declare const register: any;
export declare const syncBuiltinESMExports: any;
export declare const findSourceMap: any;
export declare const wrap: any;
export declare const Module: any;
export declare const SourceMap: any;
declare const _default: any;
export default _default;
