export declare const URL: any;
export declare const URLSearchParams: {
    new (init?: string | URLSearchParams | Record<string, string> | string[][] | undefined): URLSearchParams;
    prototype: URLSearchParams;
};
export declare const parse: any;
export declare const resolve: any;
export declare const urlToHttpOptions: any;
export declare const format: any;
export declare const domainToASCII: any;
export declare const domainToUnicode: any;
export declare const pathToFileURL: any;
export declare const fileURLToPath: any;
declare const _default: any;
export default _default;
