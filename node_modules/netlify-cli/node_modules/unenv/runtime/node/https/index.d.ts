/// <reference types="node" />
import type nodeHttps from "node:https";
export declare const Server: typeof nodeHttps.Server;
export declare const Agent: typeof nodeHttps.Agent;
export declare const globalAgent: any;
export declare const get: typeof nodeHttps.get;
export declare const createServer: typeof nodeHttps.createServer;
export declare const request: typeof nodeHttps.request;
declare const _default: any;
export default _default;
