/// <reference types="node" />
import type nodeCrypto from "node:crypto";
export declare const webcrypto: any;
export declare const randomBytes: typeof nodeCrypto.randomBytes;
export declare const fips: typeof nodeCrypto.fips;
export declare const constants: any;
export declare const checkPrime: typeof nodeCrypto.checkPrime;
export declare const checkPrimeSync: typeof nodeCrypto.checkPrimeSync;
export declare const createCipher: typeof nodeCrypto.createCipher;
export declare const createDecipher: typeof nodeCrypto.createDecipher;
export declare const pseudoRandomBytes: typeof nodeCrypto.pseudoRandomBytes;
export declare const createCipheriv: typeof nodeCrypto.createCipheriv;
export declare const createDecipheriv: typeof nodeCrypto.createDecipheriv;
export declare const createDiffieHellman: typeof nodeCrypto.createDiffieHellman;
export declare const createDiffieHellmanGroup: typeof nodeCrypto.createDiffieHellmanGroup;
export declare const createECDH: typeof nodeCrypto.createECDH;
export declare const createHash: typeof nodeCrypto.createHash;
export declare const createHmac: typeof nodeCrypto.createHmac;
export declare const createPrivateKey: typeof nodeCrypto.createPrivateKey;
export declare const createPublicKey: typeof nodeCrypto.createPublicKey;
export declare const createSecretKey: typeof nodeCrypto.createSecretKey;
export declare const createSign: typeof nodeCrypto.createSign;
export declare const createVerify: typeof nodeCrypto.createVerify;
export declare const diffieHellman: typeof nodeCrypto.diffieHellman;
export declare const generatePrime: typeof nodeCrypto.generatePrime;
export declare const generatePrimeSync: typeof nodeCrypto.generatePrimeSync;
export declare const getCiphers: typeof nodeCrypto.getCiphers;
export declare const getCipherInfo: typeof nodeCrypto.getCipherInfo;
export declare const getCurves: typeof nodeCrypto.getCurves;
export declare const getDiffieHellman: typeof nodeCrypto.getDiffieHellman;
export declare const getHashes: typeof nodeCrypto.getHashes;
export declare const hkdf: typeof nodeCrypto.hkdf;
export declare const hkdfSync: typeof nodeCrypto.hkdfSync;
export declare const pbkdf2: typeof nodeCrypto.pbkdf2;
export declare const pbkdf2Sync: typeof nodeCrypto.pbkdf2Sync;
export declare const generateKeyPair: typeof nodeCrypto.generateKeyPair;
export declare const generateKeyPairSync: typeof nodeCrypto.generateKeyPairSync;
export declare const generateKey: typeof nodeCrypto.generateKey;
export declare const generateKeySync: typeof nodeCrypto.generateKeySync;
export declare const privateDecrypt: typeof nodeCrypto.privateDecrypt;
export declare const privateEncrypt: typeof nodeCrypto.privateEncrypt;
export declare const publicDecrypt: typeof nodeCrypto.publicDecrypt;
export declare const publicEncrypt: typeof nodeCrypto.publicEncrypt;
export declare const randomFill: typeof nodeCrypto.randomFill;
export declare const randomFillSync: typeof nodeCrypto.randomFillSync;
export declare const randomInt: typeof nodeCrypto.randomInt;
export declare const scrypt: typeof nodeCrypto.scrypt;
export declare const scryptSync: typeof nodeCrypto.scryptSync;
export declare const sign: typeof nodeCrypto.sign;
export declare const setEngine: typeof nodeCrypto.setEngine;
export declare const timingSafeEqual: typeof nodeCrypto.timingSafeEqual;
export declare const getFips: typeof nodeCrypto.getFips;
export declare const setFips: typeof nodeCrypto.setFips;
export declare const verify: typeof nodeCrypto.verify;
export declare const secureHeapUsed: typeof nodeCrypto.secureHeapUsed;
export declare const Certificate: any;
export declare const Cipher: any;
export declare const Cipheriv: any;
export declare const Decipher: any;
export declare const Decipheriv: any;
export declare const DiffieHellman: any;
export declare const DiffieHellmanGroup: any;
export declare const ECDH: any;
export declare const Hash: any;
export declare const Hmac: any;
export declare const KeyObject: any;
export declare const Sign: any;
export declare const Verify: any;
export declare const X509Certificate: any;
