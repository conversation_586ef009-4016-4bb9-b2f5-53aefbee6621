{"version": 3, "file": "moize.min.js", "sources": ["../src/constants.ts", "../src/utils.ts", "../src/maxAge.ts", "../src/stats.ts", "../src/instance.ts", "../src/component.ts", "../src/serialize.ts", "../src/options.ts", "../src/maxArgs.ts", "../src/index.ts", "../src/updateCacheForKey.ts"], "sourcesContent": ["import type { Options } from '../index.d';\n\n/**\n * @private\n *\n * @constant DEFAULT_OPTIONS\n */\nexport const DEFAULT_OPTIONS: Options = {\n    isDeepEqual: false,\n    isPromise: false,\n    isReact: false,\n    isSerialized: false,\n    isShallowEqual: false,\n    matchesArg: undefined,\n    matchesKey: undefined,\n    maxAge: undefined,\n    maxArgs: undefined,\n    maxSize: 1,\n    onExpire: undefined,\n    profileName: undefined,\n    serializer: undefined,\n    updateCacheForKey: undefined,\n    transformArgs: undefined,\n    updateExpire: false,\n};\n", "import { DEFAULT_OPTIONS } from './constants';\n\nimport type {\n    Expiration,\n    Fn,\n    <PERSON>E<PERSON>l,\n    IsMatching<PERSON>ey,\n    Key,\n    Moizeable,\n    Moized,\n    Options,\n} from '../index.d';\n\n/**\n * @private\n *\n * @description\n * method to combine functions and return a single function that fires them all\n *\n * @param functions the functions to compose\n * @returns the composed function\n */\nexport function combine<Arg, Result>(\n    ...functions: Fn<Arg>[]\n): Fn<Arg, Result> | undefined {\n    return functions.reduce(function (f: any, g: any) {\n        if (typeof f === 'function') {\n            return typeof g === 'function'\n                ? function (this: any) {\n                      f.apply(this, arguments);\n                      g.apply(this, arguments);\n                  }\n                : f;\n        }\n\n        if (typeof g === 'function') {\n            return g;\n        }\n    });\n}\n\n/**\n * @private\n *\n * @description\n * method to compose functions and return a single function\n *\n * @param functions the functions to compose\n * @returns the composed function\n */\nexport function compose<Method>(...functions: Method[]): Method {\n    return functions.reduce(function (f: any, g: any) {\n        if (typeof f === 'function') {\n            return typeof g === 'function'\n                ? function (this: any) {\n                      return f(g.apply(this, arguments));\n                  }\n                : f;\n        }\n\n        if (typeof g === 'function') {\n            return g;\n        }\n    });\n}\n\n/**\n * @private\n *\n * @description\n * find the index of the expiration based on the key\n *\n * @param expirations the list of expirations\n * @param key the key to match\n * @returns the index of the expiration\n */\nexport function findExpirationIndex(expirations: Expiration[], key: Key) {\n    for (let index = 0; index < expirations.length; index++) {\n        if (expirations[index].key === key) {\n            return index;\n        }\n    }\n\n    return -1;\n}\n\n/**\n * @private\n *\n * @description\n * create function that finds the index of the key in the list of cache keys\n *\n * @param isEqual the function to test individual argument equality\n * @param isMatchingKey the function to test full key equality\n * @returns the function that finds the index of the key\n */\nexport function createFindKeyIndex(\n    isEqual: IsEqual,\n    isMatchingKey: IsMatchingKey | undefined\n) {\n    const areKeysEqual: IsMatchingKey =\n        typeof isMatchingKey === 'function'\n            ? isMatchingKey\n            : function (cacheKey: Key, key: Key) {\n                  for (let index = 0; index < key.length; index++) {\n                      if (!isEqual(cacheKey[index], key[index])) {\n                          return false;\n                      }\n                  }\n\n                  return true;\n              };\n\n    return function (keys: Key[], key: Key) {\n        for (let keysIndex = 0; keysIndex < keys.length; keysIndex++) {\n            if (\n                keys[keysIndex].length === key.length &&\n                areKeysEqual(keys[keysIndex], key)\n            ) {\n                return keysIndex;\n            }\n        }\n\n        return -1;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * merge two options objects, combining or composing functions as necessary\n *\n * @param originalOptions the options that already exist on the method\n * @param newOptions the new options to merge\n * @returns the merged options\n */\nexport function mergeOptions(\n    originalOptions: Options,\n    newOptions: Options\n): Options {\n    return !newOptions || newOptions === DEFAULT_OPTIONS\n        ? originalOptions\n        : {\n              ...originalOptions,\n              ...newOptions,\n              onCacheAdd: combine(\n                  originalOptions.onCacheAdd,\n                  newOptions.onCacheAdd\n              ),\n              onCacheChange: combine(\n                  originalOptions.onCacheChange,\n                  newOptions.onCacheChange\n              ),\n              onCacheHit: combine(\n                  originalOptions.onCacheHit,\n                  newOptions.onCacheHit\n              ),\n              transformArgs: compose(\n                  originalOptions.transformArgs,\n                  newOptions.transformArgs\n              ),\n          };\n}\n\nexport function isMoized(fn: Moizeable | Moized | Options): fn is Moized {\n    return typeof fn === 'function' && (fn as Moizeable).isMoized;\n}\n\nexport function setName(\n    fn: Moized,\n    originalFunctionName: string,\n    profileName: string\n) {\n    try {\n        const name = profileName || originalFunctionName || 'anonymous';\n\n        Object.defineProperty(fn, 'name', {\n            configurable: true,\n            enumerable: false,\n            value: `moized(${name})`,\n            writable: true,\n        });\n    } catch {\n        // For engines where `function.name` is not configurable, do nothing.\n    }\n}\n", "import { createFindKeyIndex, findExpirationIndex } from './utils';\n\nimport type {\n    Cache,\n    Expiration,\n    Fn,\n    IsEqual,\n    IsMatching<PERSON>ey,\n    Key,\n    OnCacheOperation,\n    Options,\n} from '../index.d';\n\n/**\n * @private\n *\n * @description\n * clear an active expiration and remove it from the list if applicable\n *\n * @param expirations the list of expirations\n * @param key the key to clear\n * @param shouldRemove should the expiration be removed from the list\n */\nexport function clearExpiration(\n    expirations: Expiration[],\n    key: Key,\n    shouldRemove?: boolean\n) {\n    const expirationIndex = findExpirationIndex(expirations, key);\n\n    if (expirationIndex !== -1) {\n        clearTimeout(expirations[expirationIndex].timeoutId);\n\n        if (shouldRemove) {\n            expirations.splice(expirationIndex, 1);\n        }\n    }\n}\n\n/**\n * @private\n *\n * @description\n * Create the timeout for the given expiration method. If the ability to `unref`\n * exists, then apply it to avoid process locks in NodeJS.\n *\n * @param expirationMethod the method to fire upon expiration\n * @param maxAge the time to expire after\n * @returns the timeout ID\n */\nexport function createTimeout(expirationMethod: () => void, maxAge: number) {\n    const timeoutId = setTimeout(expirationMethod, maxAge);\n\n    if (typeof timeoutId.unref === 'function') {\n        timeoutId.unref();\n    }\n\n    return timeoutId;\n}\n\n/**\n * @private\n *\n * @description\n * create a function that, when an item is added to the cache, adds an expiration for it\n *\n * @param expirations the mutable expirations array\n * @param options the options passed on initialization\n * @param isEqual the function to check argument equality\n * @param isMatchingKey the function to check complete key equality\n * @returns the onCacheAdd function to handle expirations\n */\nexport function createOnCacheAddSetExpiration(\n    expirations: Expiration[],\n    options: Options,\n    isEqual: IsEqual,\n    isMatchingKey: IsMatchingKey\n): OnCacheOperation {\n    const { maxAge } = options;\n\n    return function onCacheAdd(\n        cache: Cache,\n        moizedOptions: Options,\n        moized: Fn\n    ) {\n        const key: any = cache.keys[0];\n\n        if (findExpirationIndex(expirations, key) === -1) {\n            const expirationMethod = function () {\n                const findKeyIndex = createFindKeyIndex(isEqual, isMatchingKey);\n\n                const keyIndex: number = findKeyIndex(cache.keys, key);\n                const value: any = cache.values[keyIndex];\n\n                if (~keyIndex) {\n                    cache.keys.splice(keyIndex, 1);\n                    cache.values.splice(keyIndex, 1);\n\n                    if (typeof options.onCacheChange === 'function') {\n                        options.onCacheChange(cache, moizedOptions, moized);\n                    }\n                }\n\n                clearExpiration(expirations, key, true);\n\n                if (\n                    typeof options.onExpire === 'function' &&\n                    options.onExpire(key) === false\n                ) {\n                    cache.keys.unshift(key);\n                    cache.values.unshift(value);\n\n                    onCacheAdd(cache, moizedOptions, moized);\n\n                    if (typeof options.onCacheChange === 'function') {\n                        options.onCacheChange(cache, moizedOptions, moized);\n                    }\n                }\n            };\n\n            expirations.push({\n                expirationMethod,\n                key,\n                timeoutId: createTimeout(expirationMethod, maxAge),\n            });\n        }\n    };\n}\n\n/**\n * @private\n *\n * @description\n * creates a function that, when a cache item is hit, reset the expiration\n *\n * @param expirations the mutable expirations array\n * @param options the options passed on initialization\n * @returns the onCacheAdd function to handle expirations\n */\nexport function createOnCacheHitResetExpiration(\n    expirations: Expiration[],\n    options: Options\n): OnCacheOperation {\n    return function onCacheHit(cache: Cache) {\n        const key = cache.keys[0];\n        const expirationIndex = findExpirationIndex(expirations, key);\n\n        if (~expirationIndex) {\n            clearExpiration(expirations, key, false);\n\n            expirations[expirationIndex].timeoutId = createTimeout(\n                expirations[expirationIndex].expirationMethod,\n                options.maxAge\n            );\n        }\n    };\n}\n\n/**\n * @private\n *\n * @description\n * get the micro-memoize options specific to the maxAge option\n *\n * @param expirations the expirations for the memoized function\n * @param options the options passed to the moizer\n * @param isEqual the function to test equality of the key on a per-argument basis\n * @param isMatchingKey the function to test equality of the whole key\n * @returns the object of options based on the entries passed\n */\nexport function getMaxAgeOptions(\n    expirations: Expiration[],\n    options: Options,\n    isEqual: IsEqual,\n    isMatchingKey: IsMatchingKey\n): {\n    onCacheAdd: OnCacheOperation | undefined;\n    onCacheHit: OnCacheOperation | undefined;\n} {\n    const onCacheAdd =\n        typeof options.maxAge === 'number' && isFinite(options.maxAge)\n            ? createOnCacheAddSetExpiration(\n                  expirations,\n                  options,\n                  isEqual,\n                  isMatchingKey\n              )\n            : undefined;\n\n    return {\n        onCacheAdd,\n        onCacheHit:\n            onCacheAdd && options.updateExpire\n                ? createOnCacheHitResetExpiration(expirations, options)\n                : undefined,\n    };\n}\n", "import type {\n    Fn,\n    FunctionalComponent,\n    GlobalStatsObject,\n    OnCacheOperation,\n    Options,\n    StatsCache,\n    StatsProfile,\n} from '../index.d';\n\nexport const statsCache: StatsCache = {\n    anonymousProfileNameCounter: 1,\n    isCollectingStats: false,\n    profiles: {},\n};\n\nlet hasWarningDisplayed = false;\n\nexport function clearStats(profileName?: string) {\n    if (profileName) {\n        delete statsCache.profiles[profileName];\n    } else {\n        statsCache.profiles = {};\n    }\n}\n\n/**\n * @private\n *\n * @description\n * activate stats collection\n *\n * @param isCollectingStats should stats be collected\n */\nexport function collectStats(isCollectingStats = true) {\n    statsCache.isCollectingStats = isCollectingStats;\n}\n\n/**\n * @private\n *\n * @description\n * create a function that increments the number of calls for the specific profile\n */\nexport function createOnCacheAddIncrementCalls(options: Options) {\n    const { profileName } = options;\n\n    return function () {\n        if (profileName && !statsCache.profiles[profileName]) {\n            statsCache.profiles[profileName] = {\n                calls: 0,\n                hits: 0,\n            };\n        }\n\n        statsCache.profiles[profileName].calls++;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * create a function that increments the number of calls and cache hits for the specific profile\n */\nexport function createOnCacheHitIncrementCallsAndHits(options: Options) {\n    return function () {\n        const { profiles } = statsCache;\n        const { profileName } = options;\n\n        if (!profiles[profileName]) {\n            profiles[profileName] = {\n                calls: 0,\n                hits: 0,\n            };\n        }\n\n        profiles[profileName].calls++;\n        profiles[profileName].hits++;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * get the profileName for the function when one is not provided\n *\n * @param fn the function to be memoized\n * @returns the derived profileName for the function\n */\nexport function getDefaultProfileName(\n    fn: Fn | FunctionalComponent<Record<string, unknown>>\n) {\n    return (\n        (fn as FunctionalComponent<Record<string, unknown>>).displayName ||\n        fn.name ||\n        `Anonymous ${statsCache.anonymousProfileNameCounter++}`\n    );\n}\n\n/**\n * @private\n *\n * @description\n * get the usage percentage based on the number of hits and total calls\n *\n * @param calls the number of calls made\n * @param hits the number of cache hits when called\n * @returns the usage as a percentage string\n */\nexport function getUsagePercentage(calls: number, hits: number) {\n    return calls ? `${((hits / calls) * 100).toFixed(4)}%` : '0.0000%';\n}\n\n/**\n * @private\n *\n * @description\n * get the statistics for a given method or all methods\n *\n * @param [profileName] the profileName to get the statistics for (get all when not provided)\n * @returns the object with stats information\n */\nexport function getStats(profileName?: string): GlobalStatsObject {\n    if (!statsCache.isCollectingStats && !hasWarningDisplayed) {\n        console.warn(\n            'Stats are not currently being collected, please run \"collectStats\" to enable them.'\n        ); // eslint-disable-line no-console\n\n        hasWarningDisplayed = true;\n    }\n\n    const { profiles } = statsCache;\n\n    if (profileName) {\n        if (!profiles[profileName]) {\n            return {\n                calls: 0,\n                hits: 0,\n                usage: '0.0000%',\n            };\n        }\n\n        const { [profileName]: profile } = profiles;\n\n        return {\n            ...profile,\n            usage: getUsagePercentage(profile.calls, profile.hits),\n        };\n    }\n\n    const completeStats: StatsProfile = Object.keys(statsCache.profiles).reduce(\n        (completeProfiles, profileName) => {\n            completeProfiles.calls += profiles[profileName].calls;\n            completeProfiles.hits += profiles[profileName].hits;\n\n            return completeProfiles;\n        },\n        {\n            calls: 0,\n            hits: 0,\n        }\n    );\n\n    return {\n        ...completeStats,\n        profiles: Object.keys(profiles).reduce(\n            (computedProfiles, profileName) => {\n                computedProfiles[profileName] = getStats(profileName);\n\n                return computedProfiles;\n            },\n            {} as Record<string, StatsProfile>\n        ),\n        usage: getUsagePercentage(completeStats.calls, completeStats.hits),\n    };\n}\n\n/**\n * @private\n *\n * @function getStatsOptions\n *\n * @description\n * get the options specific to storing statistics\n *\n * @param {Options} options the options passed to the moizer\n * @returns {Object} the options specific to keeping stats\n */\nexport function getStatsOptions(options: Options): {\n    onCacheAdd?: OnCacheOperation;\n    onCacheHit?: OnCacheOperation;\n} {\n    return statsCache.isCollectingStats\n        ? {\n              onCacheAdd: createOnCacheAddIncrementCalls(options),\n              onCacheHit: createOnCacheHitIncrementCallsAndHits(options),\n          }\n        : {};\n}\n", "import { clearExpiration } from './maxAge';\nimport { clearStats, getStats } from './stats';\nimport { createFindKeyIndex } from './utils';\n\nimport type {\n    Fn,\n    Key,\n    Memoized,\n    Moizeable,\n    MoizeConfiguration,\n    Moized,\n    Options,\n    StatsProfile,\n} from '../index.d';\n\nconst ALWAYS_SKIPPED_PROPERTIES: Record<string, boolean> = {\n    arguments: true,\n    callee: true,\n    caller: true,\n    constructor: true,\n    length: true,\n    name: true,\n    prototype: true,\n};\n\n/**\n * @private\n *\n * @description\n * copy the static properties from the original function to the moized\n * function\n *\n * @param originalFn the function copying from\n * @param newFn the function copying to\n * @param skippedProperties the list of skipped properties, if any\n */\nexport function copyStaticProperties(\n    originalFn: Fn,\n    newFn: Fn,\n    skippedProperties: string[] = []\n) {\n    Object.getOwnPropertyNames(originalFn).forEach((property) => {\n        if (\n            !ALWAYS_SKIPPED_PROPERTIES[property] &&\n            skippedProperties.indexOf(property) === -1\n        ) {\n            const descriptor = Object.getOwnPropertyDescriptor(\n                originalFn,\n                property\n            );\n\n            if (descriptor.get || descriptor.set) {\n                Object.defineProperty(newFn, property, descriptor);\n            } else {\n                newFn[property as keyof typeof newFn] =\n                    originalFn[property as keyof typeof originalFn];\n            }\n        }\n    });\n}\n\n/**\n * @private\n *\n * @description\n * add methods to the moized fuction object that allow extra features\n *\n * @param memoized the memoized function from micro-memoize\n */\nexport function addInstanceMethods<OriginalFn extends Fn>(\n    memoized: Moizeable,\n    { expirations }: MoizeConfiguration<OriginalFn>\n) {\n    const { options } = memoized;\n\n    const findKeyIndex = createFindKeyIndex(\n        options.isEqual,\n        options.isMatchingKey\n    );\n\n    const moized = memoized as unknown as Moized<OriginalFn, Options>;\n\n    moized.clear = function () {\n        const {\n            _microMemoizeOptions: { onCacheChange },\n            cache,\n        } = moized;\n\n        cache.keys.length = 0;\n        cache.values.length = 0;\n\n        if (onCacheChange) {\n            onCacheChange(cache, moized.options, moized);\n        }\n\n        return true;\n    };\n\n    moized.clearStats = function () {\n        clearStats(moized.options.profileName);\n    };\n\n    moized.get = function (key: Key) {\n        const {\n            _microMemoizeOptions: { transformKey },\n            cache,\n        } = moized;\n\n        const cacheKey = transformKey ? transformKey(key) : key;\n        const keyIndex = findKeyIndex(cache.keys, cacheKey);\n\n        return keyIndex !== -1 ? moized.apply(this, key) : undefined;\n    };\n\n    moized.getStats = function (): StatsProfile {\n        return getStats(moized.options.profileName);\n    };\n\n    moized.has = function (key: Key) {\n        const { transformKey } = moized._microMemoizeOptions;\n\n        const cacheKey = transformKey ? transformKey(key) : key;\n\n        return findKeyIndex(moized.cache.keys, cacheKey) !== -1;\n    };\n\n    moized.keys = function () {\n        return moized.cacheSnapshot.keys;\n    };\n\n    moized.remove = function (key: Key) {\n        const {\n            _microMemoizeOptions: { onCacheChange, transformKey },\n            cache,\n        } = moized;\n\n        const keyIndex = findKeyIndex(\n            cache.keys,\n            transformKey ? transformKey(key) : key\n        );\n\n        if (keyIndex === -1) {\n            return false;\n        }\n\n        const existingKey = cache.keys[keyIndex];\n\n        cache.keys.splice(keyIndex, 1);\n        cache.values.splice(keyIndex, 1);\n\n        if (onCacheChange) {\n            onCacheChange(cache, moized.options, moized);\n        }\n\n        clearExpiration(expirations, existingKey, true);\n\n        return true;\n    };\n\n    moized.set = function (key: Key, value: any) {\n        const { _microMemoizeOptions, cache, options } = moized;\n        const { onCacheAdd, onCacheChange, transformKey } =\n            _microMemoizeOptions;\n\n        const cacheKey = transformKey ? transformKey(key) : key;\n        const keyIndex = findKeyIndex(cache.keys, cacheKey);\n\n        if (keyIndex === -1) {\n            const cutoff = options.maxSize - 1;\n\n            if (cache.size > cutoff) {\n                cache.keys.length = cutoff;\n                cache.values.length = cutoff;\n            }\n\n            cache.keys.unshift(cacheKey);\n            cache.values.unshift(value);\n\n            if (options.isPromise) {\n                cache.updateAsyncCache(moized);\n            }\n\n            if (onCacheAdd) {\n                onCacheAdd(cache, options, moized);\n            }\n\n            if (onCacheChange) {\n                onCacheChange(cache, options, moized);\n            }\n        } else {\n            const existingKey = cache.keys[keyIndex];\n\n            cache.values[keyIndex] = value;\n\n            if (keyIndex > 0) {\n                cache.orderByLru(existingKey, value, keyIndex);\n            }\n\n            if (options.isPromise) {\n                cache.updateAsyncCache(moized);\n            }\n\n            if (typeof onCacheChange === 'function') {\n                onCacheChange(cache, options, moized);\n            }\n        }\n    };\n\n    moized.values = function () {\n        return moized.cacheSnapshot.values;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * add propeties to the moized fuction object that surfaces extra information\n *\n * @param memoized the memoized function\n * @param expirations the list of expirations for cache items\n * @param options the options passed to the moizer\n * @param originalFunction the function that is being memoized\n */\nexport function addInstanceProperties<OriginalFn extends Moizeable>(\n    memoized: Memoized<OriginalFn>,\n    {\n        expirations,\n        options: moizeOptions,\n        originalFunction,\n    }: MoizeConfiguration<OriginalFn>\n) {\n    const { options: microMemoizeOptions } = memoized;\n\n    Object.defineProperties(memoized, {\n        _microMemoizeOptions: {\n            configurable: true,\n            get() {\n                return microMemoizeOptions;\n            },\n        },\n\n        cacheSnapshot: {\n            configurable: true,\n            get() {\n                const { cache: currentCache } = memoized;\n\n                return {\n                    keys: currentCache.keys.slice(0),\n                    size: currentCache.size,\n                    values: currentCache.values.slice(0),\n                };\n            },\n        },\n\n        expirations: {\n            configurable: true,\n            get() {\n                return expirations;\n            },\n        },\n\n        expirationsSnapshot: {\n            configurable: true,\n            get() {\n                return expirations.slice(0);\n            },\n        },\n\n        isMoized: {\n            configurable: true,\n            get() {\n                return true;\n            },\n        },\n\n        options: {\n            configurable: true,\n            get() {\n                return moizeOptions;\n            },\n        },\n\n        originalFunction: {\n            configurable: true,\n            get() {\n                return originalFunction;\n            },\n        },\n    });\n\n    const moized = memoized as unknown as Moized<OriginalFn, Options>;\n\n    copyStaticProperties(originalFunction, moized);\n}\n\n/**\n * @private\n *\n * @description\n * add methods and properties to the memoized function for more features\n *\n * @param memoized the memoized function\n * @param configuration the configuration object for the instance\n * @returns the memoized function passed\n */\nexport function createMoizeInstance<\n    OriginalFn extends Moizeable,\n    CombinedOptions extends Options\n>(\n    memoized: Memoized<OriginalFn>,\n    configuration: MoizeConfiguration<OriginalFn>\n) {\n    addInstanceMethods<OriginalFn>(memoized, configuration);\n    addInstanceProperties<OriginalFn>(memoized, configuration);\n\n    return memoized as Moized<OriginalFn, CombinedOptions>;\n}\n", "import { copyStaticProperties } from './instance';\nimport { setName } from './utils';\n\nimport type {\n    Moize,\n    Moized as MoizedFunction,\n    Moizeable,\n    Options,\n} from '../index.d';\n\n// This was stolen from React internals, which allows us to create React elements without needing\n// a dependency on the React library itself.\nconst REACT_ELEMENT_TYPE =\n    typeof Symbol === 'function' && Symbol.for\n        ? Symbol.for('react.element')\n        : 0xeac7;\n\n/**\n * @private\n *\n * @description\n * Create a component that memoizes based on `props` and legacy `context`\n * on a per-instance basis. This requires creating a component class to\n * store the memoized function. The cost is quite low, and avoids the\n * need to have access to the React dependency by basically re-creating\n * the basic essentials for a component class and the results of the\n * `createElement` function.\n *\n * @param moizer the top-level moize method\n * @param fn the component to memoize\n * @param options the memoization options\n * @returns the memoized component\n */\nexport function createMoizedComponent<OriginalFn extends Moizeable>(\n    moizer: Moize,\n    fn: OriginalFn,\n    options: Options\n) {\n    /**\n     * This is a hack override setting the necessary options\n     * for a React component to be memoized. In the main `moize`\n     * method, if the `isReact` option is set it is short-circuited\n     * to call this function, and these overrides allow the\n     * necessary transformKey method to be derived.\n     *\n     * The order is based on:\n     * 1) Set the necessary aspects of transformKey for React components.\n     * 2) Allow setting of other options and overrides of those aspects\n     *    if desired (for example, `isDeepEqual` will use deep equality).\n     * 3) Always set `isReact` to false to prevent infinite loop.\n     */\n    const reactMoizer = moizer({\n        maxArgs: 2,\n        isShallowEqual: true,\n        ...options,\n        isReact: false,\n    });\n\n    if (!fn.displayName) {\n        // @ts-ignore - allow setting of displayName\n        fn.displayName = fn.name || 'Component';\n    }\n\n    function Moized<Props extends Record<string, unknown>, Context, Updater>(\n        this: any,\n        props: Props,\n        context: Context,\n        updater: Updater\n    ) {\n        this.props = props;\n        this.context = context;\n        this.updater = updater;\n\n        this.MoizedComponent = reactMoizer(fn);\n    }\n\n    Moized.prototype.isReactComponent = {};\n\n    Moized.prototype.render = function () {\n        return {\n            $$typeof: REACT_ELEMENT_TYPE,\n            type: this.MoizedComponent,\n            props: this.props,\n            ref: null,\n            key: null,\n            _owner: null,\n        } as JSX.Element;\n    };\n\n    copyStaticProperties(fn, Moized, ['contextType', 'contextTypes']);\n\n    Moized.displayName = `Moized(${fn.displayName || fn.name || 'Component'})`;\n\n    setName(Moized as MoizedFunction, fn.name, options.profileName);\n\n    return Moized;\n}\n", "import type { Key, Options } from '../index.d';\n\n/**\n * @function getCutoff\n *\n * @description\n * faster `Array.prototype.indexOf` implementation build for slicing / splicing\n *\n * @param array the array to match the value in\n * @param value the value to match\n * @returns the matching index, or -1\n */\nfunction getCutoff(array: any[], value: any) {\n    const { length } = array;\n\n    for (let index = 0; index < length; ++index) {\n        if (array[index] === value) {\n            return index + 1;\n        }\n    }\n\n    return 0;\n}\n\n/**\n * @private\n *\n * @description\n * custom replacer for the stringify function\n *\n * @returns if function then toString of it, else the value itself\n */\nexport function createDefaultReplacer() {\n    const cache: any[] = [];\n    const keys: string[] = [];\n\n    return function defaultReplacer(key: string, value: any) {\n        const type = typeof value;\n\n        if (type === 'function' || type === 'symbol') {\n            return value.toString();\n        }\n\n        if (typeof value === 'object') {\n            if (cache.length) {\n                const thisCutoff = getCutoff(cache, this);\n\n                if (thisCutoff === 0) {\n                    cache[cache.length] = this;\n                } else {\n                    cache.splice(thisCutoff);\n                    keys.splice(thisCutoff);\n                }\n\n                keys[keys.length] = key;\n\n                const valueCutoff = getCutoff(cache, value);\n\n                if (valueCutoff !== 0) {\n                    return `[ref=${\n                        keys.slice(0, valueCutoff).join('.') || '.'\n                    }]`;\n                }\n            } else {\n                cache[0] = value;\n                keys[0] = key;\n            }\n\n            return value;\n        }\n\n        return '' + value;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * get the stringified version of the argument passed\n *\n * @param arg argument to stringify\n * @returns the stringified argument\n */\nexport function getStringifiedArgument<Type>(arg: Type) {\n    const typeOfArg = typeof arg;\n\n    return arg && (typeOfArg === 'object' || typeOfArg === 'function')\n        ? JSON.stringify(arg, createDefaultReplacer())\n        : arg;\n}\n\n/**\n * @private\n *\n * @description\n * serialize the arguments passed\n *\n * @param options the options passed to the moizer\n * @param options.maxArgs the cap on the number of arguments used in serialization\n * @returns argument serialization method\n */\nexport function defaultArgumentSerializer(args: Key) {\n    let key = '|';\n\n    for (let index = 0; index < args.length; index++) {\n        key += getStringifiedArgument(args[index]) + '|';\n    }\n\n    return [key];\n}\n\n/**\n * @private\n *\n * @description\n * based on the options passed, either use the serializer passed or generate the internal one\n *\n * @param options the options passed to the moized function\n * @returns the function to use in serializing the arguments\n */\nexport function getSerializerFunction(options: Options) {\n    return typeof options.serializer === 'function'\n        ? options.serializer\n        : defaultArgumentSerializer;\n}\n\n/**\n * @private\n *\n * @description\n * are the serialized keys equal to one another\n *\n * @param cacheKey the cache key to compare\n * @param key the key to test\n * @returns are the keys equal\n */\nexport function getIsSerializedKeyEqual(cacheKey: Key, key: Key) {\n    return cacheKey[0] === key[0];\n}\n", "import { deepEqual, sameValueZeroEqual, shallowEqual } from 'fast-equals';\nimport { createGetInitialArgs } from './maxArgs';\nimport { getIsSerializedKeyEqual, getSerializerFunction } from './serialize';\nimport { compose } from './utils';\n\nimport type {\n    Cache,\n    IsEqual,\n    IsMatchingKey,\n    MicroMemoizeOptions,\n    Moized,\n    OnCacheOperation,\n    Options,\n    TransformKey,\n} from '../index.d';\n\nexport function createOnCacheOperation(\n    fn?: OnCacheOperation\n): OnCacheOperation {\n    if (typeof fn === 'function') {\n        return (\n            _cacheIgnored: Cache,\n            _microMemoizeOptionsIgnored: MicroMemoizeOptions,\n            memoized: Moized\n        ): void => fn(memoized.cache, memoized.options, memoized);\n    }\n}\n\n/**\n * @private\n *\n * @description\n * get the isEqual method passed to micro-memoize\n *\n * @param options the options passed to the moizer\n * @returns the isEqual method to apply\n */\nexport function getIsEqual(options: Options): IsEqual {\n    return (\n        options.matchesArg ||\n        (options.isDeepEqual && deepEqual) ||\n        (options.isShallowEqual && shallowEqual) ||\n        sameValueZeroEqual\n    );\n}\n\n/**\n * @private\n *\n * @description\n * get the isEqual method passed to micro-memoize\n *\n * @param options the options passed to the moizer\n * @returns the isEqual method to apply\n */\nexport function getIsMatchingKey(options: Options): IsMatchingKey | undefined {\n    return (\n        options.matchesKey ||\n        (options.isSerialized && getIsSerializedKeyEqual) ||\n        undefined\n    );\n}\n\n/**\n * @private\n *\n * @description\n * get the function that will transform the key based on the arguments passed\n *\n * @param options the options passed to the moizer\n * @returns the function to transform the key with\n */\nexport function getTransformKey(options: Options): TransformKey | undefined {\n    return compose(\n        options.isSerialized && getSerializerFunction(options),\n        typeof options.transformArgs === 'function' && options.transformArgs,\n        typeof options.maxArgs === 'number' &&\n            createGetInitialArgs(options.maxArgs)\n    ) as TransformKey;\n}\n", "import type { Key } from '../index.d';\n\nexport function createGetInitialArgs(size: number) {\n    /**\n     * @private\n     *\n     * @description\n     * take the first N number of items from the array (faster than slice)\n     *\n     * @param args the args to take from\n     * @returns the shortened list of args as an array\n     */\n    return function (args: Key): Key {\n        if (size >= args.length) {\n            return args;\n        }\n\n        if (size === 0) {\n            return [];\n        }\n\n        if (size === 1) {\n            return [args[0]];\n        }\n\n        if (size === 2) {\n            return [args[0], args[1]];\n        }\n\n        if (size === 3) {\n            return [args[0], args[1], args[2]];\n        }\n\n        const clone = [];\n\n        for (let index = 0; index < size; index++) {\n            clone[index] = args[index];\n        }\n\n        return clone;\n    };\n}\n", "import memoize from 'micro-memoize';\nimport { createMoizedComponent } from './component';\nimport { DEFAULT_OPTIONS } from './constants';\nimport { createMoizeInstance } from './instance';\nimport { getMaxAgeOptions } from './maxAge';\nimport {\n    createOnCacheOperation,\n    getIsEqual,\n    getIsMatchingKey,\n    getTransformKey,\n} from './options';\nimport {\n    clearStats,\n    collectStats,\n    getDefaultProfileName,\n    getStats,\n    getStatsOptions,\n    statsCache,\n} from './stats';\nimport { createRefreshableMoized } from './updateCacheForKey';\nimport { combine, compose, isMoized, mergeOptions, setName } from './utils';\n\nimport type {\n    Expiration,\n    IsEqual,\n    IsMatchingKey,\n    MicroMemoizeOptions,\n    Moize,\n    Moizeable,\n    Moized,\n    OnExpire,\n    Options,\n    Serialize,\n    TransformKey,\n    UpdateCacheForKey,\n} from '../index.d';\n\n/**\n * @module moize\n */\n\n/**\n * @description\n * memoize a function based its arguments passed, potentially improving runtime performance\n *\n * @example\n * import moize from 'moize';\n *\n * // standard implementation\n * const fn = (foo, bar) => `${foo} ${bar}`;\n * const memoizedFn = moize(fn);\n *\n * // implementation with options\n * const fn = async (id) => get(`http://foo.com/${id}`);\n * const memoizedFn = moize(fn, {isPromise: true, maxSize: 5});\n *\n * // implementation with convenience methods\n * const Foo = ({foo}) => <div>{foo}</div>;\n * const MemoizedFoo = moize.react(Foo);\n *\n * @param fn the function to memoized, or a list of options when currying\n * @param [options=DEFAULT_OPTIONS] the options to apply\n * @returns the memoized function\n */\nconst moize: Moize = function <\n    Fn extends Moizeable,\n    PassedOptions extends Options\n>(fn: Fn | PassedOptions, passedOptions?: PassedOptions) {\n    type CombinedOptions = Options & PassedOptions;\n\n    const options: Options = passedOptions || DEFAULT_OPTIONS;\n\n    if (isMoized(fn)) {\n        const moizeable = fn.originalFunction as Fn;\n        const mergedOptions = mergeOptions(\n            fn.options,\n            options\n        ) as CombinedOptions;\n\n        return moize<Fn, CombinedOptions>(moizeable, mergedOptions);\n    }\n\n    if (typeof fn === 'object') {\n        return function <\n            CurriedFn extends Moizeable,\n            CurriedOptions extends Options\n        >(\n            curriedFn: CurriedFn | CurriedOptions,\n            curriedOptions: CurriedOptions\n        ) {\n            type CombinedCurriedOptions = CombinedOptions & CurriedOptions;\n\n            if (typeof curriedFn === 'function') {\n                const mergedOptions = mergeOptions(\n                    fn as CombinedOptions,\n                    curriedOptions\n                ) as CombinedCurriedOptions;\n\n                return moize(curriedFn, mergedOptions);\n            }\n\n            const mergedOptions = mergeOptions(\n                fn as CombinedOptions,\n                curriedFn as CurriedOptions\n            );\n\n            return moize(mergedOptions);\n        };\n    }\n\n    if (options.isReact) {\n        return createMoizedComponent(moize, fn, options);\n    }\n\n    const coalescedOptions: Options = {\n        ...DEFAULT_OPTIONS,\n        ...options,\n        maxAge:\n            typeof options.maxAge === 'number' && options.maxAge >= 0\n                ? options.maxAge\n                : DEFAULT_OPTIONS.maxAge,\n        maxArgs:\n            typeof options.maxArgs === 'number' && options.maxArgs >= 0\n                ? options.maxArgs\n                : DEFAULT_OPTIONS.maxArgs,\n        maxSize:\n            typeof options.maxSize === 'number' && options.maxSize >= 0\n                ? options.maxSize\n                : DEFAULT_OPTIONS.maxSize,\n        profileName: options.profileName || getDefaultProfileName(fn),\n    };\n    const expirations: Array<Expiration> = [];\n\n    const {\n        matchesArg: equalsIgnored,\n        isDeepEqual: isDeepEqualIgnored,\n        isPromise,\n        isReact: isReactIgnored,\n        isSerialized: isSerialzedIgnored,\n        isShallowEqual: isShallowEqualIgnored,\n        matchesKey: matchesKeyIgnored,\n        maxAge: maxAgeIgnored,\n        maxArgs: maxArgsIgnored,\n        maxSize,\n        onCacheAdd,\n        onCacheChange,\n        onCacheHit,\n        onExpire: onExpireIgnored,\n        profileName: profileNameIgnored,\n        serializer: serializerIgnored,\n        updateCacheForKey,\n        transformArgs: transformArgsIgnored,\n        updateExpire: updateExpireIgnored,\n        ...customOptions\n    } = coalescedOptions;\n\n    const isEqual = getIsEqual(coalescedOptions);\n    const isMatchingKey = getIsMatchingKey(coalescedOptions);\n\n    const maxAgeOptions = getMaxAgeOptions(\n        expirations,\n        coalescedOptions,\n        isEqual,\n        isMatchingKey\n    );\n    const statsOptions = getStatsOptions(coalescedOptions);\n\n    const transformKey = getTransformKey(coalescedOptions);\n\n    const microMemoizeOptions: MicroMemoizeOptions = {\n        ...customOptions,\n        isEqual,\n        isMatchingKey,\n        isPromise,\n        maxSize,\n        onCacheAdd: createOnCacheOperation(\n            combine(\n                onCacheAdd,\n                maxAgeOptions.onCacheAdd,\n                statsOptions.onCacheAdd\n            )\n        ),\n        onCacheChange: createOnCacheOperation(onCacheChange),\n        onCacheHit: createOnCacheOperation(\n            combine(\n                onCacheHit,\n                maxAgeOptions.onCacheHit,\n                statsOptions.onCacheHit\n            )\n        ),\n        transformKey,\n    };\n\n    const memoized = memoize(fn, microMemoizeOptions);\n\n    let moized = createMoizeInstance<Fn, CombinedOptions>(memoized, {\n        expirations,\n        options: coalescedOptions,\n        originalFunction: fn,\n    });\n\n    if (updateCacheForKey) {\n        moized = createRefreshableMoized<typeof moized>(moized);\n    }\n\n    setName(moized, (fn as Moizeable).name, options.profileName);\n\n    return moized;\n};\n\n/**\n * @function\n * @name clearStats\n * @memberof module:moize\n * @alias moize.clearStats\n *\n * @description\n * clear all existing stats stored\n */\nmoize.clearStats = clearStats;\n\n/**\n * @function\n * @name collectStats\n * @memberof module:moize\n * @alias moize.collectStats\n *\n * @description\n * start collecting statistics\n */\nmoize.collectStats = collectStats;\n\n/**\n * @function\n * @name compose\n * @memberof module:moize\n * @alias moize.compose\n *\n * @description\n * method to compose moized methods and return a single moized function\n *\n * @param moized the functions to compose\n * @returns the composed function\n */\nmoize.compose = function (...moized: Moize[]) {\n    return compose<Moize>(...moized) || moize;\n};\n\n/**\n * @function\n * @name deep\n * @memberof module:moize\n * @alias moize.deep\n *\n * @description\n * should deep equality check be used\n *\n * @returns the moizer function\n */\nmoize.deep = moize({ isDeepEqual: true });\n\n/**\n * @function\n * @name getStats\n * @memberof module:moize\n * @alias moize.getStats\n *\n * @description\n * get the statistics of a given profile, or overall usage\n *\n * @returns statistics for a given profile or overall usage\n */\nmoize.getStats = getStats;\n\n/**\n * @function\n * @name infinite\n * @memberof module:moize\n * @alias moize.infinite\n *\n * @description\n * a moized method that will remove all limits from the cache size\n *\n * @returns the moizer function\n */\nmoize.infinite = moize({ maxSize: Infinity });\n\n/**\n * @function\n * @name isCollectingStats\n * @memberof module:moize\n * @alias moize.isCollectingStats\n *\n * @description\n * are stats being collected\n *\n * @returns are stats being collected\n */\nmoize.isCollectingStats = function isCollectingStats(): boolean {\n    return statsCache.isCollectingStats;\n};\n\n/**\n * @function\n * @name isMoized\n * @memberof module:moize\n * @alias moize.isMoized\n *\n * @description\n * is the fn passed a moized function\n *\n * @param fn the object to test\n * @returns is fn a moized function\n */\nmoize.isMoized = function isMoized(fn: any): fn is Moized {\n    return typeof fn === 'function' && !!fn.isMoized;\n};\n\n/**\n * @function\n * @name matchesArg\n * @memberof module:moize\n * @alias moize.matchesArg\n *\n * @description\n * a moized method where the arg matching method is the custom one passed\n *\n * @param keyMatcher the method to compare against those in cache\n * @returns the moizer function\n */\nmoize.matchesArg = function (argMatcher: IsEqual) {\n    return moize({ matchesArg: argMatcher });\n};\n\n/**\n * @function\n * @name matchesKey\n * @memberof module:moize\n * @alias moize.matchesKey\n *\n * @description\n * a moized method where the key matching method is the custom one passed\n *\n * @param keyMatcher the method to compare against those in cache\n * @returns the moizer function\n */\nmoize.matchesKey = function (keyMatcher: IsMatchingKey) {\n    return moize({ matchesKey: keyMatcher });\n};\n\nfunction maxAge<MaxAge extends number>(\n    maxAge: MaxAge\n): Moize<{ maxAge: MaxAge }>;\nfunction maxAge<MaxAge extends number, UpdateExpire extends boolean>(\n    maxAge: MaxAge,\n    expireOptions: UpdateExpire\n): Moize<{ maxAge: MaxAge; updateExpire: UpdateExpire }>;\nfunction maxAge<MaxAge extends number, ExpireHandler extends OnExpire>(\n    maxAge: MaxAge,\n    expireOptions: ExpireHandler\n): Moize<{ maxAge: MaxAge; onExpire: ExpireHandler }>;\nfunction maxAge<\n    MaxAge extends number,\n    ExpireHandler extends OnExpire,\n    ExpireOptions extends {\n        onExpire: ExpireHandler;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions: ExpireOptions\n): Moize<{ maxAge: MaxAge; onExpire: ExpireOptions['onExpire'] }>;\nfunction maxAge<\n    MaxAge extends number,\n    UpdateExpire extends boolean,\n    ExpireOptions extends {\n        updateExpire: UpdateExpire;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions: ExpireOptions\n): Moize<{ maxAge: MaxAge; updateExpire: UpdateExpire }>;\nfunction maxAge<\n    MaxAge extends number,\n    ExpireHandler extends OnExpire,\n    UpdateExpire extends boolean,\n    ExpireOptions extends {\n        onExpire: ExpireHandler;\n        updateExpire: UpdateExpire;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions: ExpireOptions\n): Moize<{\n    maxAge: MaxAge;\n    onExpire: ExpireHandler;\n    updateExpire: UpdateExpire;\n}>;\nfunction maxAge<\n    MaxAge extends number,\n    ExpireHandler extends OnExpire,\n    UpdateExpire extends boolean,\n    ExpireOptions extends {\n        onExpire?: ExpireHandler;\n        updateExpire?: UpdateExpire;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions?: ExpireHandler | UpdateExpire | ExpireOptions\n) {\n    if (expireOptions === true) {\n        return moize({\n            maxAge,\n            updateExpire: expireOptions,\n        });\n    }\n\n    if (typeof expireOptions === 'object') {\n        const { onExpire, updateExpire } = expireOptions;\n\n        return moize({\n            maxAge,\n            onExpire,\n            updateExpire,\n        });\n    }\n\n    if (typeof expireOptions === 'function') {\n        return moize({\n            maxAge,\n            onExpire: expireOptions,\n            updateExpire: true,\n        });\n    }\n\n    return moize({ maxAge });\n}\n\n/**\n * @function\n * @name maxAge\n * @memberof module:moize\n * @alias moize.maxAge\n *\n * @description\n * a moized method where the age of the cache is limited to the number of milliseconds passed\n *\n * @param maxAge the TTL of the value in cache\n * @returns the moizer function\n */\nmoize.maxAge = maxAge;\n\n/**\n * @function\n * @name maxArgs\n * @memberof module:moize\n * @alias moize.maxArgs\n *\n * @description\n * a moized method where the number of arguments used for determining cache is limited to the value passed\n *\n * @param maxArgs the number of args to base the key on\n * @returns the moizer function\n */\nmoize.maxArgs = function maxArgs(maxArgs: number) {\n    return moize({ maxArgs });\n};\n\n/**\n * @function\n * @name maxSize\n * @memberof module:moize\n * @alias moize.maxSize\n *\n * @description\n * a moized method where the total size of the cache is limited to the value passed\n *\n * @param maxSize the maximum size of the cache\n * @returns the moizer function\n */\nmoize.maxSize = function maxSize(maxSize: number) {\n    return moize({ maxSize });\n};\n\n/**\n * @function\n * @name profile\n * @memberof module:moize\n * @alias moize.profile\n *\n * @description\n * a moized method with a profile name\n *\n * @returns the moizer function\n */\nmoize.profile = function (profileName: string) {\n    return moize({ profileName });\n};\n\n/**\n * @function\n * @name promise\n * @memberof module:moize\n * @alias moize.promise\n *\n * @description\n * a moized method specific to caching resolved promise / async values\n *\n * @returns the moizer function\n */\nmoize.promise = moize({\n    isPromise: true,\n    updateExpire: true,\n});\n\n/**\n * @function\n * @name react\n * @memberof module:moize\n * @alias moize.react\n *\n * @description\n * a moized method specific to caching React element values\n *\n * @returns the moizer function\n */\nmoize.react = moize({ isReact: true });\n\n/**\n * @function\n * @name serialize\n * @memberof module:moize\n * @alias moize.serialize\n *\n * @description\n * a moized method that will serialize the arguments passed to use as the cache key\n *\n * @returns the moizer function\n */\nmoize.serialize = moize({ isSerialized: true });\n\n/**\n * @function\n * @name serializeWith\n * @memberof module:moize\n * @alias moize.serializeWith\n *\n * @description\n * a moized method that will serialize the arguments passed to use as the cache key\n * based on the serializer passed\n *\n * @returns the moizer function\n */\nmoize.serializeWith = function (serializer: Serialize) {\n    return moize({ isSerialized: true, serializer });\n};\n\n/**\n * @function\n * @name shallow\n * @memberof module:moize\n * @alias moize.shallow\n *\n * @description\n * should shallow equality check be used\n *\n * @returns the moizer function\n */\nmoize.shallow = moize({ isShallowEqual: true });\n\n/**\n * @function\n * @name transformArgs\n * @memberof module:moize\n * @alias moize.transformArgs\n *\n * @description\n * transform the args to allow for specific cache key comparison\n *\n * @param transformArgs the args transformer\n * @returns the moizer function\n */\nmoize.transformArgs = <Transformer extends TransformKey>(\n    transformArgs: Transformer\n) => moize({ transformArgs });\n\n/**\n * @function\n * @name updateCacheForKey\n * @memberof module:moize\n * @alias moize.updateCacheForKey\n *\n * @description\n * update the cache for a given key when the method passed returns truthy\n *\n * @param updateCacheForKey the method to determine when to update cache\n * @returns the moizer function\n */\nmoize.updateCacheForKey = <UpdateWhen extends UpdateCacheForKey>(\n    updateCacheForKey: UpdateWhen\n) => moize({ updateCacheForKey });\n\n// Add self-referring `default` property for edge-case cross-compatibility of mixed ESM/CommonJS usage.\n// This property is frozen and non-enumerable to avoid visibility on iteration or accidental overrides.\nObject.defineProperty(moize, 'default', {\n    configurable: false,\n    enumerable: false,\n    value: moize,\n    writable: false,\n});\n\nexport default moize;\n", "import { copyStaticProperties } from './instance';\n\nimport type { Moized } from '../index.d';\n\nexport function createRefreshableMoized<MoizedFn extends Moized>(\n    moized: MoizedFn\n) {\n    const {\n        options: { updateCacheForKey },\n    } = moized;\n\n    /**\n     * @private\n     *\n     * @description\n     * Wrapper around already-`moize`d function which will intercept the memoization\n     * and call the underlying function directly with the purpose of updating the cache\n     * for the given key.\n     *\n     * Promise values use a tweak of the logic that exists at cache.updateAsyncCache, which\n     * reverts to the original value if the promise is rejected and there was already a cached\n     * value.\n     */\n    const refreshableMoized = function refreshableMoized(\n        this: any,\n        ...args: Parameters<typeof moized.fn>\n    ) {\n        if (!updateCacheForKey(args)) {\n            return moized.apply(this, args);\n        }\n\n        const result = moized.fn.apply(this, args);\n\n        moized.set(args, result);\n\n        return result;\n    } as typeof moized;\n\n    copyStaticProperties(moized, refreshableMoized);\n\n    return refreshableMoized;\n}\n"], "names": ["DEFAULT_OPTIONS", "isDeepEqual", "isPromise", "isReact", "isSerialized", "isShallowEqual", "matchesArg", "undefined", "matchesKey", "maxAge", "maxArgs", "maxSize", "onExpire", "profileName", "serializer", "updateCacheForKey", "transformArgs", "updateExpire", "combine", "_len", "arguments", "length", "functions", "Array", "_key", "reduce", "f", "g", "apply", "this", "compose", "_len2", "_key2", "findExpirationIndex", "expirations", "key", "index", "createFindKeyIndex", "isEqual", "isMatchingKey", "areKeysEqual", "cache<PERSON>ey", "keys", "keysIndex", "mergeOptions", "originalOptions", "newOptions", "_extends", "onCacheAdd", "onCacheChange", "onCacheHit", "setName", "fn", "originalFunctionName", "name", "Object", "defineProperty", "configurable", "enumerable", "value", "writable", "clearExpiration", "<PERSON><PERSON><PERSON><PERSON>", "expirationIndex", "clearTimeout", "timeoutId", "splice", "createTimeout", "expirationMethod", "setTimeout", "unref", "createOnCacheHitResetExpiration", "options", "cache", "getMaxAgeOptions", "isFinite", "moizedOptions", "moized", "keyIndex", "findKeyIndex", "values", "unshift", "push", "createOnCacheAddSetExpiration", "statsCache", "anonymousProfileNameCounter", "isCollectingStats", "profiles", "hasWarningDisplayed", "clearStats", "createOnCacheAddIncrementCalls", "calls", "hits", "createOnCacheHitIncrementCallsAndHits", "getDefaultProfileName", "displayName", "getUsagePercentage", "toFixed", "getStats", "console", "warn", "usage", "profile", "completeStats", "completeProfiles", "computedProfiles", "ALWAYS_SKIPPED_PROPERTIES", "callee", "caller", "constructor", "prototype", "copyStaticProperties", "originalFn", "newFn", "skippedProperties", "getOwnPropertyNames", "for<PERSON>ach", "property", "indexOf", "descriptor", "getOwnPropertyDescriptor", "get", "set", "createMoizeInstance", "memoized", "configuration", "_ref", "clear", "_microMemoizeOptions", "transform<PERSON>ey", "has", "cacheSnapshot", "remove", "existingKey", "cutoff", "size", "updateAsyncCache", "orderByLru", "addInstanceMethods", "_ref2", "moizeOptions", "originalFunction", "microMemoizeOptions", "defineProperties", "currentCache", "slice", "expirationsSnapshot", "isMoized", "addInstanceProperties", "REACT_ELEMENT_TYPE", "Symbol", "for", "<PERSON><PERSON><PERSON><PERSON>", "array", "getStringifiedArgument", "arg", "typeOfArg", "JSON", "stringify", "type", "toString", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "join", "defaultArgumentSerializer", "args", "getIsSerializedKeyEqual", "createOnCacheOperation", "_cacheIgnored", "_microMemoizeOptionsIgnored", "getTransformKey", "getSerializerFunction", "clone", "moize", "passedOptions", "moizeable", "mergedOptions", "curriedFn", "curriedOptions", "moizer", "reactMoizer", "Moized", "props", "context", "updater", "MoizedComponent", "isReactComponent", "render", "$$typeof", "ref", "_owner", "createMoizedComponent", "coalescedOptions", "customOptions", "_excluded", "deepEqual", "shallowEqual", "sameValueZeroEqual", "getIsEqual", "getIsMatchingKey", "maxAgeOptions", "statsOptions", "getStatsOptions", "memoize", "refreshableMoized", "result", "createRefreshableMoized", "collectStats", "deep", "infinite", "Infinity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "expireOptions", "promise", "react", "serialize", "serializeWith", "shallow"], "mappings": "6nBAOO,IAAMA,EAA2B,CACpCC,aAAa,EACbC,WAAW,EACXC,SAAS,EACTC,cAAc,EACdC,gBAAgB,EAChBC,gBAAYC,EACZC,gBAAYD,EACZE,YAAQF,EACRG,aAASH,EACTI,QAAS,EACTC,cAAUL,EACVM,iBAAaN,EACbO,gBAAYP,EACZQ,uBAAmBR,EACnBS,mBAAeT,EACfU,cAAc,GCDX,SAASC,IAEe,IAAA,IAAAC,EAAAC,UAAAC,OADxBC,EACwB,IAAAC,MAAAJ,GAAAK,EAAA,EAAAA,EAAAL,EAAAK,IADxBF,EACwBE,GAAAJ,UAAAI,GAC3B,OAAOF,EAAUG,QAAO,SAAUC,EAAQC,GACtC,MAAiB,mBAAND,EACa,mBAANC,EACR,WACID,EAAEE,MAAMC,KAAMT,WACdO,EAAEC,MAAMC,KAAMT,UAHjB,EAKDM,EAGO,mBAANC,EACAA,OADX,CAGH,GACJ,CAWM,SAASG,IAAgD,IAAA,IAAAC,EAAAX,UAAAC,OAA7BC,EAA6B,IAAAC,MAAAQ,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAA7BV,EAA6BU,GAAAZ,UAAAY,GAC5D,OAAOV,EAAUG,QAAO,SAAUC,EAAQC,GACtC,MAAiB,mBAAND,EACa,mBAANC,EACR,WACI,OAAOD,EAAEC,EAAEC,MAAMC,KAAMT,WAF1B,EAIDM,EAGO,mBAANC,EACAA,OADX,CAGH,GACJ,CAYM,SAASM,EAAoBC,EAA2BC,GAC3D,IAAK,IAAIC,EAAQ,EAAGA,EAAQF,EAAYb,OAAQe,IAC5C,GAAIF,EAAYE,GAAOD,MAAQA,EAC3B,OAAOC,EAIf,OAAQ,CACX,CAYM,SAASC,EACZC,EACAC,GAEA,IAAMC,EACuB,mBAAlBD,EACDA,EACA,SAAUE,EAAeN,GACrB,IAAK,IAAIC,EAAQ,EAAGA,EAAQD,EAAId,OAAQe,IACpC,IAAKE,EAAQG,EAASL,GAAQD,EAAIC,IAC9B,OAAO,EAIf,OAAO,GAGrB,OAAO,SAAUM,EAAaP,GAC1B,IAAK,IAAIQ,EAAY,EAAGA,EAAYD,EAAKrB,OAAQsB,IAC7C,GACID,EAAKC,GAAWtB,SAAWc,EAAId,QAC/BmB,EAAaE,EAAKC,GAAYR,GAE9B,OAAOQ,EAIf,OAAQ,EAEf,CAYM,SAASC,EACZC,EACAC,GAEA,OAAQA,GAAcA,IAAe9C,EAA9B+C,EAAA,CAAA,EAGMF,EACAC,EAJN,CAKGE,WAAY9B,EACR2B,EAAgBG,WAChBF,EAAWE,YAEfC,cAAe/B,EACX2B,EAAgBI,cAChBH,EAAWG,eAEfC,WAAYhC,EACR2B,EAAgBK,WAChBJ,EAAWI,YAEflC,cAAec,EACXe,EAAgB7B,cAChB8B,EAAW9B,iBAlBnB6B,CAqBT,CAMM,SAASM,EACZC,EACAC,EACAxC,GAEA,IACI,IAAMyC,EAAOzC,GAAewC,GAAwB,YAEpDE,OAAOC,eAAeJ,EAAI,OAAQ,CAC9BK,cAAc,EACdC,YAAY,EACZC,MAAK,UAAYL,EAHa,IAI9BM,UAAU,GAIjB,CAFC,SAED,CACJ,CCnKM,SAASC,EACZ3B,EACAC,EACA2B,GAEA,IAAMC,EAAkB9B,EAAoBC,EAAaC,IAEhC,IAArB4B,IACAC,aAAa9B,EAAY6B,GAAiBE,WAEtCH,GACA5B,EAAYgC,OAAOH,EAAiB,GAG/C,CAaM,SAASI,EAAcC,EAA8B3D,GACxD,IAAMwD,EAAYI,WAAWD,EAAkB3D,GAM/C,MAJ+B,mBAApBwD,EAAUK,OACjBL,EAAUK,QAGPL,CACV,CAiFM,SAASM,EACZrC,EACAsC,GAEA,OAAO,SAAoBC,GACvB,IAAMtC,EAAMsC,EAAM/B,KAAK,GACjBqB,EAAkB9B,EAAoBC,EAAaC,IAEpD4B,IACDF,EAAgB3B,EAAaC,GAAK,GAElCD,EAAY6B,GAAiBE,UAAYE,EACrCjC,EAAY6B,GAAiBK,iBAC7BI,EAAQ/D,SAIvB,CAcM,SAASiE,EACZxC,EACAsC,EACAlC,EACAC,GAKA,IAAMS,EACwB,iBAAnBwB,EAAQ/D,QAAuBkE,SAASH,EAAQ/D,QA5GxD,SACHyB,EACAsC,EACAlC,EACAC,GAEA,IAAQ9B,EAAW+D,EAAX/D,OAER,OAAO,SAASuC,EACZyB,EACAG,EACAC,GAEA,IAAM1C,EAAWsC,EAAM/B,KAAK,GAE5B,IAA+C,IAA3CT,EAAoBC,EAAaC,GAAa,CAC9C,IAAMiC,EAAmB,WACrB,IAEMU,EAFezC,EAAmBC,EAASC,EAExBwC,CAAaN,EAAM/B,KAAMP,GAC5CwB,EAAac,EAAMO,OAAOF,IAE3BA,IACDL,EAAM/B,KAAKwB,OAAOY,EAAU,GAC5BL,EAAMO,OAAOd,OAAOY,EAAU,GAEO,mBAA1BN,EAAQvB,eACfuB,EAAQvB,cAAcwB,EAAOG,EAAeC,IAIpDhB,EAAgB3B,EAAaC,GAAK,GAGF,mBAArBqC,EAAQ5D,WACW,IAA1B4D,EAAQ5D,SAASuB,KAEjBsC,EAAM/B,KAAKuC,QAAQ9C,GACnBsC,EAAMO,OAAOC,QAAQtB,GAErBX,EAAWyB,EAAOG,EAAeC,GAEI,mBAA1BL,EAAQvB,eACfuB,EAAQvB,cAAcwB,EAAOG,EAAeC,KAKxD3C,EAAYgD,KAAK,CACbd,iBAAAA,EACAjC,IAAAA,EACA8B,UAAWE,EAAcC,EAAkB3D,IAElD,EAER,CAsDa0E,CACIjD,EACAsC,EACAlC,EACAC,QAEJhC,EAEV,MAAO,CACHyC,WAAAA,EACAE,WACIF,GAAcwB,EAAQvD,aAChBsD,EAAgCrC,EAAasC,QAC7CjE,EAEjB,CC1LM,IAAM6E,EAAyB,CAClCC,4BAA6B,EAC7BC,mBAAmB,EACnBC,SAAU,CAAA,GAGVC,GAAsB,EAEnB,SAASC,EAAW5E,GACnBA,SACOuE,EAAWG,SAAS1E,GAE3BuE,EAAWG,SAAW,EAE7B,CAoBM,SAASG,EAA+BlB,GAC3C,IAAQ3D,EAAgB2D,EAAhB3D,YAER,OAAO,WACCA,IAAgBuE,EAAWG,SAAS1E,KACpCuE,EAAWG,SAAS1E,GAAe,CAC/B8E,MAAO,EACPC,KAAM,IAIdR,EAAWG,SAAS1E,GAAa8E,QAExC,CAQM,SAASE,EAAsCrB,GAClD,OAAO,WACH,IAAQe,EAAaH,EAAbG,SACA1E,EAAgB2D,EAAhB3D,YAEH0E,EAAS1E,KACV0E,EAAS1E,GAAe,CACpB8E,MAAO,EACPC,KAAM,IAIdL,EAAS1E,GAAa8E,QACtBJ,EAAS1E,GAAa+E,OAE7B,CAWM,SAASE,EACZ1C,GAEA,OACKA,EAAoD2C,aACrD3C,EAAGE,MACU8B,aAAAA,EAAWC,6BAE/B,CAYM,SAASW,EAAmBL,EAAeC,GAC9C,OAAOD,GAAaC,EAAOD,EAAS,KAAKM,QAAQ,OAAQ,SAC5D,CAWM,SAASC,EAASrF,GAChBuE,EAAWE,mBAAsBE,IAClCW,QAAQC,KACJ,sFAGJZ,GAAsB,GAG1B,IAAQD,EAAaH,EAAbG,SAER,GAAI1E,EAAa,CACb,IAAK0E,EAAS1E,GACV,MAAO,CACH8E,MAAO,EACPC,KAAM,EACNS,MAAO,WAIf,IAAuBC,EAAYf,EAA1B1E,GAET,OAAAkC,EAAA,CAAA,EACOuD,EADP,CAEID,MAAOL,EAAmBM,EAAQX,MAAOW,EAAQV,OAExD,CAED,IAAMW,EAA8BhD,OAAOb,KAAK0C,EAAWG,UAAU9D,QACjE,SAAC+E,EAAkB3F,GAIf,OAHA2F,EAAiBb,OAASJ,EAAS1E,GAAa8E,MAChDa,EAAiBZ,MAAQL,EAAS1E,GAAa+E,KAExCY,CACV,GACD,CACIb,MAAO,EACPC,KAAM,IAId,OAAA7C,EAAA,CAAA,EACOwD,EADP,CAEIhB,SAAUhC,OAAOb,KAAK6C,GAAU9D,QAC5B,SAACgF,EAAkB5F,GAGf,OAFA4F,EAAiB5F,GAAeqF,EAASrF,GAElC4F,CAJL,GAMN,IAEJJ,MAAOL,EAAmBO,EAAcZ,MAAOY,EAAcX,OAEpE,CClKD,IAAMc,EAAqD,CACvDtF,WAAW,EACXuF,QAAQ,EACRC,QAAQ,EACRC,aAAa,EACbxF,QAAQ,EACRiC,MAAM,EACNwD,WAAW,GAcR,SAASC,EACZC,EACAC,EACAC,QACF,IADEA,IAAAA,EAA8B,IAE9B3D,OAAO4D,oBAAoBH,GAAYI,SAAQ,SAACC,GAC5C,IACKX,EAA0BW,KACc,IAAzCH,EAAkBI,QAAQD,GAC5B,CACE,IAAME,EAAahE,OAAOiE,yBACtBR,EACAK,GAGAE,EAAWE,KAAOF,EAAWG,IAC7BnE,OAAOC,eAAeyD,EAAOI,EAAUE,GAEvCN,EAAMI,GACFL,EAAWK,EAEtB,IAER,CAuPM,SAASM,EAIZC,EACAC,GAKA,OAvPG,SACHD,EAEFE,GAAA,IADI5F,IAAAA,YAEMsC,EAAYoD,EAAZpD,QAEFO,EAAe1C,EACjBmC,EAAQlC,QACRkC,EAAQjC,eAGNsC,EAAS+C,EAEf/C,EAAOkD,MAAQ,WACX,IAC4B9E,EAExB4B,EAFAmD,qBAAwB/E,cACxBwB,EACAI,EADAJ,MAUJ,OAPAA,EAAM/B,KAAKrB,OAAS,EACpBoD,EAAMO,OAAO3D,OAAS,EAElB4B,GACAA,EAAcwB,EAAOI,EAAOL,QAASK,IAGlC,GAGXA,EAAOY,WAAa,WAChBA,EAAWZ,EAAOL,QAAQ3D,cAG9BgE,EAAO4C,IAAM,SAAUtF,GACnB,IAC4B8F,EAExBpD,EAFAmD,qBAAwBC,aACxBxD,EACAI,EADAJ,MAGEhC,EAAWwF,EAAeA,EAAa9F,GAAOA,EAGpD,OAAqB,IAFJ4C,EAAaN,EAAM/B,KAAMD,GAEjBoC,EAAOjD,MAAMC,KAAMM,QAAO5B,GAGvDsE,EAAOqB,SAAW,WACd,OAAOA,EAASrB,EAAOL,QAAQ3D,cAGnCgE,EAAOqD,IAAM,SAAU/F,GACnB,IAAQ8F,EAAiBpD,EAAOmD,qBAAxBC,aAEFxF,EAAWwF,EAAeA,EAAa9F,GAAOA,EAEpD,OAAsD,IAA/C4C,EAAaF,EAAOJ,MAAM/B,KAAMD,IAG3CoC,EAAOnC,KAAO,WACV,OAAOmC,EAAOsD,cAAczF,MAGhCmC,EAAOuD,OAAS,SAAUjG,GACtB,IAGI0C,EAAAA,EAFAmD,qBAAwB/E,IAAAA,cAAegF,IAAAA,aACvCxD,EACAI,EADAJ,MAGEK,EAAWC,EACbN,EAAM/B,KACNuF,EAAeA,EAAa9F,GAAOA,GAGvC,IAAkB,IAAd2C,EACA,OAAO,EAGX,IAAMuD,EAAc5D,EAAM/B,KAAKoC,GAW/B,OATAL,EAAM/B,KAAKwB,OAAOY,EAAU,GAC5BL,EAAMO,OAAOd,OAAOY,EAAU,GAE1B7B,GACAA,EAAcwB,EAAOI,EAAOL,QAASK,GAGzChB,EAAgB3B,EAAamG,GAAa,IAEnC,GAGXxD,EAAO6C,IAAM,SAAUvF,EAAUwB,GAC7B,IAAQqE,EAAyCnD,EAAzCmD,qBAAsBvD,EAAmBI,EAAnBJ,MAAOD,EAAYK,EAAZL,QAC7BxB,EACJgF,EADIhF,WAAYC,EAChB+E,EADgB/E,cAAegF,EAC/BD,EAD+BC,aAG7BxF,EAAWwF,EAAeA,EAAa9F,GAAOA,EAC9C2C,EAAWC,EAAaN,EAAM/B,KAAMD,GAE1C,IAAkB,IAAdqC,EAAiB,CACjB,IAAMwD,EAAS9D,EAAQ7D,QAAU,EAE7B8D,EAAM8D,KAAOD,IACb7D,EAAM/B,KAAKrB,OAASiH,EACpB7D,EAAMO,OAAO3D,OAASiH,GAG1B7D,EAAM/B,KAAKuC,QAAQxC,GACnBgC,EAAMO,OAAOC,QAAQtB,GAEjBa,EAAQtE,WACRuE,EAAM+D,iBAAiB3D,GAGvB7B,GACAA,EAAWyB,EAAOD,EAASK,GAG3B5B,GACAA,EAAcwB,EAAOD,EAASK,EAErC,KAAM,CACH,IAAMwD,EAAc5D,EAAM/B,KAAKoC,GAE/BL,EAAMO,OAAOF,GAAYnB,EAErBmB,EAAW,GACXL,EAAMgE,WAAWJ,EAAa1E,EAAOmB,GAGrCN,EAAQtE,WACRuE,EAAM+D,iBAAiB3D,GAGE,mBAAlB5B,GACPA,EAAcwB,EAAOD,EAASK,EAErC,GAGLA,EAAOG,OAAS,WACZ,OAAOH,EAAOsD,cAAcnD,OAEnC,CAsGG0D,CAA+Bd,EAAUC,GAzFtC,SACHD,EAMFe,GAAA,IAJMzG,IAAAA,YACS0G,IAATpE,QACAqE,IAAAA,iBAGaC,EAAwBlB,EAAjCpD,QAERjB,OAAOwF,iBAAiBnB,EAAU,CAC9BI,qBAAsB,CAClBvE,cAAc,EACdgE,IAAM,WACF,OAAOqB,CACV,GAGLX,cAAe,CACX1E,cAAc,EACdgE,IAAM,WACF,IAAeuB,EAAiBpB,EAAxBnD,MAER,MAAO,CACH/B,KAAMsG,EAAatG,KAAKuG,MAAM,GAC9BV,KAAMS,EAAaT,KACnBvD,OAAQgE,EAAahE,OAAOiE,MAAM,GAEzC,GAGL/G,YAAa,CACTuB,cAAc,EACdgE,IAAM,WACF,OAAOvF,CACV,GAGLgH,oBAAqB,CACjBzF,cAAc,EACdgE,IAAM,WACF,OAAOvF,EAAY+G,MAAM,EAC5B,GAGLE,SAAU,CACN1F,cAAc,EACdgE,IAAM,WACF,OAAO,CACV,GAGLjD,QAAS,CACLf,cAAc,EACdgE,IAAM,WACF,OAAOmB,CACV,GAGLC,iBAAkB,CACdpF,cAAc,EACdgE,IAAM,WACF,OAAOoB,CACV,KAMT9B,EAAqB8B,EAFNjB,EAGlB,CAoBGwB,CAAkCxB,EAAUC,GAErCD,CACV,CCjTD,IAAMyB,EACgB,mBAAXC,QAAyBA,OAAOC,IACjCD,OAAOC,IAAI,iBACX,MCHV,SAASC,EAAUC,EAAc9F,GAG7B,IAFA,IAAQtC,EAAWoI,EAAXpI,OAECe,EAAQ,EAAGA,EAAQf,IAAUe,EAClC,GAAIqH,EAAMrH,KAAWuB,EACjB,OAAOvB,EAAQ,EAIvB,OAAO,CACV,CA8DM,SAASsH,EAA6BC,GACzC,IApDMlF,EACA/B,EAmDAkH,SAAmBD,EAEzB,OAAOA,GAAsB,WAAdC,GAAwC,aAAdA,EAEnCD,EADAE,KAAKC,UAAUH,GAvDflF,EAAe,GACf/B,EAAiB,GAEhB,SAAyBP,EAAawB,GACzC,IAAMoG,SAAcpG,EAEpB,GAAa,aAAToG,GAAgC,WAATA,EACvB,OAAOpG,EAAMqG,WAGjB,GAAqB,iBAAVrG,EAAoB,CAC3B,GAAIc,EAAMpD,OAAQ,CACd,IAAM4I,EAAaT,EAAU/E,EAAO5C,MAEjB,IAAfoI,EACAxF,EAAMA,EAAMpD,QAAUQ,MAEtB4C,EAAMP,OAAO+F,GACbvH,EAAKwB,OAAO+F,IAGhBvH,EAAKA,EAAKrB,QAAUc,EAEpB,IAAM+H,EAAcV,EAAU/E,EAAOd,GAErC,GAAoB,IAAhBuG,EACA,MAAA,SACIxH,EAAKuG,MAAM,EAAGiB,GAAaC,KAAK,MAAQ,KAD5C,GAIP,MACG1F,EAAM,GAAKd,EACXjB,EAAK,GAAKP,EAGd,OAAOwB,CACV,CAED,MAAO,GAAKA,IAmBnB,CAYM,SAASyG,EAA0BC,GAGtC,IAFA,IAAIlI,EAAM,IAEDC,EAAQ,EAAGA,EAAQiI,EAAKhJ,OAAQe,IACrCD,GAAOuH,EAAuBW,EAAKjI,IAAU,IAGjD,MAAO,CAACD,EACX,CA2BM,SAASmI,EAAwB7H,EAAeN,GACnD,OAAOM,EAAS,KAAON,EAAI,EAC9B,CC3HM,SAASoI,EACZnH,GAEA,GAAkB,mBAAPA,EACP,OAAO,SACHoH,EACAC,EACA7C,GAHG,OAIIxE,EAAGwE,EAASnD,MAAOmD,EAASpD,QAASoD,GAEvD,CA8CM,SAAS8C,EAAgBlG,GAC5B,OAAO1C,EACH0C,EAAQpE,cD+CT,SAA+BoE,GAClC,MAAqC,mBAAvBA,EAAQ1D,WAChB0D,EAAQ1D,WACRsJ,CACT,CCnD+BO,CAAsBnG,GACb,mBAA1BA,EAAQxD,eAAgCwD,EAAQxD,cAC5B,iBAApBwD,EAAQ9D,UC1Ec6H,ED2EJ/D,EAAQ9D,QCjE9B,SAAU2J,GACb,GAAI9B,GAAQ8B,EAAKhJ,OACb,OAAOgJ,EAGX,GAAa,IAAT9B,EACA,MAAO,GAGX,GAAa,IAATA,EACA,MAAO,CAAC8B,EAAK,IAGjB,GAAa,IAAT9B,EACA,MAAO,CAAC8B,EAAK,GAAIA,EAAK,IAG1B,GAAa,IAAT9B,EACA,MAAO,CAAC8B,EAAK,GAAIA,EAAK,GAAIA,EAAK,IAKnC,IAFA,IAAMO,EAAQ,GAELxI,EAAQ,EAAGA,EAAQmG,EAAMnG,IAC9BwI,EAAMxI,GAASiI,EAAKjI,GAGxB,OAAOwI,KArCR,IAA8BrC,CD6EpC,uQEfKsC,EAAe,SAAfA,EAGJzH,EAAwB0H,GAGtB,IAAMtG,EAAmBsG,GAAiB9K,EAE1C,GR6FG,SAAkBoD,GACrB,MAAqB,mBAAPA,GAAsBA,EAAiB+F,QACxD,CQ/FOA,CAAS/F,GAAK,CACd,IAAM2H,EAAY3H,EAAGyF,iBACfmC,EAAgBpI,EAClBQ,EAAGoB,QACHA,GAGJ,OAAOqG,EAA2BE,EAAWC,EAChD,CAED,GAAkB,iBAAP5H,EACP,OAAO,SAIH6H,EACAC,GAIA,GAAyB,mBAAdD,EAA0B,CACjC,IAAMD,EAAgBpI,EAClBQ,EACA8H,GAGJ,OAAOL,EAAMI,EAAWD,EAC3B,CAED,IAAMA,EAAgBpI,EAClBQ,EACA6H,GAGJ,OAAOJ,EAAMG,IAIrB,GAAIxG,EAAQrE,QACR,OJ9ED,SACHgL,EACA/H,EACAoB,GAeA,IAAM4G,EAAcD,EAAMpI,EAAA,CACtBrC,QAAS,EACTL,gBAAgB,GACbmE,EAHmB,CAItBrE,SAAS,KAQb,SAASkL,EAELC,EACAC,EACAC,GAEA3J,KAAKyJ,MAAQA,EACbzJ,KAAK0J,QAAUA,EACf1J,KAAK2J,QAAUA,EAEf3J,KAAK4J,gBAAkBL,EAAYhI,EACtC,CAqBD,OArCKA,EAAG2C,cAEJ3C,EAAG2C,YAAc3C,EAAGE,MAAQ,aAgBhC+H,EAAOvE,UAAU4E,iBAAmB,GAEpCL,EAAOvE,UAAU6E,OAAS,WACtB,MAAO,CACHC,SAAUvC,EACVU,KAAMlI,KAAK4J,gBACXH,MAAOzJ,KAAKyJ,MACZO,IAAK,KACL1J,IAAK,KACL2J,OAAQ,OAIhB/E,EAAqB3D,EAAIiI,EAAQ,CAAC,cAAe,iBAEjDA,EAAOtF,YAAP,WAA+B3C,EAAG2C,aAAe3C,EAAGE,MAAQ,aAA5D,IAEAH,EAAQkI,EAA0BjI,EAAGE,KAAMkB,EAAQ3D,aAE5CwK,CACV,CIecU,CAAsBlB,EAAOzH,EAAIoB,GAG5C,IAAMwH,EAAyBjJ,EAAA,GACxB/C,EACAwE,EAFwB,CAG3B/D,OAC8B,iBAAnB+D,EAAQ/D,QAAuB+D,EAAQ/D,QAAU,EAClD+D,EAAQ/D,OACRT,EAAgBS,OAC1BC,QAC+B,iBAApB8D,EAAQ9D,SAAwB8D,EAAQ9D,SAAW,EACpD8D,EAAQ9D,QACRV,EAAgBU,QAC1BC,QAC+B,iBAApB6D,EAAQ7D,SAAwB6D,EAAQ7D,SAAW,EACpD6D,EAAQ7D,QACRX,EAAgBW,QAC1BE,YAAa2D,EAAQ3D,aAAeiF,EAAsB1C,KAExDlB,EAAiC,GAuBnC8J,EApBA1L,WAoBA0L,EAnBA/L,YAFJ,IAGIC,EAkBA8L,EAlBA9L,UAkBA8L,EAjBA7L,QAiBA6L,EAhBA5L,aAgBA4L,EAfA3L,eAeA2L,EAdAxL,WAcAwL,EAbAvL,OAaAuL,EAZAtL,QATJ,IAUIC,EAWAqL,EAXArL,QACAqC,EAUAgJ,EAVAhJ,WACAC,EASA+I,EATA/I,cACAC,EAQA8I,EARA9I,WAQA8I,EAPApL,SAOAoL,EANAnL,YAMAmL,EALAlL,WAhBJ,IAiBIC,EAIAiL,EAJAjL,kBAIAiL,EAHAhL,cAGAgL,EAFA/K,iBACGgL,uIACHD,EArBJE,GAuBM5J,EFvHH,SAAoBkC,GACvB,OACIA,EAAQlE,YACPkE,EAAQvE,aAAekM,EADxBA,WAEC3H,EAAQnE,gBAAkB+L,EAF3BA,cAGAC,oBAEP,CEgHmBC,CAAWN,GACrBzJ,EFtGH,SAA0BiC,GAC7B,OACIA,EAAQhE,YACPgE,EAAQpE,cAAgBkK,QACzB/J,CAEP,CEgGyBgM,CAAiBP,GAEjCQ,EAAgB9H,EAClBxC,EACA8J,EACA1J,EACAC,GAEEkK,ENyBH,SAAyBjI,GAI5B,OAAOY,EAAWE,kBACZ,CACItC,WAAY0C,EAA+BlB,GAC3CtB,WAAY2C,EAAsCrB,IAEtD,EACT,CMnCwBkI,CAAgBV,GAE/B/D,EAAeyC,EAAgBsB,GAE/BlD,OACCmD,EADuC,CAE1C3J,QAAAA,EACAC,cAAAA,EACArC,UAAAA,EACAS,QAAAA,EACAqC,WAAYuH,EACRrJ,EACI8B,EACAwJ,EAAcxJ,WACdyJ,EAAazJ,aAGrBC,cAAesH,EAAuBtH,GACtCC,WAAYqH,EACRrJ,EACIgC,EACAsJ,EAActJ,WACduJ,EAAavJ,aAGrB+E,aAAAA,IAKApD,EAAS8C,EAFIgF,EAAAA,QAAQvJ,EAAI0F,GAEmC,CAC5D5G,YAAAA,EACAsC,QAASwH,EACTnD,iBAAkBzF,IAStB,OANIrC,IACA8D,ECtMD,SACHA,GAEA,IACe9D,EACX8D,EADAL,QAAWzD,kBAeT6L,EAAoB,WAGxB,IAAA,IAAAzL,EAAAC,UAAAC,OADKgJ,EACL,IAAA9I,MAAAJ,GAAAK,EAAA,EAAAA,EAAAL,EAAAK,IADK6I,EACL7I,GAAAJ,UAAAI,GACE,IAAKT,EAAkBsJ,GACnB,OAAOxF,EAAOjD,MAAMC,KAAMwI,GAG9B,IAAMwC,EAAShI,EAAOzB,GAAGxB,MAAMC,KAAMwI,GAIrC,OAFAxF,EAAO6C,IAAI2C,EAAMwC,GAEVA,GAKX,OAFA9F,EAAqBlC,EAAQ+H,GAEtBA,CACV,CDiKgBE,CAAuCjI,IAGpD1B,EAAQ0B,EAASzB,EAAiBE,KAAMkB,EAAQ3D,aAEzCgE,CACV,SAWDgG,EAAMpF,WAAaA,EAWnBoF,EAAMkC,aNpMC,SAAsBzH,QAA0B,IAA1BA,IAAAA,GAAoB,GAC7CF,EAAWE,kBAAoBA,CAClC,EMgNDuF,EAAM/I,QAAU,WACZ,OAAOA,EAAAF,WAAA,EAAAR,YAA6ByJ,CACvC,EAaDA,EAAMmC,KAAOnC,EAAM,CAAE5K,aAAa,IAalC4K,EAAM3E,SAAWA,EAajB2E,EAAMoC,SAAWpC,EAAM,CAAElK,QAASuM,MAalCrC,EAAMvF,kBAAoB,WACtB,OAAOF,EAAWE,iBACrB,EAcDuF,EAAM1B,SAAW,SAAkB/F,GAC/B,MAAqB,mBAAPA,KAAuBA,EAAG+F,QAC3C,EAcD0B,EAAMvK,WAAa,SAAU6M,GACzB,OAAOtC,EAAM,CAAEvK,WAAY6M,GAC9B,EAcDtC,EAAMrK,WAAa,SAAU4M,GACzB,OAAOvC,EAAM,CAAErK,WAAY4M,GAC9B,EAqGDvC,EAAMpK,OApDN,SASIA,EACA4M,GAEA,IAAsB,IAAlBA,EACA,OAAOxC,EAAM,CACTpK,OAAAA,EACAQ,aAAcoM,IAItB,GAA6B,iBAAlBA,EAA4B,CACnC,IAAQzM,EAA2ByM,EAA3BzM,SAAUK,EAAiBoM,EAAjBpM,aAElB,OAAO4J,EAAM,CACTpK,OAAAA,EACAG,SAAAA,EACAK,aAAAA,GAEP,CAED,OACW4J,EADkB,mBAAlBwC,EACM,CACT5M,OAAAA,EACAG,SAAUyM,EACVpM,cAAc,GAIT,CAAER,OAAAA,GAClB,EA4BDoK,EAAMnK,QAAU,SAAiBA,GAC7B,OAAOmK,EAAM,CAAEnK,QAAAA,GAClB,EAcDmK,EAAMlK,QAAU,SAAiBA,GAC7B,OAAOkK,EAAM,CAAElK,QAAAA,GAClB,EAaDkK,EAAMvE,QAAU,SAAUzF,GACtB,OAAOgK,EAAM,CAAEhK,YAAAA,GAClB,EAaDgK,EAAMyC,QAAUzC,EAAM,CAClB3K,WAAW,EACXe,cAAc,IAclB4J,EAAM0C,MAAQ1C,EAAM,CAAE1K,SAAS,IAa/B0K,EAAM2C,UAAY3C,EAAM,CAAEzK,cAAc,IAcxCyK,EAAM4C,cAAgB,SAAU3M,GAC5B,OAAO+J,EAAM,CAAEzK,cAAc,EAAMU,WAAAA,GACtC,EAaD+J,EAAM6C,QAAU7C,EAAM,CAAExK,gBAAgB,IAcxCwK,EAAM7J,cAAgB,SAClBA,GADkB,OAEjB6J,EAAM,CAAE7J,cAAAA,GAFS,EAgBtB6J,EAAM9J,kBAAoB,SACtBA,GADsB,OAErB8J,EAAM,CAAE9J,kBAAAA,GAFa,EAM1BwC,OAAOC,eAAeqH,EAAO,UAAW,CACpCpH,cAAc,EACdC,YAAY,EACZC,MAAOkH,EACPjH,UAAU"}