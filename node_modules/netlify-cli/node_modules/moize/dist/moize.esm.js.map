{"version": 3, "file": "moize.esm.js", "sources": ["../src/constants.ts", "../src/utils.ts", "../src/maxAge.ts", "../src/stats.ts", "../src/instance.ts", "../src/component.ts", "../src/maxArgs.ts", "../src/serialize.ts", "../src/options.ts", "../src/updateCacheForKey.ts", "../src/index.ts"], "sourcesContent": ["import type { Options } from '../index.d';\n\n/**\n * @private\n *\n * @constant DEFAULT_OPTIONS\n */\nexport const DEFAULT_OPTIONS: Options = {\n    isDeepEqual: false,\n    isPromise: false,\n    isReact: false,\n    isSerialized: false,\n    isShallowEqual: false,\n    matchesArg: undefined,\n    matchesKey: undefined,\n    maxAge: undefined,\n    maxArgs: undefined,\n    maxSize: 1,\n    onExpire: undefined,\n    profileName: undefined,\n    serializer: undefined,\n    updateCacheForKey: undefined,\n    transformArgs: undefined,\n    updateExpire: false,\n};\n", "import { DEFAULT_OPTIONS } from './constants';\n\nimport type {\n    Expiration,\n    Fn,\n    <PERSON>E<PERSON>l,\n    IsMatching<PERSON>ey,\n    Key,\n    Moizeable,\n    Moized,\n    Options,\n} from '../index.d';\n\n/**\n * @private\n *\n * @description\n * method to combine functions and return a single function that fires them all\n *\n * @param functions the functions to compose\n * @returns the composed function\n */\nexport function combine<Arg, Result>(\n    ...functions: Fn<Arg>[]\n): Fn<Arg, Result> | undefined {\n    return functions.reduce(function (f: any, g: any) {\n        if (typeof f === 'function') {\n            return typeof g === 'function'\n                ? function (this: any) {\n                      f.apply(this, arguments);\n                      g.apply(this, arguments);\n                  }\n                : f;\n        }\n\n        if (typeof g === 'function') {\n            return g;\n        }\n    });\n}\n\n/**\n * @private\n *\n * @description\n * method to compose functions and return a single function\n *\n * @param functions the functions to compose\n * @returns the composed function\n */\nexport function compose<Method>(...functions: Method[]): Method {\n    return functions.reduce(function (f: any, g: any) {\n        if (typeof f === 'function') {\n            return typeof g === 'function'\n                ? function (this: any) {\n                      return f(g.apply(this, arguments));\n                  }\n                : f;\n        }\n\n        if (typeof g === 'function') {\n            return g;\n        }\n    });\n}\n\n/**\n * @private\n *\n * @description\n * find the index of the expiration based on the key\n *\n * @param expirations the list of expirations\n * @param key the key to match\n * @returns the index of the expiration\n */\nexport function findExpirationIndex(expirations: Expiration[], key: Key) {\n    for (let index = 0; index < expirations.length; index++) {\n        if (expirations[index].key === key) {\n            return index;\n        }\n    }\n\n    return -1;\n}\n\n/**\n * @private\n *\n * @description\n * create function that finds the index of the key in the list of cache keys\n *\n * @param isEqual the function to test individual argument equality\n * @param isMatchingKey the function to test full key equality\n * @returns the function that finds the index of the key\n */\nexport function createFindKeyIndex(\n    isEqual: IsEqual,\n    isMatchingKey: IsMatchingKey | undefined\n) {\n    const areKeysEqual: IsMatchingKey =\n        typeof isMatchingKey === 'function'\n            ? isMatchingKey\n            : function (cacheKey: Key, key: Key) {\n                  for (let index = 0; index < key.length; index++) {\n                      if (!isEqual(cacheKey[index], key[index])) {\n                          return false;\n                      }\n                  }\n\n                  return true;\n              };\n\n    return function (keys: Key[], key: Key) {\n        for (let keysIndex = 0; keysIndex < keys.length; keysIndex++) {\n            if (\n                keys[keysIndex].length === key.length &&\n                areKeysEqual(keys[keysIndex], key)\n            ) {\n                return keysIndex;\n            }\n        }\n\n        return -1;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * merge two options objects, combining or composing functions as necessary\n *\n * @param originalOptions the options that already exist on the method\n * @param newOptions the new options to merge\n * @returns the merged options\n */\nexport function mergeOptions(\n    originalOptions: Options,\n    newOptions: Options\n): Options {\n    return !newOptions || newOptions === DEFAULT_OPTIONS\n        ? originalOptions\n        : {\n              ...originalOptions,\n              ...newOptions,\n              onCacheAdd: combine(\n                  originalOptions.onCacheAdd,\n                  newOptions.onCacheAdd\n              ),\n              onCacheChange: combine(\n                  originalOptions.onCacheChange,\n                  newOptions.onCacheChange\n              ),\n              onCacheHit: combine(\n                  originalOptions.onCacheHit,\n                  newOptions.onCacheHit\n              ),\n              transformArgs: compose(\n                  originalOptions.transformArgs,\n                  newOptions.transformArgs\n              ),\n          };\n}\n\nexport function isMoized(fn: Moizeable | Moized | Options): fn is Moized {\n    return typeof fn === 'function' && (fn as Moizeable).isMoized;\n}\n\nexport function setName(\n    fn: Moized,\n    originalFunctionName: string,\n    profileName: string\n) {\n    try {\n        const name = profileName || originalFunctionName || 'anonymous';\n\n        Object.defineProperty(fn, 'name', {\n            configurable: true,\n            enumerable: false,\n            value: `moized(${name})`,\n            writable: true,\n        });\n    } catch {\n        // For engines where `function.name` is not configurable, do nothing.\n    }\n}\n", "import { createFindKeyIndex, findExpirationIndex } from './utils';\n\nimport type {\n    Cache,\n    Expiration,\n    Fn,\n    IsEqual,\n    IsMatching<PERSON>ey,\n    Key,\n    OnCacheOperation,\n    Options,\n} from '../index.d';\n\n/**\n * @private\n *\n * @description\n * clear an active expiration and remove it from the list if applicable\n *\n * @param expirations the list of expirations\n * @param key the key to clear\n * @param shouldRemove should the expiration be removed from the list\n */\nexport function clearExpiration(\n    expirations: Expiration[],\n    key: Key,\n    shouldRemove?: boolean\n) {\n    const expirationIndex = findExpirationIndex(expirations, key);\n\n    if (expirationIndex !== -1) {\n        clearTimeout(expirations[expirationIndex].timeoutId);\n\n        if (shouldRemove) {\n            expirations.splice(expirationIndex, 1);\n        }\n    }\n}\n\n/**\n * @private\n *\n * @description\n * Create the timeout for the given expiration method. If the ability to `unref`\n * exists, then apply it to avoid process locks in NodeJS.\n *\n * @param expirationMethod the method to fire upon expiration\n * @param maxAge the time to expire after\n * @returns the timeout ID\n */\nexport function createTimeout(expirationMethod: () => void, maxAge: number) {\n    const timeoutId = setTimeout(expirationMethod, maxAge);\n\n    if (typeof timeoutId.unref === 'function') {\n        timeoutId.unref();\n    }\n\n    return timeoutId;\n}\n\n/**\n * @private\n *\n * @description\n * create a function that, when an item is added to the cache, adds an expiration for it\n *\n * @param expirations the mutable expirations array\n * @param options the options passed on initialization\n * @param isEqual the function to check argument equality\n * @param isMatchingKey the function to check complete key equality\n * @returns the onCacheAdd function to handle expirations\n */\nexport function createOnCacheAddSetExpiration(\n    expirations: Expiration[],\n    options: Options,\n    isEqual: IsEqual,\n    isMatchingKey: IsMatchingKey\n): OnCacheOperation {\n    const { maxAge } = options;\n\n    return function onCacheAdd(\n        cache: Cache,\n        moizedOptions: Options,\n        moized: Fn\n    ) {\n        const key: any = cache.keys[0];\n\n        if (findExpirationIndex(expirations, key) === -1) {\n            const expirationMethod = function () {\n                const findKeyIndex = createFindKeyIndex(isEqual, isMatchingKey);\n\n                const keyIndex: number = findKeyIndex(cache.keys, key);\n                const value: any = cache.values[keyIndex];\n\n                if (~keyIndex) {\n                    cache.keys.splice(keyIndex, 1);\n                    cache.values.splice(keyIndex, 1);\n\n                    if (typeof options.onCacheChange === 'function') {\n                        options.onCacheChange(cache, moizedOptions, moized);\n                    }\n                }\n\n                clearExpiration(expirations, key, true);\n\n                if (\n                    typeof options.onExpire === 'function' &&\n                    options.onExpire(key) === false\n                ) {\n                    cache.keys.unshift(key);\n                    cache.values.unshift(value);\n\n                    onCacheAdd(cache, moizedOptions, moized);\n\n                    if (typeof options.onCacheChange === 'function') {\n                        options.onCacheChange(cache, moizedOptions, moized);\n                    }\n                }\n            };\n\n            expirations.push({\n                expirationMethod,\n                key,\n                timeoutId: createTimeout(expirationMethod, maxAge),\n            });\n        }\n    };\n}\n\n/**\n * @private\n *\n * @description\n * creates a function that, when a cache item is hit, reset the expiration\n *\n * @param expirations the mutable expirations array\n * @param options the options passed on initialization\n * @returns the onCacheAdd function to handle expirations\n */\nexport function createOnCacheHitResetExpiration(\n    expirations: Expiration[],\n    options: Options\n): OnCacheOperation {\n    return function onCacheHit(cache: Cache) {\n        const key = cache.keys[0];\n        const expirationIndex = findExpirationIndex(expirations, key);\n\n        if (~expirationIndex) {\n            clearExpiration(expirations, key, false);\n\n            expirations[expirationIndex].timeoutId = createTimeout(\n                expirations[expirationIndex].expirationMethod,\n                options.maxAge\n            );\n        }\n    };\n}\n\n/**\n * @private\n *\n * @description\n * get the micro-memoize options specific to the maxAge option\n *\n * @param expirations the expirations for the memoized function\n * @param options the options passed to the moizer\n * @param isEqual the function to test equality of the key on a per-argument basis\n * @param isMatchingKey the function to test equality of the whole key\n * @returns the object of options based on the entries passed\n */\nexport function getMaxAgeOptions(\n    expirations: Expiration[],\n    options: Options,\n    isEqual: IsEqual,\n    isMatchingKey: IsMatchingKey\n): {\n    onCacheAdd: OnCacheOperation | undefined;\n    onCacheHit: OnCacheOperation | undefined;\n} {\n    const onCacheAdd =\n        typeof options.maxAge === 'number' && isFinite(options.maxAge)\n            ? createOnCacheAddSetExpiration(\n                  expirations,\n                  options,\n                  isEqual,\n                  isMatchingKey\n              )\n            : undefined;\n\n    return {\n        onCacheAdd,\n        onCacheHit:\n            onCacheAdd && options.updateExpire\n                ? createOnCacheHitResetExpiration(expirations, options)\n                : undefined,\n    };\n}\n", "import type {\n    Fn,\n    FunctionalComponent,\n    GlobalStatsObject,\n    OnCacheOperation,\n    Options,\n    StatsCache,\n    StatsProfile,\n} from '../index.d';\n\nexport const statsCache: StatsCache = {\n    anonymousProfileNameCounter: 1,\n    isCollectingStats: false,\n    profiles: {},\n};\n\nlet hasWarningDisplayed = false;\n\nexport function clearStats(profileName?: string) {\n    if (profileName) {\n        delete statsCache.profiles[profileName];\n    } else {\n        statsCache.profiles = {};\n    }\n}\n\n/**\n * @private\n *\n * @description\n * activate stats collection\n *\n * @param isCollectingStats should stats be collected\n */\nexport function collectStats(isCollectingStats = true) {\n    statsCache.isCollectingStats = isCollectingStats;\n}\n\n/**\n * @private\n *\n * @description\n * create a function that increments the number of calls for the specific profile\n */\nexport function createOnCacheAddIncrementCalls(options: Options) {\n    const { profileName } = options;\n\n    return function () {\n        if (profileName && !statsCache.profiles[profileName]) {\n            statsCache.profiles[profileName] = {\n                calls: 0,\n                hits: 0,\n            };\n        }\n\n        statsCache.profiles[profileName].calls++;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * create a function that increments the number of calls and cache hits for the specific profile\n */\nexport function createOnCacheHitIncrementCallsAndHits(options: Options) {\n    return function () {\n        const { profiles } = statsCache;\n        const { profileName } = options;\n\n        if (!profiles[profileName]) {\n            profiles[profileName] = {\n                calls: 0,\n                hits: 0,\n            };\n        }\n\n        profiles[profileName].calls++;\n        profiles[profileName].hits++;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * get the profileName for the function when one is not provided\n *\n * @param fn the function to be memoized\n * @returns the derived profileName for the function\n */\nexport function getDefaultProfileName(\n    fn: Fn | FunctionalComponent<Record<string, unknown>>\n) {\n    return (\n        (fn as FunctionalComponent<Record<string, unknown>>).displayName ||\n        fn.name ||\n        `Anonymous ${statsCache.anonymousProfileNameCounter++}`\n    );\n}\n\n/**\n * @private\n *\n * @description\n * get the usage percentage based on the number of hits and total calls\n *\n * @param calls the number of calls made\n * @param hits the number of cache hits when called\n * @returns the usage as a percentage string\n */\nexport function getUsagePercentage(calls: number, hits: number) {\n    return calls ? `${((hits / calls) * 100).toFixed(4)}%` : '0.0000%';\n}\n\n/**\n * @private\n *\n * @description\n * get the statistics for a given method or all methods\n *\n * @param [profileName] the profileName to get the statistics for (get all when not provided)\n * @returns the object with stats information\n */\nexport function getStats(profileName?: string): GlobalStatsObject {\n    if (!statsCache.isCollectingStats && !hasWarningDisplayed) {\n        console.warn(\n            'Stats are not currently being collected, please run \"collectStats\" to enable them.'\n        ); // eslint-disable-line no-console\n\n        hasWarningDisplayed = true;\n    }\n\n    const { profiles } = statsCache;\n\n    if (profileName) {\n        if (!profiles[profileName]) {\n            return {\n                calls: 0,\n                hits: 0,\n                usage: '0.0000%',\n            };\n        }\n\n        const { [profileName]: profile } = profiles;\n\n        return {\n            ...profile,\n            usage: getUsagePercentage(profile.calls, profile.hits),\n        };\n    }\n\n    const completeStats: StatsProfile = Object.keys(statsCache.profiles).reduce(\n        (completeProfiles, profileName) => {\n            completeProfiles.calls += profiles[profileName].calls;\n            completeProfiles.hits += profiles[profileName].hits;\n\n            return completeProfiles;\n        },\n        {\n            calls: 0,\n            hits: 0,\n        }\n    );\n\n    return {\n        ...completeStats,\n        profiles: Object.keys(profiles).reduce(\n            (computedProfiles, profileName) => {\n                computedProfiles[profileName] = getStats(profileName);\n\n                return computedProfiles;\n            },\n            {} as Record<string, StatsProfile>\n        ),\n        usage: getUsagePercentage(completeStats.calls, completeStats.hits),\n    };\n}\n\n/**\n * @private\n *\n * @function getStatsOptions\n *\n * @description\n * get the options specific to storing statistics\n *\n * @param {Options} options the options passed to the moizer\n * @returns {Object} the options specific to keeping stats\n */\nexport function getStatsOptions(options: Options): {\n    onCacheAdd?: OnCacheOperation;\n    onCacheHit?: OnCacheOperation;\n} {\n    return statsCache.isCollectingStats\n        ? {\n              onCacheAdd: createOnCacheAddIncrementCalls(options),\n              onCacheHit: createOnCacheHitIncrementCallsAndHits(options),\n          }\n        : {};\n}\n", "import { clearExpiration } from './maxAge';\nimport { clearStats, getStats } from './stats';\nimport { createFindKeyIndex } from './utils';\n\nimport type {\n    Fn,\n    Key,\n    Memoized,\n    Moizeable,\n    MoizeConfiguration,\n    Moized,\n    Options,\n    StatsProfile,\n} from '../index.d';\n\nconst ALWAYS_SKIPPED_PROPERTIES: Record<string, boolean> = {\n    arguments: true,\n    callee: true,\n    caller: true,\n    constructor: true,\n    length: true,\n    name: true,\n    prototype: true,\n};\n\n/**\n * @private\n *\n * @description\n * copy the static properties from the original function to the moized\n * function\n *\n * @param originalFn the function copying from\n * @param newFn the function copying to\n * @param skippedProperties the list of skipped properties, if any\n */\nexport function copyStaticProperties(\n    originalFn: Fn,\n    newFn: Fn,\n    skippedProperties: string[] = []\n) {\n    Object.getOwnPropertyNames(originalFn).forEach((property) => {\n        if (\n            !ALWAYS_SKIPPED_PROPERTIES[property] &&\n            skippedProperties.indexOf(property) === -1\n        ) {\n            const descriptor = Object.getOwnPropertyDescriptor(\n                originalFn,\n                property\n            );\n\n            if (descriptor.get || descriptor.set) {\n                Object.defineProperty(newFn, property, descriptor);\n            } else {\n                newFn[property as keyof typeof newFn] =\n                    originalFn[property as keyof typeof originalFn];\n            }\n        }\n    });\n}\n\n/**\n * @private\n *\n * @description\n * add methods to the moized fuction object that allow extra features\n *\n * @param memoized the memoized function from micro-memoize\n */\nexport function addInstanceMethods<OriginalFn extends Fn>(\n    memoized: Moizeable,\n    { expirations }: MoizeConfiguration<OriginalFn>\n) {\n    const { options } = memoized;\n\n    const findKeyIndex = createFindKeyIndex(\n        options.isEqual,\n        options.isMatchingKey\n    );\n\n    const moized = memoized as unknown as Moized<OriginalFn, Options>;\n\n    moized.clear = function () {\n        const {\n            _microMemoizeOptions: { onCacheChange },\n            cache,\n        } = moized;\n\n        cache.keys.length = 0;\n        cache.values.length = 0;\n\n        if (onCacheChange) {\n            onCacheChange(cache, moized.options, moized);\n        }\n\n        return true;\n    };\n\n    moized.clearStats = function () {\n        clearStats(moized.options.profileName);\n    };\n\n    moized.get = function (key: Key) {\n        const {\n            _microMemoizeOptions: { transformKey },\n            cache,\n        } = moized;\n\n        const cacheKey = transformKey ? transformKey(key) : key;\n        const keyIndex = findKeyIndex(cache.keys, cacheKey);\n\n        return keyIndex !== -1 ? moized.apply(this, key) : undefined;\n    };\n\n    moized.getStats = function (): StatsProfile {\n        return getStats(moized.options.profileName);\n    };\n\n    moized.has = function (key: Key) {\n        const { transformKey } = moized._microMemoizeOptions;\n\n        const cacheKey = transformKey ? transformKey(key) : key;\n\n        return findKeyIndex(moized.cache.keys, cacheKey) !== -1;\n    };\n\n    moized.keys = function () {\n        return moized.cacheSnapshot.keys;\n    };\n\n    moized.remove = function (key: Key) {\n        const {\n            _microMemoizeOptions: { onCacheChange, transformKey },\n            cache,\n        } = moized;\n\n        const keyIndex = findKeyIndex(\n            cache.keys,\n            transformKey ? transformKey(key) : key\n        );\n\n        if (keyIndex === -1) {\n            return false;\n        }\n\n        const existingKey = cache.keys[keyIndex];\n\n        cache.keys.splice(keyIndex, 1);\n        cache.values.splice(keyIndex, 1);\n\n        if (onCacheChange) {\n            onCacheChange(cache, moized.options, moized);\n        }\n\n        clearExpiration(expirations, existingKey, true);\n\n        return true;\n    };\n\n    moized.set = function (key: Key, value: any) {\n        const { _microMemoizeOptions, cache, options } = moized;\n        const { onCacheAdd, onCacheChange, transformKey } =\n            _microMemoizeOptions;\n\n        const cacheKey = transformKey ? transformKey(key) : key;\n        const keyIndex = findKeyIndex(cache.keys, cacheKey);\n\n        if (keyIndex === -1) {\n            const cutoff = options.maxSize - 1;\n\n            if (cache.size > cutoff) {\n                cache.keys.length = cutoff;\n                cache.values.length = cutoff;\n            }\n\n            cache.keys.unshift(cacheKey);\n            cache.values.unshift(value);\n\n            if (options.isPromise) {\n                cache.updateAsyncCache(moized);\n            }\n\n            if (onCacheAdd) {\n                onCacheAdd(cache, options, moized);\n            }\n\n            if (onCacheChange) {\n                onCacheChange(cache, options, moized);\n            }\n        } else {\n            const existingKey = cache.keys[keyIndex];\n\n            cache.values[keyIndex] = value;\n\n            if (keyIndex > 0) {\n                cache.orderByLru(existingKey, value, keyIndex);\n            }\n\n            if (options.isPromise) {\n                cache.updateAsyncCache(moized);\n            }\n\n            if (typeof onCacheChange === 'function') {\n                onCacheChange(cache, options, moized);\n            }\n        }\n    };\n\n    moized.values = function () {\n        return moized.cacheSnapshot.values;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * add propeties to the moized fuction object that surfaces extra information\n *\n * @param memoized the memoized function\n * @param expirations the list of expirations for cache items\n * @param options the options passed to the moizer\n * @param originalFunction the function that is being memoized\n */\nexport function addInstanceProperties<OriginalFn extends Moizeable>(\n    memoized: Memoized<OriginalFn>,\n    {\n        expirations,\n        options: moizeOptions,\n        originalFunction,\n    }: MoizeConfiguration<OriginalFn>\n) {\n    const { options: microMemoizeOptions } = memoized;\n\n    Object.defineProperties(memoized, {\n        _microMemoizeOptions: {\n            configurable: true,\n            get() {\n                return microMemoizeOptions;\n            },\n        },\n\n        cacheSnapshot: {\n            configurable: true,\n            get() {\n                const { cache: currentCache } = memoized;\n\n                return {\n                    keys: currentCache.keys.slice(0),\n                    size: currentCache.size,\n                    values: currentCache.values.slice(0),\n                };\n            },\n        },\n\n        expirations: {\n            configurable: true,\n            get() {\n                return expirations;\n            },\n        },\n\n        expirationsSnapshot: {\n            configurable: true,\n            get() {\n                return expirations.slice(0);\n            },\n        },\n\n        isMoized: {\n            configurable: true,\n            get() {\n                return true;\n            },\n        },\n\n        options: {\n            configurable: true,\n            get() {\n                return moizeOptions;\n            },\n        },\n\n        originalFunction: {\n            configurable: true,\n            get() {\n                return originalFunction;\n            },\n        },\n    });\n\n    const moized = memoized as unknown as Moized<OriginalFn, Options>;\n\n    copyStaticProperties(originalFunction, moized);\n}\n\n/**\n * @private\n *\n * @description\n * add methods and properties to the memoized function for more features\n *\n * @param memoized the memoized function\n * @param configuration the configuration object for the instance\n * @returns the memoized function passed\n */\nexport function createMoizeInstance<\n    OriginalFn extends Moizeable,\n    CombinedOptions extends Options\n>(\n    memoized: Memoized<OriginalFn>,\n    configuration: MoizeConfiguration<OriginalFn>\n) {\n    addInstanceMethods<OriginalFn>(memoized, configuration);\n    addInstanceProperties<OriginalFn>(memoized, configuration);\n\n    return memoized as Moized<OriginalFn, CombinedOptions>;\n}\n", "import { copyStaticProperties } from './instance';\nimport { setName } from './utils';\n\nimport type {\n    Moize,\n    Moized as MoizedFunction,\n    Moizeable,\n    Options,\n} from '../index.d';\n\n// This was stolen from React internals, which allows us to create React elements without needing\n// a dependency on the React library itself.\nconst REACT_ELEMENT_TYPE =\n    typeof Symbol === 'function' && Symbol.for\n        ? Symbol.for('react.element')\n        : 0xeac7;\n\n/**\n * @private\n *\n * @description\n * Create a component that memoizes based on `props` and legacy `context`\n * on a per-instance basis. This requires creating a component class to\n * store the memoized function. The cost is quite low, and avoids the\n * need to have access to the React dependency by basically re-creating\n * the basic essentials for a component class and the results of the\n * `createElement` function.\n *\n * @param moizer the top-level moize method\n * @param fn the component to memoize\n * @param options the memoization options\n * @returns the memoized component\n */\nexport function createMoizedComponent<OriginalFn extends Moizeable>(\n    moizer: Moize,\n    fn: OriginalFn,\n    options: Options\n) {\n    /**\n     * This is a hack override setting the necessary options\n     * for a React component to be memoized. In the main `moize`\n     * method, if the `isReact` option is set it is short-circuited\n     * to call this function, and these overrides allow the\n     * necessary transformKey method to be derived.\n     *\n     * The order is based on:\n     * 1) Set the necessary aspects of transformKey for React components.\n     * 2) Allow setting of other options and overrides of those aspects\n     *    if desired (for example, `isDeepEqual` will use deep equality).\n     * 3) Always set `isReact` to false to prevent infinite loop.\n     */\n    const reactMoizer = moizer({\n        maxArgs: 2,\n        isShallowEqual: true,\n        ...options,\n        isReact: false,\n    });\n\n    if (!fn.displayName) {\n        // @ts-ignore - allow setting of displayName\n        fn.displayName = fn.name || 'Component';\n    }\n\n    function Moized<Props extends Record<string, unknown>, Context, Updater>(\n        this: any,\n        props: Props,\n        context: Context,\n        updater: Updater\n    ) {\n        this.props = props;\n        this.context = context;\n        this.updater = updater;\n\n        this.MoizedComponent = reactMoizer(fn);\n    }\n\n    Moized.prototype.isReactComponent = {};\n\n    Moized.prototype.render = function () {\n        return {\n            $$typeof: REACT_ELEMENT_TYPE,\n            type: this.MoizedComponent,\n            props: this.props,\n            ref: null,\n            key: null,\n            _owner: null,\n        } as JSX.Element;\n    };\n\n    copyStaticProperties(fn, Moized, ['contextType', 'contextTypes']);\n\n    Moized.displayName = `Moized(${fn.displayName || fn.name || 'Component'})`;\n\n    setName(Moized as MoizedFunction, fn.name, options.profileName);\n\n    return Moized;\n}\n", "import type { Key } from '../index.d';\n\nexport function createGetInitialArgs(size: number) {\n    /**\n     * @private\n     *\n     * @description\n     * take the first N number of items from the array (faster than slice)\n     *\n     * @param args the args to take from\n     * @returns the shortened list of args as an array\n     */\n    return function (args: Key): Key {\n        if (size >= args.length) {\n            return args;\n        }\n\n        if (size === 0) {\n            return [];\n        }\n\n        if (size === 1) {\n            return [args[0]];\n        }\n\n        if (size === 2) {\n            return [args[0], args[1]];\n        }\n\n        if (size === 3) {\n            return [args[0], args[1], args[2]];\n        }\n\n        const clone = [];\n\n        for (let index = 0; index < size; index++) {\n            clone[index] = args[index];\n        }\n\n        return clone;\n    };\n}\n", "import type { Key, Options } from '../index.d';\n\n/**\n * @function getCutoff\n *\n * @description\n * faster `Array.prototype.indexOf` implementation build for slicing / splicing\n *\n * @param array the array to match the value in\n * @param value the value to match\n * @returns the matching index, or -1\n */\nfunction getCutoff(array: any[], value: any) {\n    const { length } = array;\n\n    for (let index = 0; index < length; ++index) {\n        if (array[index] === value) {\n            return index + 1;\n        }\n    }\n\n    return 0;\n}\n\n/**\n * @private\n *\n * @description\n * custom replacer for the stringify function\n *\n * @returns if function then toString of it, else the value itself\n */\nexport function createDefaultReplacer() {\n    const cache: any[] = [];\n    const keys: string[] = [];\n\n    return function defaultReplacer(key: string, value: any) {\n        const type = typeof value;\n\n        if (type === 'function' || type === 'symbol') {\n            return value.toString();\n        }\n\n        if (typeof value === 'object') {\n            if (cache.length) {\n                const thisCutoff = getCutoff(cache, this);\n\n                if (thisCutoff === 0) {\n                    cache[cache.length] = this;\n                } else {\n                    cache.splice(thisCutoff);\n                    keys.splice(thisCutoff);\n                }\n\n                keys[keys.length] = key;\n\n                const valueCutoff = getCutoff(cache, value);\n\n                if (valueCutoff !== 0) {\n                    return `[ref=${\n                        keys.slice(0, valueCutoff).join('.') || '.'\n                    }]`;\n                }\n            } else {\n                cache[0] = value;\n                keys[0] = key;\n            }\n\n            return value;\n        }\n\n        return '' + value;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * get the stringified version of the argument passed\n *\n * @param arg argument to stringify\n * @returns the stringified argument\n */\nexport function getStringifiedArgument<Type>(arg: Type) {\n    const typeOfArg = typeof arg;\n\n    return arg && (typeOfArg === 'object' || typeOfArg === 'function')\n        ? JSON.stringify(arg, createDefaultReplacer())\n        : arg;\n}\n\n/**\n * @private\n *\n * @description\n * serialize the arguments passed\n *\n * @param options the options passed to the moizer\n * @param options.maxArgs the cap on the number of arguments used in serialization\n * @returns argument serialization method\n */\nexport function defaultArgumentSerializer(args: Key) {\n    let key = '|';\n\n    for (let index = 0; index < args.length; index++) {\n        key += getStringifiedArgument(args[index]) + '|';\n    }\n\n    return [key];\n}\n\n/**\n * @private\n *\n * @description\n * based on the options passed, either use the serializer passed or generate the internal one\n *\n * @param options the options passed to the moized function\n * @returns the function to use in serializing the arguments\n */\nexport function getSerializerFunction(options: Options) {\n    return typeof options.serializer === 'function'\n        ? options.serializer\n        : defaultArgumentSerializer;\n}\n\n/**\n * @private\n *\n * @description\n * are the serialized keys equal to one another\n *\n * @param cacheKey the cache key to compare\n * @param key the key to test\n * @returns are the keys equal\n */\nexport function getIsSerializedKeyEqual(cacheKey: Key, key: Key) {\n    return cacheKey[0] === key[0];\n}\n", "import { deepEqual, sameValueZeroEqual, shallowEqual } from 'fast-equals';\nimport { createGetInitialArgs } from './maxArgs';\nimport { getIsSerializedKeyEqual, getSerializerFunction } from './serialize';\nimport { compose } from './utils';\n\nimport type {\n    Cache,\n    IsEqual,\n    IsMatchingKey,\n    MicroMemoizeOptions,\n    Moized,\n    OnCacheOperation,\n    Options,\n    TransformKey,\n} from '../index.d';\n\nexport function createOnCacheOperation(\n    fn?: OnCacheOperation\n): OnCacheOperation {\n    if (typeof fn === 'function') {\n        return (\n            _cacheIgnored: Cache,\n            _microMemoizeOptionsIgnored: MicroMemoizeOptions,\n            memoized: Moized\n        ): void => fn(memoized.cache, memoized.options, memoized);\n    }\n}\n\n/**\n * @private\n *\n * @description\n * get the isEqual method passed to micro-memoize\n *\n * @param options the options passed to the moizer\n * @returns the isEqual method to apply\n */\nexport function getIsEqual(options: Options): IsEqual {\n    return (\n        options.matchesArg ||\n        (options.isDeepEqual && deepEqual) ||\n        (options.isShallowEqual && shallowEqual) ||\n        sameValueZeroEqual\n    );\n}\n\n/**\n * @private\n *\n * @description\n * get the isEqual method passed to micro-memoize\n *\n * @param options the options passed to the moizer\n * @returns the isEqual method to apply\n */\nexport function getIsMatchingKey(options: Options): IsMatchingKey | undefined {\n    return (\n        options.matchesKey ||\n        (options.isSerialized && getIsSerializedKeyEqual) ||\n        undefined\n    );\n}\n\n/**\n * @private\n *\n * @description\n * get the function that will transform the key based on the arguments passed\n *\n * @param options the options passed to the moizer\n * @returns the function to transform the key with\n */\nexport function getTransformKey(options: Options): TransformKey | undefined {\n    return compose(\n        options.isSerialized && getSerializerFunction(options),\n        typeof options.transformArgs === 'function' && options.transformArgs,\n        typeof options.maxArgs === 'number' &&\n            createGetInitialArgs(options.maxArgs)\n    ) as TransformKey;\n}\n", "import { copyStaticProperties } from './instance';\n\nimport type { Moized } from '../index.d';\n\nexport function createRefreshableMoized<MoizedFn extends Moized>(\n    moized: MoizedFn\n) {\n    const {\n        options: { updateCacheForKey },\n    } = moized;\n\n    /**\n     * @private\n     *\n     * @description\n     * Wrapper around already-`moize`d function which will intercept the memoization\n     * and call the underlying function directly with the purpose of updating the cache\n     * for the given key.\n     *\n     * Promise values use a tweak of the logic that exists at cache.updateAsyncCache, which\n     * reverts to the original value if the promise is rejected and there was already a cached\n     * value.\n     */\n    const refreshableMoized = function refreshableMoized(\n        this: any,\n        ...args: Parameters<typeof moized.fn>\n    ) {\n        if (!updateCacheForKey(args)) {\n            return moized.apply(this, args);\n        }\n\n        const result = moized.fn.apply(this, args);\n\n        moized.set(args, result);\n\n        return result;\n    } as typeof moized;\n\n    copyStaticProperties(moized, refreshableMoized);\n\n    return refreshableMoized;\n}\n", "import memoize from 'micro-memoize';\nimport { createMoizedComponent } from './component';\nimport { DEFAULT_OPTIONS } from './constants';\nimport { createMoizeInstance } from './instance';\nimport { getMaxAgeOptions } from './maxAge';\nimport {\n    createOnCacheOperation,\n    getIsEqual,\n    getIsMatchingKey,\n    getTransformKey,\n} from './options';\nimport {\n    clearStats,\n    collectStats,\n    getDefaultProfileName,\n    getStats,\n    getStatsOptions,\n    statsCache,\n} from './stats';\nimport { createRefreshableMoized } from './updateCacheForKey';\nimport { combine, compose, isMoized, mergeOptions, setName } from './utils';\n\nimport type {\n    Expiration,\n    IsEqual,\n    IsMatchingKey,\n    MicroMemoizeOptions,\n    Moize,\n    Moizeable,\n    Moized,\n    OnExpire,\n    Options,\n    Serialize,\n    TransformKey,\n    UpdateCacheForKey,\n} from '../index.d';\n\n/**\n * @module moize\n */\n\n/**\n * @description\n * memoize a function based its arguments passed, potentially improving runtime performance\n *\n * @example\n * import moize from 'moize';\n *\n * // standard implementation\n * const fn = (foo, bar) => `${foo} ${bar}`;\n * const memoizedFn = moize(fn);\n *\n * // implementation with options\n * const fn = async (id) => get(`http://foo.com/${id}`);\n * const memoizedFn = moize(fn, {isPromise: true, maxSize: 5});\n *\n * // implementation with convenience methods\n * const Foo = ({foo}) => <div>{foo}</div>;\n * const MemoizedFoo = moize.react(Foo);\n *\n * @param fn the function to memoized, or a list of options when currying\n * @param [options=DEFAULT_OPTIONS] the options to apply\n * @returns the memoized function\n */\nconst moize: Moize = function <\n    Fn extends Moizeable,\n    PassedOptions extends Options\n>(fn: Fn | PassedOptions, passedOptions?: PassedOptions) {\n    type CombinedOptions = Options & PassedOptions;\n\n    const options: Options = passedOptions || DEFAULT_OPTIONS;\n\n    if (isMoized(fn)) {\n        const moizeable = fn.originalFunction as Fn;\n        const mergedOptions = mergeOptions(\n            fn.options,\n            options\n        ) as CombinedOptions;\n\n        return moize<Fn, CombinedOptions>(moizeable, mergedOptions);\n    }\n\n    if (typeof fn === 'object') {\n        return function <\n            CurriedFn extends Moizeable,\n            CurriedOptions extends Options\n        >(\n            curriedFn: CurriedFn | CurriedOptions,\n            curriedOptions: CurriedOptions\n        ) {\n            type CombinedCurriedOptions = CombinedOptions & CurriedOptions;\n\n            if (typeof curriedFn === 'function') {\n                const mergedOptions = mergeOptions(\n                    fn as CombinedOptions,\n                    curriedOptions\n                ) as CombinedCurriedOptions;\n\n                return moize(curriedFn, mergedOptions);\n            }\n\n            const mergedOptions = mergeOptions(\n                fn as CombinedOptions,\n                curriedFn as CurriedOptions\n            );\n\n            return moize(mergedOptions);\n        };\n    }\n\n    if (options.isReact) {\n        return createMoizedComponent(moize, fn, options);\n    }\n\n    const coalescedOptions: Options = {\n        ...DEFAULT_OPTIONS,\n        ...options,\n        maxAge:\n            typeof options.maxAge === 'number' && options.maxAge >= 0\n                ? options.maxAge\n                : DEFAULT_OPTIONS.maxAge,\n        maxArgs:\n            typeof options.maxArgs === 'number' && options.maxArgs >= 0\n                ? options.maxArgs\n                : DEFAULT_OPTIONS.maxArgs,\n        maxSize:\n            typeof options.maxSize === 'number' && options.maxSize >= 0\n                ? options.maxSize\n                : DEFAULT_OPTIONS.maxSize,\n        profileName: options.profileName || getDefaultProfileName(fn),\n    };\n    const expirations: Array<Expiration> = [];\n\n    const {\n        matchesArg: equalsIgnored,\n        isDeepEqual: isDeepEqualIgnored,\n        isPromise,\n        isReact: isReactIgnored,\n        isSerialized: isSerialzedIgnored,\n        isShallowEqual: isShallowEqualIgnored,\n        matchesKey: matchesKeyIgnored,\n        maxAge: maxAgeIgnored,\n        maxArgs: maxArgsIgnored,\n        maxSize,\n        onCacheAdd,\n        onCacheChange,\n        onCacheHit,\n        onExpire: onExpireIgnored,\n        profileName: profileNameIgnored,\n        serializer: serializerIgnored,\n        updateCacheForKey,\n        transformArgs: transformArgsIgnored,\n        updateExpire: updateExpireIgnored,\n        ...customOptions\n    } = coalescedOptions;\n\n    const isEqual = getIsEqual(coalescedOptions);\n    const isMatchingKey = getIsMatchingKey(coalescedOptions);\n\n    const maxAgeOptions = getMaxAgeOptions(\n        expirations,\n        coalescedOptions,\n        isEqual,\n        isMatchingKey\n    );\n    const statsOptions = getStatsOptions(coalescedOptions);\n\n    const transformKey = getTransformKey(coalescedOptions);\n\n    const microMemoizeOptions: MicroMemoizeOptions = {\n        ...customOptions,\n        isEqual,\n        isMatchingKey,\n        isPromise,\n        maxSize,\n        onCacheAdd: createOnCacheOperation(\n            combine(\n                onCacheAdd,\n                maxAgeOptions.onCacheAdd,\n                statsOptions.onCacheAdd\n            )\n        ),\n        onCacheChange: createOnCacheOperation(onCacheChange),\n        onCacheHit: createOnCacheOperation(\n            combine(\n                onCacheHit,\n                maxAgeOptions.onCacheHit,\n                statsOptions.onCacheHit\n            )\n        ),\n        transformKey,\n    };\n\n    const memoized = memoize(fn, microMemoizeOptions);\n\n    let moized = createMoizeInstance<Fn, CombinedOptions>(memoized, {\n        expirations,\n        options: coalescedOptions,\n        originalFunction: fn,\n    });\n\n    if (updateCacheForKey) {\n        moized = createRefreshableMoized<typeof moized>(moized);\n    }\n\n    setName(moized, (fn as Moizeable).name, options.profileName);\n\n    return moized;\n};\n\n/**\n * @function\n * @name clearStats\n * @memberof module:moize\n * @alias moize.clearStats\n *\n * @description\n * clear all existing stats stored\n */\nmoize.clearStats = clearStats;\n\n/**\n * @function\n * @name collectStats\n * @memberof module:moize\n * @alias moize.collectStats\n *\n * @description\n * start collecting statistics\n */\nmoize.collectStats = collectStats;\n\n/**\n * @function\n * @name compose\n * @memberof module:moize\n * @alias moize.compose\n *\n * @description\n * method to compose moized methods and return a single moized function\n *\n * @param moized the functions to compose\n * @returns the composed function\n */\nmoize.compose = function (...moized: Moize[]) {\n    return compose<Moize>(...moized) || moize;\n};\n\n/**\n * @function\n * @name deep\n * @memberof module:moize\n * @alias moize.deep\n *\n * @description\n * should deep equality check be used\n *\n * @returns the moizer function\n */\nmoize.deep = moize({ isDeepEqual: true });\n\n/**\n * @function\n * @name getStats\n * @memberof module:moize\n * @alias moize.getStats\n *\n * @description\n * get the statistics of a given profile, or overall usage\n *\n * @returns statistics for a given profile or overall usage\n */\nmoize.getStats = getStats;\n\n/**\n * @function\n * @name infinite\n * @memberof module:moize\n * @alias moize.infinite\n *\n * @description\n * a moized method that will remove all limits from the cache size\n *\n * @returns the moizer function\n */\nmoize.infinite = moize({ maxSize: Infinity });\n\n/**\n * @function\n * @name isCollectingStats\n * @memberof module:moize\n * @alias moize.isCollectingStats\n *\n * @description\n * are stats being collected\n *\n * @returns are stats being collected\n */\nmoize.isCollectingStats = function isCollectingStats(): boolean {\n    return statsCache.isCollectingStats;\n};\n\n/**\n * @function\n * @name isMoized\n * @memberof module:moize\n * @alias moize.isMoized\n *\n * @description\n * is the fn passed a moized function\n *\n * @param fn the object to test\n * @returns is fn a moized function\n */\nmoize.isMoized = function isMoized(fn: any): fn is Moized {\n    return typeof fn === 'function' && !!fn.isMoized;\n};\n\n/**\n * @function\n * @name matchesArg\n * @memberof module:moize\n * @alias moize.matchesArg\n *\n * @description\n * a moized method where the arg matching method is the custom one passed\n *\n * @param keyMatcher the method to compare against those in cache\n * @returns the moizer function\n */\nmoize.matchesArg = function (argMatcher: IsEqual) {\n    return moize({ matchesArg: argMatcher });\n};\n\n/**\n * @function\n * @name matchesKey\n * @memberof module:moize\n * @alias moize.matchesKey\n *\n * @description\n * a moized method where the key matching method is the custom one passed\n *\n * @param keyMatcher the method to compare against those in cache\n * @returns the moizer function\n */\nmoize.matchesKey = function (keyMatcher: IsMatchingKey) {\n    return moize({ matchesKey: keyMatcher });\n};\n\nfunction maxAge<MaxAge extends number>(\n    maxAge: MaxAge\n): Moize<{ maxAge: MaxAge }>;\nfunction maxAge<MaxAge extends number, UpdateExpire extends boolean>(\n    maxAge: MaxAge,\n    expireOptions: UpdateExpire\n): Moize<{ maxAge: MaxAge; updateExpire: UpdateExpire }>;\nfunction maxAge<MaxAge extends number, ExpireHandler extends OnExpire>(\n    maxAge: MaxAge,\n    expireOptions: ExpireHandler\n): Moize<{ maxAge: MaxAge; onExpire: ExpireHandler }>;\nfunction maxAge<\n    MaxAge extends number,\n    ExpireHandler extends OnExpire,\n    ExpireOptions extends {\n        onExpire: ExpireHandler;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions: ExpireOptions\n): Moize<{ maxAge: MaxAge; onExpire: ExpireOptions['onExpire'] }>;\nfunction maxAge<\n    MaxAge extends number,\n    UpdateExpire extends boolean,\n    ExpireOptions extends {\n        updateExpire: UpdateExpire;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions: ExpireOptions\n): Moize<{ maxAge: MaxAge; updateExpire: UpdateExpire }>;\nfunction maxAge<\n    MaxAge extends number,\n    ExpireHandler extends OnExpire,\n    UpdateExpire extends boolean,\n    ExpireOptions extends {\n        onExpire: ExpireHandler;\n        updateExpire: UpdateExpire;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions: ExpireOptions\n): Moize<{\n    maxAge: MaxAge;\n    onExpire: ExpireHandler;\n    updateExpire: UpdateExpire;\n}>;\nfunction maxAge<\n    MaxAge extends number,\n    ExpireHandler extends OnExpire,\n    UpdateExpire extends boolean,\n    ExpireOptions extends {\n        onExpire?: ExpireHandler;\n        updateExpire?: UpdateExpire;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions?: ExpireHandler | UpdateExpire | ExpireOptions\n) {\n    if (expireOptions === true) {\n        return moize({\n            maxAge,\n            updateExpire: expireOptions,\n        });\n    }\n\n    if (typeof expireOptions === 'object') {\n        const { onExpire, updateExpire } = expireOptions;\n\n        return moize({\n            maxAge,\n            onExpire,\n            updateExpire,\n        });\n    }\n\n    if (typeof expireOptions === 'function') {\n        return moize({\n            maxAge,\n            onExpire: expireOptions,\n            updateExpire: true,\n        });\n    }\n\n    return moize({ maxAge });\n}\n\n/**\n * @function\n * @name maxAge\n * @memberof module:moize\n * @alias moize.maxAge\n *\n * @description\n * a moized method where the age of the cache is limited to the number of milliseconds passed\n *\n * @param maxAge the TTL of the value in cache\n * @returns the moizer function\n */\nmoize.maxAge = maxAge;\n\n/**\n * @function\n * @name maxArgs\n * @memberof module:moize\n * @alias moize.maxArgs\n *\n * @description\n * a moized method where the number of arguments used for determining cache is limited to the value passed\n *\n * @param maxArgs the number of args to base the key on\n * @returns the moizer function\n */\nmoize.maxArgs = function maxArgs(maxArgs: number) {\n    return moize({ maxArgs });\n};\n\n/**\n * @function\n * @name maxSize\n * @memberof module:moize\n * @alias moize.maxSize\n *\n * @description\n * a moized method where the total size of the cache is limited to the value passed\n *\n * @param maxSize the maximum size of the cache\n * @returns the moizer function\n */\nmoize.maxSize = function maxSize(maxSize: number) {\n    return moize({ maxSize });\n};\n\n/**\n * @function\n * @name profile\n * @memberof module:moize\n * @alias moize.profile\n *\n * @description\n * a moized method with a profile name\n *\n * @returns the moizer function\n */\nmoize.profile = function (profileName: string) {\n    return moize({ profileName });\n};\n\n/**\n * @function\n * @name promise\n * @memberof module:moize\n * @alias moize.promise\n *\n * @description\n * a moized method specific to caching resolved promise / async values\n *\n * @returns the moizer function\n */\nmoize.promise = moize({\n    isPromise: true,\n    updateExpire: true,\n});\n\n/**\n * @function\n * @name react\n * @memberof module:moize\n * @alias moize.react\n *\n * @description\n * a moized method specific to caching React element values\n *\n * @returns the moizer function\n */\nmoize.react = moize({ isReact: true });\n\n/**\n * @function\n * @name serialize\n * @memberof module:moize\n * @alias moize.serialize\n *\n * @description\n * a moized method that will serialize the arguments passed to use as the cache key\n *\n * @returns the moizer function\n */\nmoize.serialize = moize({ isSerialized: true });\n\n/**\n * @function\n * @name serializeWith\n * @memberof module:moize\n * @alias moize.serializeWith\n *\n * @description\n * a moized method that will serialize the arguments passed to use as the cache key\n * based on the serializer passed\n *\n * @returns the moizer function\n */\nmoize.serializeWith = function (serializer: Serialize) {\n    return moize({ isSerialized: true, serializer });\n};\n\n/**\n * @function\n * @name shallow\n * @memberof module:moize\n * @alias moize.shallow\n *\n * @description\n * should shallow equality check be used\n *\n * @returns the moizer function\n */\nmoize.shallow = moize({ isShallowEqual: true });\n\n/**\n * @function\n * @name transformArgs\n * @memberof module:moize\n * @alias moize.transformArgs\n *\n * @description\n * transform the args to allow for specific cache key comparison\n *\n * @param transformArgs the args transformer\n * @returns the moizer function\n */\nmoize.transformArgs = <Transformer extends TransformKey>(\n    transformArgs: Transformer\n) => moize({ transformArgs });\n\n/**\n * @function\n * @name updateCacheForKey\n * @memberof module:moize\n * @alias moize.updateCacheForKey\n *\n * @description\n * update the cache for a given key when the method passed returns truthy\n *\n * @param updateCacheForKey the method to determine when to update cache\n * @returns the moizer function\n */\nmoize.updateCacheForKey = <UpdateWhen extends UpdateCacheForKey>(\n    updateCacheForKey: UpdateWhen\n) => moize({ updateCacheForKey });\n\n// Add self-referring `default` property for edge-case cross-compatibility of mixed ESM/CommonJS usage.\n// This property is frozen and non-enumerable to avoid visibility on iteration or accidental overrides.\nObject.defineProperty(moize, 'default', {\n    configurable: false,\n    enumerable: false,\n    value: moize,\n    writable: false,\n});\n\nexport default moize;\n"], "names": ["DEFAULT_OPTIONS", "isDeepEqual", "isPromise", "isReact", "isSerialized", "isShallowEqual", "matchesArg", "undefined", "matchesKey", "maxAge", "maxArgs", "maxSize", "onExpire", "profileName", "serializer", "updateCacheForKey", "transformArgs", "updateExpire", "combine", "functions", "reduce", "f", "g", "apply", "arguments", "compose", "findExpirationIndex", "expirations", "key", "index", "length", "createFindKeyIndex", "isEqual", "isMatchingKey", "areKeysEqual", "cache<PERSON>ey", "keys", "keysIndex", "mergeOptions", "originalOptions", "newOptions", "onCacheAdd", "onCacheChange", "onCacheHit", "isMoized", "fn", "setName", "originalFunctionName", "name", "Object", "defineProperty", "configurable", "enumerable", "value", "writable", "clearExpiration", "<PERSON><PERSON><PERSON><PERSON>", "expirationIndex", "clearTimeout", "timeoutId", "splice", "createTimeout", "expirationMethod", "setTimeout", "unref", "createOnCacheAddSetExpiration", "options", "cache", "moizedOptions", "moized", "findKeyIndex", "keyIndex", "values", "unshift", "push", "createOnCacheHitResetExpiration", "getMaxAgeOptions", "isFinite", "statsCache", "anonymousProfileNameCounter", "isCollectingStats", "profiles", "hasWarningDisplayed", "clearStats", "collectStats", "createOnCacheAddIncrementCalls", "calls", "hits", "createOnCacheHitIncrementCallsAndHits", "getDefaultProfileName", "displayName", "getUsagePercentage", "toFixed", "getStats", "console", "warn", "usage", "profile", "completeStats", "completeProfiles", "computedProfiles", "getStatsOptions", "ALWAYS_SKIPPED_PROPERTIES", "callee", "caller", "constructor", "prototype", "copyStaticProperties", "originalFn", "newFn", "skippedProperties", "getOwnPropertyNames", "for<PERSON>ach", "property", "indexOf", "descriptor", "getOwnPropertyDescriptor", "get", "set", "addInstanceMethods", "memoized", "clear", "_microMemoizeOptions", "transform<PERSON>ey", "has", "cacheSnapshot", "remove", "existingKey", "cutoff", "size", "updateAsyncCache", "orderByLru", "addInstanceProperties", "moizeOptions", "originalFunction", "microMemoizeOptions", "defineProperties", "currentCache", "slice", "expirationsSnapshot", "createMoizeInstance", "configuration", "REACT_ELEMENT_TYPE", "Symbol", "for", "createMoizedComponent", "moizer", "reactMoizer", "Moized", "props", "context", "updater", "MoizedComponent", "isReactComponent", "render", "$$typeof", "type", "ref", "_owner", "createGetInitialArgs", "args", "clone", "<PERSON><PERSON><PERSON><PERSON>", "array", "createDefaultReplacer", "defaultReplacer", "toString", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "join", "getStringifiedArgument", "arg", "typeOfArg", "JSON", "stringify", "defaultArgumentSerializer", "getSerializerFunction", "getIsSerializedKeyEqual", "createOnCacheOperation", "_cacheIgnored", "_microMemoizeOptionsIgnored", "getIsEqual", "deepEqual", "shallowEqual", "sameValueZeroEqual", "getIsMatchingKey", "getTransformKey", "createRefreshableMoized", "refreshableMoized", "result", "moize", "passedOptions", "moizeable", "mergedOptions", "curriedFn", "curriedOptions", "coalescedOptions", "customOptions", "maxAgeOptions", "statsOptions", "memoize", "deep", "infinite", "Infinity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "expireOptions", "promise", "react", "serialize", "serializeWith", "shallow"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACO,IAAMA,eAAwB,GAAG;AACpCC,EAAAA,WAAW,EAAE,KADuB;AAEpCC,EAAAA,SAAS,EAAE,KAFyB;AAGpCC,EAAAA,OAAO,EAAE,KAH2B;AAIpCC,EAAAA,YAAY,EAAE,KAJsB;AAKpCC,EAAAA,cAAc,EAAE,KALoB;AAMpCC,EAAAA,UAAU,EAAEC,SANwB;AAOpCC,EAAAA,UAAU,EAAED,SAPwB;AAQpCE,EAAAA,MAAM,EAAEF,SAR4B;AASpCG,EAAAA,OAAO,EAAEH,SAT2B;AAUpCI,EAAAA,OAAO,EAAE,CAV2B;AAWpCC,EAAAA,QAAQ,EAAEL,SAX0B;AAYpCM,EAAAA,WAAW,EAAEN,SAZuB;AAapCO,EAAAA,UAAU,EAAEP,SAbwB;AAcpCQ,EAAAA,iBAAiB,EAAER,SAdiB;AAepCS,EAAAA,aAAa,EAAET,SAfqB;AAgBpCU,EAAAA,YAAY,EAAE,KAAA;AAhBsB,CAAjC;;ACMP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,OAAT,GAEwB;AAAA,EAAA,KAAA,IAAA,IAAA,GAAA,SAAA,CAAA,MAAA,EADxBC,SACwB,GAAA,IAAA,KAAA,CAAA,IAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,GAAA,IAAA,EAAA,IAAA,EAAA,EAAA;IADxBA,SACwB,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AAAA,GAAA;;EAC3B,OAAOA,SAAS,CAACC,MAAV,CAAiB,UAAUC,CAAV,EAAkBC,CAAlB,EAA0B;AAC9C,IAAA,IAAI,OAAOD,CAAP,KAAa,UAAjB,EAA6B;AACzB,MAAA,OAAO,OAAOC,CAAP,KAAa,UAAb,GACD,YAAqB;AACjBD,QAAAA,CAAC,CAACE,KAAF,CAAQ,IAAR,EAAcC,SAAd,CAAA,CAAA;AACAF,QAAAA,CAAC,CAACC,KAAF,CAAQ,IAAR,EAAcC,SAAd,CAAA,CAAA;AACH,OAJA,GAKDH,CALN,CAAA;AAMH,KAAA;;AAED,IAAA,IAAI,OAAOC,CAAP,KAAa,UAAjB,EAA6B;AACzB,MAAA,OAAOA,CAAP,CAAA;AACH,KAAA;AACJ,GAbM,CAAP,CAAA;AAcH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASG,OAAT,GAAyD;AAAA,EAAA,KAAA,IAAA,KAAA,GAAA,SAAA,CAAA,MAAA,EAA7BN,SAA6B,GAAA,IAAA,KAAA,CAAA,KAAA,CAAA,EAAA,KAAA,GAAA,CAAA,EAAA,KAAA,GAAA,KAAA,EAAA,KAAA,EAAA,EAAA;IAA7BA,SAA6B,CAAA,KAAA,CAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA;AAAA,GAAA;;EAC5D,OAAOA,SAAS,CAACC,MAAV,CAAiB,UAAUC,CAAV,EAAkBC,CAAlB,EAA0B;AAC9C,IAAA,IAAI,OAAOD,CAAP,KAAa,UAAjB,EAA6B;AACzB,MAAA,OAAO,OAAOC,CAAP,KAAa,UAAb,GACD,YAAqB;QACjB,OAAOD,CAAC,CAACC,CAAC,CAACC,KAAF,CAAQ,IAAR,EAAcC,SAAd,CAAD,CAAR,CAAA;AACH,OAHA,GAIDH,CAJN,CAAA;AAKH,KAAA;;AAED,IAAA,IAAI,OAAOC,CAAP,KAAa,UAAjB,EAA6B;AACzB,MAAA,OAAOA,CAAP,CAAA;AACH,KAAA;AACJ,GAZM,CAAP,CAAA;AAaH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASI,mBAAT,CAA6BC,WAA7B,EAAwDC,GAAxD,EAAkE;AACrE,EAAA,KAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGF,WAAW,CAACG,MAAxC,EAAgDD,KAAK,EAArD,EAAyD;IACrD,IAAIF,WAAW,CAACE,KAAD,CAAX,CAAmBD,GAAnB,KAA2BA,GAA/B,EAAoC;AAChC,MAAA,OAAOC,KAAP,CAAA;AACH,KAAA;AACJ,GAAA;;AAED,EAAA,OAAO,CAAC,CAAR,CAAA;AACH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASE,kBAAT,CACHC,OADG,EAEHC,aAFG,EAGL;AACE,EAAA,IAAMC,YAA2B,GAC7B,OAAOD,aAAP,KAAyB,UAAzB,GACMA,aADN,GAEM,UAAUE,QAAV,EAAyBP,GAAzB,EAAmC;AAC/B,IAAA,KAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGD,GAAG,CAACE,MAAhC,EAAwCD,KAAK,EAA7C,EAAiD;AAC7C,MAAA,IAAI,CAACG,OAAO,CAACG,QAAQ,CAACN,KAAD,CAAT,EAAkBD,GAAG,CAACC,KAAD,CAArB,CAAZ,EAA2C;AACvC,QAAA,OAAO,KAAP,CAAA;AACH,OAAA;AACJ,KAAA;;AAED,IAAA,OAAO,IAAP,CAAA;GAVd,CAAA;AAaA,EAAA,OAAO,UAAUO,IAAV,EAAuBR,GAAvB,EAAiC;AACpC,IAAA,KAAK,IAAIS,SAAS,GAAG,CAArB,EAAwBA,SAAS,GAAGD,IAAI,CAACN,MAAzC,EAAiDO,SAAS,EAA1D,EAA8D;MAC1D,IACID,IAAI,CAACC,SAAD,CAAJ,CAAgBP,MAAhB,KAA2BF,GAAG,CAACE,MAA/B,IACAI,YAAY,CAACE,IAAI,CAACC,SAAD,CAAL,EAAkBT,GAAlB,CAFhB,EAGE;AACE,QAAA,OAAOS,SAAP,CAAA;AACH,OAAA;AACJ,KAAA;;AAED,IAAA,OAAO,CAAC,CAAR,CAAA;GAVJ,CAAA;AAYH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,YAAT,CACHC,eADG,EAEHC,UAFG,EAGI;EACP,OAAO,CAACA,UAAD,IAAeA,UAAU,KAAKxC,eAA9B,GACDuC,eADC,GAAA,QAAA,CAAA,EAAA,EAGMA,eAHN,EAIMC,UAJN,EAAA;IAKGC,UAAU,EAAEvB,OAAO,CACfqB,eAAe,CAACE,UADD,EAEfD,UAAU,CAACC,UAFI,CALtB;IASGC,aAAa,EAAExB,OAAO,CAClBqB,eAAe,CAACG,aADE,EAElBF,UAAU,CAACE,aAFO,CATzB;IAaGC,UAAU,EAAEzB,OAAO,CACfqB,eAAe,CAACI,UADD,EAEfH,UAAU,CAACG,UAFI,CAbtB;IAiBG3B,aAAa,EAAES,OAAO,CAClBc,eAAe,CAACvB,aADE,EAElBwB,UAAU,CAACxB,aAFO,CAAA;GAjBhC,CAAA,CAAA;AAsBH,CAAA;AAEM,SAAS4B,QAAT,CAAkBC,EAAlB,EAAkE;AACrE,EAAA,OAAO,OAAOA,EAAP,KAAc,UAAd,IAA6BA,EAAD,CAAkBD,QAArD,CAAA;AACH,CAAA;AAEM,SAASE,OAAT,CACHD,EADG,EAEHE,oBAFG,EAGHlC,WAHG,EAIL;EACE,IAAI;AACA,IAAA,IAAMmC,IAAI,GAAGnC,WAAW,IAAIkC,oBAAf,IAAuC,WAApD,CAAA;AAEAE,IAAAA,MAAM,CAACC,cAAP,CAAsBL,EAAtB,EAA0B,MAA1B,EAAkC;AAC9BM,MAAAA,YAAY,EAAE,IADgB;AAE9BC,MAAAA,UAAU,EAAE,KAFkB;MAG9BC,KAAK,EAAA,SAAA,GAAYL,IAAZ,GAHyB,GAAA;AAI9BM,MAAAA,QAAQ,EAAE,IAAA;KAJd,CAAA,CAAA;GAHJ,CASE,gBAAM;AAEP,GAAA;AACJ;;AC7KD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,eAAT,CACH5B,WADG,EAEHC,GAFG,EAGH4B,YAHG,EAIL;AACE,EAAA,IAAMC,eAAe,GAAG/B,mBAAmB,CAACC,WAAD,EAAcC,GAAd,CAA3C,CAAA;;AAEA,EAAA,IAAI6B,eAAe,KAAK,CAAC,CAAzB,EAA4B;AACxBC,IAAAA,YAAY,CAAC/B,WAAW,CAAC8B,eAAD,CAAX,CAA6BE,SAA9B,CAAZ,CAAA;;AAEA,IAAA,IAAIH,YAAJ,EAAkB;AACd7B,MAAAA,WAAW,CAACiC,MAAZ,CAAmBH,eAAnB,EAAoC,CAApC,CAAA,CAAA;AACH,KAAA;AACJ,GAAA;AACJ,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASI,aAAT,CAAuBC,gBAAvB,EAAqDrD,MAArD,EAAqE;AACxE,EAAA,IAAMkD,SAAS,GAAGI,UAAU,CAACD,gBAAD,EAAmBrD,MAAnB,CAA5B,CAAA;;AAEA,EAAA,IAAI,OAAOkD,SAAS,CAACK,KAAjB,KAA2B,UAA/B,EAA2C;AACvCL,IAAAA,SAAS,CAACK,KAAV,EAAA,CAAA;AACH,GAAA;;AAED,EAAA,OAAOL,SAAP,CAAA;AACH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASM,6BAAT,CACHtC,WADG,EAEHuC,OAFG,EAGHlC,OAHG,EAIHC,aAJG,EAKa;AAChB,EAAA,IAAQxB,MAAR,GAAmByD,OAAnB,CAAQzD,MAAR,CAAA;EAEA,OAAO,SAASgC,UAAT,CACH0B,KADG,EAEHC,aAFG,EAGHC,MAHG,EAIL;AACE,IAAA,IAAMzC,GAAQ,GAAGuC,KAAK,CAAC/B,IAAN,CAAW,CAAX,CAAjB,CAAA;;IAEA,IAAIV,mBAAmB,CAACC,WAAD,EAAcC,GAAd,CAAnB,KAA0C,CAAC,CAA/C,EAAkD;AAC9C,MAAA,IAAMkC,gBAAgB,GAAG,SAAnBA,gBAAmB,GAAY;AACjC,QAAA,IAAMQ,YAAY,GAAGvC,kBAAkB,CAACC,OAAD,EAAUC,aAAV,CAAvC,CAAA;QAEA,IAAMsC,QAAgB,GAAGD,YAAY,CAACH,KAAK,CAAC/B,IAAP,EAAaR,GAAb,CAArC,CAAA;AACA,QAAA,IAAMyB,KAAU,GAAGc,KAAK,CAACK,MAAN,CAAaD,QAAb,CAAnB,CAAA;;QAEA,IAAI,CAACA,QAAL,EAAe;AACXJ,UAAAA,KAAK,CAAC/B,IAAN,CAAWwB,MAAX,CAAkBW,QAAlB,EAA4B,CAA5B,CAAA,CAAA;AACAJ,UAAAA,KAAK,CAACK,MAAN,CAAaZ,MAAb,CAAoBW,QAApB,EAA8B,CAA9B,CAAA,CAAA;;AAEA,UAAA,IAAI,OAAOL,OAAO,CAACxB,aAAf,KAAiC,UAArC,EAAiD;AAC7CwB,YAAAA,OAAO,CAACxB,aAAR,CAAsByB,KAAtB,EAA6BC,aAA7B,EAA4CC,MAA5C,CAAA,CAAA;AACH,WAAA;AACJ,SAAA;;AAEDd,QAAAA,eAAe,CAAC5B,WAAD,EAAcC,GAAd,EAAmB,IAAnB,CAAf,CAAA;;AAEA,QAAA,IACI,OAAOsC,OAAO,CAACtD,QAAf,KAA4B,UAA5B,IACAsD,OAAO,CAACtD,QAAR,CAAiBgB,GAAjB,CAAA,KAA0B,KAF9B,EAGE;AACEuC,UAAAA,KAAK,CAAC/B,IAAN,CAAWqC,OAAX,CAAmB7C,GAAnB,CAAA,CAAA;AACAuC,UAAAA,KAAK,CAACK,MAAN,CAAaC,OAAb,CAAqBpB,KAArB,CAAA,CAAA;AAEAZ,UAAAA,UAAU,CAAC0B,KAAD,EAAQC,aAAR,EAAuBC,MAAvB,CAAV,CAAA;;AAEA,UAAA,IAAI,OAAOH,OAAO,CAACxB,aAAf,KAAiC,UAArC,EAAiD;AAC7CwB,YAAAA,OAAO,CAACxB,aAAR,CAAsByB,KAAtB,EAA6BC,aAA7B,EAA4CC,MAA5C,CAAA,CAAA;AACH,WAAA;AACJ,SAAA;OA7BL,CAAA;;MAgCA1C,WAAW,CAAC+C,IAAZ,CAAiB;AACbZ,QAAAA,gBAAgB,EAAhBA,gBADa;AAEblC,QAAAA,GAAG,EAAHA,GAFa;AAGb+B,QAAAA,SAAS,EAAEE,aAAa,CAACC,gBAAD,EAAmBrD,MAAnB,CAAA;OAH5B,CAAA,CAAA;AAKH,KAAA;GA7CL,CAAA;AA+CH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASkE,+BAAT,CACHhD,WADG,EAEHuC,OAFG,EAGa;AAChB,EAAA,OAAO,SAASvB,UAAT,CAAoBwB,KAApB,EAAkC;AACrC,IAAA,IAAMvC,GAAG,GAAGuC,KAAK,CAAC/B,IAAN,CAAW,CAAX,CAAZ,CAAA;AACA,IAAA,IAAMqB,eAAe,GAAG/B,mBAAmB,CAACC,WAAD,EAAcC,GAAd,CAA3C,CAAA;;IAEA,IAAI,CAAC6B,eAAL,EAAsB;AAClBF,MAAAA,eAAe,CAAC5B,WAAD,EAAcC,GAAd,EAAmB,KAAnB,CAAf,CAAA;AAEAD,MAAAA,WAAW,CAAC8B,eAAD,CAAX,CAA6BE,SAA7B,GAAyCE,aAAa,CAClDlC,WAAW,CAAC8B,eAAD,CAAX,CAA6BK,gBADqB,EAElDI,OAAO,CAACzD,MAF0C,CAAtD,CAAA;AAIH,KAAA;GAXL,CAAA;AAaH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASmE,gBAAT,CACHjD,WADG,EAEHuC,OAFG,EAGHlC,OAHG,EAIHC,aAJG,EAQL;EACE,IAAMQ,UAAU,GACZ,OAAOyB,OAAO,CAACzD,MAAf,KAA0B,QAA1B,IAAsCoE,QAAQ,CAACX,OAAO,CAACzD,MAAT,CAA9C,GACMwD,6BAA6B,CACzBtC,WADyB,EAEzBuC,OAFyB,EAGzBlC,OAHyB,EAIzBC,aAJyB,CADnC,GAOM1B,SARV,CAAA;EAUA,OAAO;AACHkC,IAAAA,UAAU,EAAVA,UADG;AAEHE,IAAAA,UAAU,EACNF,UAAU,IAAIyB,OAAO,CAACjD,YAAtB,GACM0D,+BAA+B,CAAChD,WAAD,EAAcuC,OAAd,CADrC,GAEM3D,SAAAA;GALd,CAAA;AAOH;;AC1LM,IAAMuE,UAAsB,GAAG;AAClCC,EAAAA,2BAA2B,EAAE,CADK;AAElCC,EAAAA,iBAAiB,EAAE,KAFe;AAGlCC,EAAAA,QAAQ,EAAE,EAAA;AAHwB,CAA/B,CAAA;AAMP,IAAIC,mBAAmB,GAAG,KAA1B,CAAA;AAEO,SAASC,UAAT,CAAoBtE,WAApB,EAA0C;AAC7C,EAAA,IAAIA,WAAJ,EAAiB;AACb,IAAA,OAAOiE,UAAU,CAACG,QAAX,CAAoBpE,WAApB,CAAP,CAAA;AACH,GAFD,MAEO;IACHiE,UAAU,CAACG,QAAX,GAAsB,EAAtB,CAAA;AACH,GAAA;AACJ,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASG,YAAT,CAAsBJ,iBAAtB,EAAgD;AAAA,EAAA,IAA1BA,iBAA0B,KAAA,KAAA,CAAA,EAAA;AAA1BA,IAAAA,iBAA0B,GAAN,IAAM,CAAA;AAAA,GAAA;;EACnDF,UAAU,CAACE,iBAAX,GAA+BA,iBAA/B,CAAA;AACH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;;AACO,SAASK,8BAAT,CAAwCnB,OAAxC,EAA0D;AAC7D,EAAA,IAAQrD,WAAR,GAAwBqD,OAAxB,CAAQrD,WAAR,CAAA;AAEA,EAAA,OAAO,YAAY;IACf,IAAIA,WAAW,IAAI,CAACiE,UAAU,CAACG,QAAX,CAAoBpE,WAApB,CAApB,EAAsD;AAClDiE,MAAAA,UAAU,CAACG,QAAX,CAAoBpE,WAApB,CAAmC,GAAA;AAC/ByE,QAAAA,KAAK,EAAE,CADwB;AAE/BC,QAAAA,IAAI,EAAE,CAAA;OAFV,CAAA;AAIH,KAAA;;AAEDT,IAAAA,UAAU,CAACG,QAAX,CAAoBpE,WAApB,EAAiCyE,KAAjC,EAAA,CAAA;GARJ,CAAA;AAUH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;;AACO,SAASE,qCAAT,CAA+CtB,OAA/C,EAAiE;AACpE,EAAA,OAAO,YAAY;AACf,IAAA,IAAQe,QAAR,GAAqBH,UAArB,CAAQG,QAAR,CAAA;AACA,IAAA,IAAQpE,WAAR,GAAwBqD,OAAxB,CAAQrD,WAAR,CAAA;;AAEA,IAAA,IAAI,CAACoE,QAAQ,CAACpE,WAAD,CAAb,EAA4B;MACxBoE,QAAQ,CAACpE,WAAD,CAAR,GAAwB;AACpByE,QAAAA,KAAK,EAAE,CADa;AAEpBC,QAAAA,IAAI,EAAE,CAAA;OAFV,CAAA;AAIH,KAAA;;AAEDN,IAAAA,QAAQ,CAACpE,WAAD,CAAR,CAAsByE,KAAtB,EAAA,CAAA;AACAL,IAAAA,QAAQ,CAACpE,WAAD,CAAR,CAAsB0E,IAAtB,EAAA,CAAA;GAZJ,CAAA;AAcH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASE,qBAAT,CACH5C,EADG,EAEL;EACE,OACKA,EAAD,CAAqD6C,WAArD,IACA7C,EAAE,CAACG,IADH,IAEa8B,YAAAA,GAAAA,UAAU,CAACC,2BAAX,EAHjB,CAAA;AAKH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASY,kBAAT,CAA4BL,KAA5B,EAA2CC,IAA3C,EAAyD;AAC5D,EAAA,OAAOD,KAAK,GAAM,CAAEC,IAAI,GAAGD,KAAR,GAAiB,GAAlB,EAAuBM,OAAvB,CAA+B,CAA/B,CAAN,SAA6C,SAAzD,CAAA;AACH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,QAAT,CAAkBhF,WAAlB,EAA2D;AAC9D,EAAA,IAAI,CAACiE,UAAU,CAACE,iBAAZ,IAAiC,CAACE,mBAAtC,EAA2D;AACvDY,IAAAA,OAAO,CAACC,IAAR,CACI,oFADJ,EADuD;;AAKvDb,IAAAA,mBAAmB,GAAG,IAAtB,CAAA;AACH,GAAA;;AAED,EAAA,IAAQD,QAAR,GAAqBH,UAArB,CAAQG,QAAR,CAAA;;AAEA,EAAA,IAAIpE,WAAJ,EAAiB;AACb,IAAA,IAAI,CAACoE,QAAQ,CAACpE,WAAD,CAAb,EAA4B;MACxB,OAAO;AACHyE,QAAAA,KAAK,EAAE,CADJ;AAEHC,QAAAA,IAAI,EAAE,CAFH;AAGHS,QAAAA,KAAK,EAAE,SAAA;OAHX,CAAA;AAKH,KAAA;;AAED,IAAA,IAAuBC,OAAvB,GAAmChB,QAAnC,CAASpE,WAAT,CAAA,CAAA;AAEA,IAAA,OAAA,QAAA,CAAA,EAAA,EACOoF,OADP,EAAA;MAEID,KAAK,EAAEL,kBAAkB,CAACM,OAAO,CAACX,KAAT,EAAgBW,OAAO,CAACV,IAAxB,CAAA;AAF7B,KAAA,CAAA,CAAA;AAIH,GAAA;;AAED,EAAA,IAAMW,aAA2B,GAAGjD,MAAM,CAACb,IAAP,CAAY0C,UAAU,CAACG,QAAvB,CAAA,CAAiC7D,MAAjC,CAChC,UAAC+E,gBAAD,EAAmBtF,WAAnB,EAAmC;IAC/BsF,gBAAgB,CAACb,KAAjB,IAA0BL,QAAQ,CAACpE,WAAD,CAAR,CAAsByE,KAAhD,CAAA;IACAa,gBAAgB,CAACZ,IAAjB,IAAyBN,QAAQ,CAACpE,WAAD,CAAR,CAAsB0E,IAA/C,CAAA;AAEA,IAAA,OAAOY,gBAAP,CAAA;AACH,GAN+B,EAOhC;AACIb,IAAAA,KAAK,EAAE,CADX;AAEIC,IAAAA,IAAI,EAAE,CAAA;AAFV,GAPgC,CAApC,CAAA;AAaA,EAAA,OAAA,QAAA,CAAA,EAAA,EACOW,aADP,EAAA;AAEIjB,IAAAA,QAAQ,EAAEhC,MAAM,CAACb,IAAP,CAAY6C,QAAZ,CAAsB7D,CAAAA,MAAtB,CACN,UAACgF,gBAAD,EAAmBvF,WAAnB,EAAmC;AAC/BuF,MAAAA,gBAAgB,CAACvF,WAAD,CAAhB,GAAgCgF,QAAQ,CAAChF,WAAD,CAAxC,CAAA;AAEA,MAAA,OAAOuF,gBAAP,CAAA;KAJE,EAMN,EANM,CAFd;IAUIJ,KAAK,EAAEL,kBAAkB,CAACO,aAAa,CAACZ,KAAf,EAAsBY,aAAa,CAACX,IAApC,CAAA;AAV7B,GAAA,CAAA,CAAA;AAYH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASc,eAAT,CAAyBnC,OAAzB,EAGL;EACE,OAAOY,UAAU,CAACE,iBAAX,GACD;AACIvC,IAAAA,UAAU,EAAE4C,8BAA8B,CAACnB,OAAD,CAD9C;IAEIvB,UAAU,EAAE6C,qCAAqC,CAACtB,OAAD,CAAA;AAFrD,GADC,GAKD,EALN,CAAA;AAMH;;ACzLD,IAAMoC,yBAAkD,GAAG;AACvD9E,EAAAA,SAAS,EAAE,IAD4C;AAEvD+E,EAAAA,MAAM,EAAE,IAF+C;AAGvDC,EAAAA,MAAM,EAAE,IAH+C;AAIvDC,EAAAA,WAAW,EAAE,IAJ0C;AAKvD3E,EAAAA,MAAM,EAAE,IAL+C;AAMvDkB,EAAAA,IAAI,EAAE,IANiD;AAOvD0D,EAAAA,SAAS,EAAE,IAAA;AAP4C,CAA3D,CAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,oBAAT,CACHC,UADG,EAEHC,KAFG,EAGHC,iBAHG,EAIL;AAAA,EAAA,IADEA,iBACF,KAAA,KAAA,CAAA,EAAA;AADEA,IAAAA,iBACF,GADgC,EAChC,CAAA;AAAA,GAAA;;EACE7D,MAAM,CAAC8D,mBAAP,CAA2BH,UAA3B,EAAuCI,OAAvC,CAA+C,UAACC,QAAD,EAAc;AACzD,IAAA,IACI,CAACX,yBAAyB,CAACW,QAAD,CAA1B,IACAH,iBAAiB,CAACI,OAAlB,CAA0BD,QAA1B,CAAwC,KAAA,CAAC,CAF7C,EAGE;MACE,IAAME,UAAU,GAAGlE,MAAM,CAACmE,wBAAP,CACfR,UADe,EAEfK,QAFe,CAAnB,CAAA;;AAKA,MAAA,IAAIE,UAAU,CAACE,GAAX,IAAkBF,UAAU,CAACG,GAAjC,EAAsC;AAClCrE,QAAAA,MAAM,CAACC,cAAP,CAAsB2D,KAAtB,EAA6BI,QAA7B,EAAuCE,UAAvC,CAAA,CAAA;AACH,OAFD,MAEO;AACHN,QAAAA,KAAK,CAACI,QAAD,CAAL,GACIL,UAAU,CAACK,QAAD,CADd,CAAA;AAEH,OAAA;AACJ,KAAA;GAhBL,CAAA,CAAA;AAkBH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASM,kBAAT,CACHC,QADG,EAGL,IAAA,EAAA;EAAA,IADI7F,WACJ,QADIA,WACJ,CAAA;AACE,EAAA,IAAQuC,OAAR,GAAoBsD,QAApB,CAAQtD,OAAR,CAAA;EAEA,IAAMI,YAAY,GAAGvC,kBAAkB,CACnCmC,OAAO,CAAClC,OAD2B,EAEnCkC,OAAO,CAACjC,aAF2B,CAAvC,CAAA;EAKA,IAAMoC,MAAM,GAAGmD,QAAf,CAAA;;EAEAnD,MAAM,CAACoD,KAAP,GAAe,YAAY;AACvB,IAAA,IAC4B/E,aAD5B,GAGI2B,MAHJ,CACIqD,oBADJ,CAC4BhF,aAD5B;AAAA,QAEIyB,KAFJ,GAGIE,MAHJ,CAEIF,KAFJ,CAAA;AAKAA,IAAAA,KAAK,CAAC/B,IAAN,CAAWN,MAAX,GAAoB,CAApB,CAAA;AACAqC,IAAAA,KAAK,CAACK,MAAN,CAAa1C,MAAb,GAAsB,CAAtB,CAAA;;AAEA,IAAA,IAAIY,aAAJ,EAAmB;MACfA,aAAa,CAACyB,KAAD,EAAQE,MAAM,CAACH,OAAf,EAAwBG,MAAxB,CAAb,CAAA;AACH,KAAA;;AAED,IAAA,OAAO,IAAP,CAAA;GAbJ,CAAA;;EAgBAA,MAAM,CAACc,UAAP,GAAoB,YAAY;AAC5BA,IAAAA,UAAU,CAACd,MAAM,CAACH,OAAP,CAAerD,WAAhB,CAAV,CAAA;GADJ,CAAA;;AAIAwD,EAAAA,MAAM,CAACgD,GAAP,GAAa,UAAUzF,GAAV,EAAoB;AAC7B,IAAA,IAC4B+F,YAD5B,GAGItD,MAHJ,CACIqD,oBADJ,CAC4BC,YAD5B;AAAA,QAEIxD,KAFJ,GAGIE,MAHJ,CAEIF,KAFJ,CAAA;IAKA,IAAMhC,QAAQ,GAAGwF,YAAY,GAAGA,YAAY,CAAC/F,GAAD,CAAf,GAAuBA,GAApD,CAAA;IACA,IAAM2C,QAAQ,GAAGD,YAAY,CAACH,KAAK,CAAC/B,IAAP,EAAaD,QAAb,CAA7B,CAAA;AAEA,IAAA,OAAOoC,QAAQ,KAAK,CAAC,CAAd,GAAkBF,MAAM,CAAC9C,KAAP,CAAa,IAAb,EAAmBK,GAAnB,CAAlB,GAA4CrB,SAAnD,CAAA;GATJ,CAAA;;EAYA8D,MAAM,CAACwB,QAAP,GAAkB,YAA0B;AACxC,IAAA,OAAOA,QAAQ,CAACxB,MAAM,CAACH,OAAP,CAAerD,WAAhB,CAAf,CAAA;GADJ,CAAA;;AAIAwD,EAAAA,MAAM,CAACuD,GAAP,GAAa,UAAUhG,GAAV,EAAoB;AAC7B,IAAA,IAAQ+F,YAAR,GAAyBtD,MAAM,CAACqD,oBAAhC,CAAQC,YAAR,CAAA;IAEA,IAAMxF,QAAQ,GAAGwF,YAAY,GAAGA,YAAY,CAAC/F,GAAD,CAAf,GAAuBA,GAApD,CAAA;AAEA,IAAA,OAAO0C,YAAY,CAACD,MAAM,CAACF,KAAP,CAAa/B,IAAd,EAAoBD,QAApB,CAAZ,KAA8C,CAAC,CAAtD,CAAA;GALJ,CAAA;;EAQAkC,MAAM,CAACjC,IAAP,GAAc,YAAY;AACtB,IAAA,OAAOiC,MAAM,CAACwD,aAAP,CAAqBzF,IAA5B,CAAA;GADJ,CAAA;;AAIAiC,EAAAA,MAAM,CAACyD,MAAP,GAAgB,UAAUlG,GAAV,EAAoB;IAChC,IAGIyC,qBAAAA,GAAAA,MAHJ,CACIqD,oBADJ;QAC4BhF,aAD5B,yBAC4BA,aAD5B;QAC2CiF,YAD3C,yBAC2CA,YAD3C;AAAA,QAEIxD,KAFJ,GAGIE,MAHJ,CAEIF,KAFJ,CAAA;AAKA,IAAA,IAAMI,QAAQ,GAAGD,YAAY,CACzBH,KAAK,CAAC/B,IADmB,EAEzBuF,YAAY,GAAGA,YAAY,CAAC/F,GAAD,CAAf,GAAuBA,GAFV,CAA7B,CAAA;;AAKA,IAAA,IAAI2C,QAAQ,KAAK,CAAC,CAAlB,EAAqB;AACjB,MAAA,OAAO,KAAP,CAAA;AACH,KAAA;;AAED,IAAA,IAAMwD,WAAW,GAAG5D,KAAK,CAAC/B,IAAN,CAAWmC,QAAX,CAApB,CAAA;AAEAJ,IAAAA,KAAK,CAAC/B,IAAN,CAAWwB,MAAX,CAAkBW,QAAlB,EAA4B,CAA5B,CAAA,CAAA;AACAJ,IAAAA,KAAK,CAACK,MAAN,CAAaZ,MAAb,CAAoBW,QAApB,EAA8B,CAA9B,CAAA,CAAA;;AAEA,IAAA,IAAI7B,aAAJ,EAAmB;MACfA,aAAa,CAACyB,KAAD,EAAQE,MAAM,CAACH,OAAf,EAAwBG,MAAxB,CAAb,CAAA;AACH,KAAA;;AAEDd,IAAAA,eAAe,CAAC5B,WAAD,EAAcoG,WAAd,EAA2B,IAA3B,CAAf,CAAA;AAEA,IAAA,OAAO,IAAP,CAAA;GA1BJ,CAAA;;AA6BA1D,EAAAA,MAAM,CAACiD,GAAP,GAAa,UAAU1F,GAAV,EAAoByB,KAApB,EAAgC;AACzC,IAAA,IAAQqE,oBAAR,GAAiDrD,MAAjD,CAAQqD,oBAAR;AAAA,QAA8BvD,KAA9B,GAAiDE,MAAjD,CAA8BF,KAA9B;AAAA,QAAqCD,OAArC,GAAiDG,MAAjD,CAAqCH,OAArC,CAAA;AACA,IAAA,IAAQzB,UAAR,GACIiF,oBADJ,CAAQjF,UAAR;AAAA,QAAoBC,aAApB,GACIgF,oBADJ,CAAoBhF,aAApB;AAAA,QAAmCiF,YAAnC,GACID,oBADJ,CAAmCC,YAAnC,CAAA;IAGA,IAAMxF,QAAQ,GAAGwF,YAAY,GAAGA,YAAY,CAAC/F,GAAD,CAAf,GAAuBA,GAApD,CAAA;IACA,IAAM2C,QAAQ,GAAGD,YAAY,CAACH,KAAK,CAAC/B,IAAP,EAAaD,QAAb,CAA7B,CAAA;;AAEA,IAAA,IAAIoC,QAAQ,KAAK,CAAC,CAAlB,EAAqB;AACjB,MAAA,IAAMyD,MAAM,GAAG9D,OAAO,CAACvD,OAAR,GAAkB,CAAjC,CAAA;;AAEA,MAAA,IAAIwD,KAAK,CAAC8D,IAAN,GAAaD,MAAjB,EAAyB;AACrB7D,QAAAA,KAAK,CAAC/B,IAAN,CAAWN,MAAX,GAAoBkG,MAApB,CAAA;AACA7D,QAAAA,KAAK,CAACK,MAAN,CAAa1C,MAAb,GAAsBkG,MAAtB,CAAA;AACH,OAAA;;AAED7D,MAAAA,KAAK,CAAC/B,IAAN,CAAWqC,OAAX,CAAmBtC,QAAnB,CAAA,CAAA;AACAgC,MAAAA,KAAK,CAACK,MAAN,CAAaC,OAAb,CAAqBpB,KAArB,CAAA,CAAA;;MAEA,IAAIa,OAAO,CAAChE,SAAZ,EAAuB;QACnBiE,KAAK,CAAC+D,gBAAN,CAAuB7D,MAAvB,CAAA,CAAA;AACH,OAAA;;AAED,MAAA,IAAI5B,UAAJ,EAAgB;AACZA,QAAAA,UAAU,CAAC0B,KAAD,EAAQD,OAAR,EAAiBG,MAAjB,CAAV,CAAA;AACH,OAAA;;AAED,MAAA,IAAI3B,aAAJ,EAAmB;AACfA,QAAAA,aAAa,CAACyB,KAAD,EAAQD,OAAR,EAAiBG,MAAjB,CAAb,CAAA;AACH,OAAA;AACJ,KAtBD,MAsBO;AACH,MAAA,IAAM0D,WAAW,GAAG5D,KAAK,CAAC/B,IAAN,CAAWmC,QAAX,CAApB,CAAA;AAEAJ,MAAAA,KAAK,CAACK,MAAN,CAAaD,QAAb,IAAyBlB,KAAzB,CAAA;;MAEA,IAAIkB,QAAQ,GAAG,CAAf,EAAkB;AACdJ,QAAAA,KAAK,CAACgE,UAAN,CAAiBJ,WAAjB,EAA8B1E,KAA9B,EAAqCkB,QAArC,CAAA,CAAA;AACH,OAAA;;MAED,IAAIL,OAAO,CAAChE,SAAZ,EAAuB;QACnBiE,KAAK,CAAC+D,gBAAN,CAAuB7D,MAAvB,CAAA,CAAA;AACH,OAAA;;AAED,MAAA,IAAI,OAAO3B,aAAP,KAAyB,UAA7B,EAAyC;AACrCA,QAAAA,aAAa,CAACyB,KAAD,EAAQD,OAAR,EAAiBG,MAAjB,CAAb,CAAA;AACH,OAAA;AACJ,KAAA;GA9CL,CAAA;;EAiDAA,MAAM,CAACG,MAAP,GAAgB,YAAY;AACxB,IAAA,OAAOH,MAAM,CAACwD,aAAP,CAAqBrD,MAA5B,CAAA;GADJ,CAAA;AAGH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAAS4D,qBAAT,CACHZ,QADG,EAOL,KAAA,EAAA;EAAA,IAJM7F,WAIN,SAJMA,WAIN;MAHe0G,YAGf,SAHMnE,OAGN;MAFMoE,gBAEN,SAFMA,gBAEN,CAAA;AACE,EAAA,IAAiBC,mBAAjB,GAAyCf,QAAzC,CAAQtD,OAAR,CAAA;AAEAjB,EAAAA,MAAM,CAACuF,gBAAP,CAAwBhB,QAAxB,EAAkC;AAC9BE,IAAAA,oBAAoB,EAAE;AAClBvE,MAAAA,YAAY,EAAE,IADI;AAElBkE,MAAAA,GAFkB,EAEZ,SAAA,GAAA,GAAA;AACF,QAAA,OAAOkB,mBAAP,CAAA;AACH,OAAA;KALyB;AAQ9BV,IAAAA,aAAa,EAAE;AACX1E,MAAAA,YAAY,EAAE,IADH;AAEXkE,MAAAA,GAFW,EAEL,SAAA,GAAA,GAAA;AACF,QAAA,IAAeoB,YAAf,GAAgCjB,QAAhC,CAAQrD,KAAR,CAAA;QAEA,OAAO;UACH/B,IAAI,EAAEqG,YAAY,CAACrG,IAAb,CAAkBsG,KAAlB,CAAwB,CAAxB,CADH;UAEHT,IAAI,EAAEQ,YAAY,CAACR,IAFhB;AAGHzD,UAAAA,MAAM,EAAEiE,YAAY,CAACjE,MAAb,CAAoBkE,KAApB,CAA0B,CAA1B,CAAA;SAHZ,CAAA;AAKH,OAAA;KAlByB;AAqB9B/G,IAAAA,WAAW,EAAE;AACTwB,MAAAA,YAAY,EAAE,IADL;AAETkE,MAAAA,GAFS,EAEH,SAAA,GAAA,GAAA;AACF,QAAA,OAAO1F,WAAP,CAAA;AACH,OAAA;KAzByB;AA4B9BgH,IAAAA,mBAAmB,EAAE;AACjBxF,MAAAA,YAAY,EAAE,IADG;AAEjBkE,MAAAA,GAFiB,EAEX,SAAA,GAAA,GAAA;AACF,QAAA,OAAO1F,WAAW,CAAC+G,KAAZ,CAAkB,CAAlB,CAAP,CAAA;AACH,OAAA;KAhCyB;AAmC9B9F,IAAAA,QAAQ,EAAE;AACNO,MAAAA,YAAY,EAAE,IADR;AAENkE,MAAAA,GAFM,EAEA,SAAA,GAAA,GAAA;AACF,QAAA,OAAO,IAAP,CAAA;AACH,OAAA;KAvCyB;AA0C9BnD,IAAAA,OAAO,EAAE;AACLf,MAAAA,YAAY,EAAE,IADT;AAELkE,MAAAA,GAFK,EAEC,SAAA,GAAA,GAAA;AACF,QAAA,OAAOgB,YAAP,CAAA;AACH,OAAA;KA9CyB;AAiD9BC,IAAAA,gBAAgB,EAAE;AACdnF,MAAAA,YAAY,EAAE,IADA;AAEdkE,MAAAA,GAFc,EAER,SAAA,GAAA,GAAA;AACF,QAAA,OAAOiB,gBAAP,CAAA;AACH,OAAA;AAJa,KAAA;GAjDtB,CAAA,CAAA;EAyDA,IAAMjE,MAAM,GAAGmD,QAAf,CAAA;AAEAb,EAAAA,oBAAoB,CAAC2B,gBAAD,EAAmBjE,MAAnB,CAApB,CAAA;AACH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASuE,mBAAT,CAIHpB,QAJG,EAKHqB,aALG,EAML;AACEtB,EAAAA,kBAAkB,CAAaC,QAAb,EAAuBqB,aAAvB,CAAlB,CAAA;AACAT,EAAAA,qBAAqB,CAAaZ,QAAb,EAAuBqB,aAAvB,CAArB,CAAA;AAEA,EAAA,OAAOrB,QAAP,CAAA;AACH;;ACnTD;AACA;AACA,IAAMsB,kBAAkB,GACpB,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAAvC,GACMD,MAAM,CAACC,GAAP,CAAW,eAAX,CADN,GAEM,MAHV,CAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,qBAAT,CACHC,MADG,EAEHrG,EAFG,EAGHqB,OAHG,EAIL;AACE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAMiF,WAAW,GAAGD,MAAM,CAAA,QAAA,CAAA;AACtBxI,IAAAA,OAAO,EAAE,CADa;AAEtBL,IAAAA,cAAc,EAAE,IAAA;AAFM,GAAA,EAGnB6D,OAHmB,EAAA;AAItB/D,IAAAA,OAAO,EAAE,KAAA;GAJb,CAAA,CAAA,CAAA;;AAOA,EAAA,IAAI,CAAC0C,EAAE,CAAC6C,WAAR,EAAqB;AACjB;AACA7C,IAAAA,EAAE,CAAC6C,WAAH,GAAiB7C,EAAE,CAACG,IAAH,IAAW,WAA5B,CAAA;AACH,GAAA;;AAED,EAAA,SAASoG,MAAT,CAEIC,KAFJ,EAGIC,OAHJ,EAIIC,OAJJ,EAKE;IACE,IAAKF,CAAAA,KAAL,GAAaA,KAAb,CAAA;IACA,IAAKC,CAAAA,OAAL,GAAeA,OAAf,CAAA;IACA,IAAKC,CAAAA,OAAL,GAAeA,OAAf,CAAA;AAEA,IAAA,IAAA,CAAKC,eAAL,GAAuBL,WAAW,CAACtG,EAAD,CAAlC,CAAA;AACH,GAAA;;AAEDuG,EAAAA,MAAM,CAAC1C,SAAP,CAAiB+C,gBAAjB,GAAoC,EAApC,CAAA;;AAEAL,EAAAA,MAAM,CAAC1C,SAAP,CAAiBgD,MAAjB,GAA0B,YAAY;IAClC,OAAO;AACHC,MAAAA,QAAQ,EAAEb,kBADP;MAEHc,IAAI,EAAE,KAAKJ,eAFR;MAGHH,KAAK,EAAE,KAAKA,KAHT;AAIHQ,MAAAA,GAAG,EAAE,IAJF;AAKHjI,MAAAA,GAAG,EAAE,IALF;AAMHkI,MAAAA,MAAM,EAAE,IAAA;KANZ,CAAA;GADJ,CAAA;;EAWAnD,oBAAoB,CAAC9D,EAAD,EAAKuG,MAAL,EAAa,CAAC,aAAD,EAAgB,cAAhB,CAAb,CAApB,CAAA;EAEAA,MAAM,CAAC1D,WAAP,GAAA,SAAA,IAA+B7C,EAAE,CAAC6C,WAAH,IAAkB7C,EAAE,CAACG,IAArB,IAA6B,WAA5D,CAAA,GAAA,GAAA,CAAA;EAEAF,OAAO,CAACsG,MAAD,EAA2BvG,EAAE,CAACG,IAA9B,EAAoCkB,OAAO,CAACrD,WAA5C,CAAP,CAAA;AAEA,EAAA,OAAOuI,MAAP,CAAA;AACH;;AC9FM,SAASW,oBAAT,CAA8B9B,IAA9B,EAA4C;AAC/C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAO,UAAU+B,IAAV,EAA0B;AAC7B,IAAA,IAAI/B,IAAI,IAAI+B,IAAI,CAAClI,MAAjB,EAAyB;AACrB,MAAA,OAAOkI,IAAP,CAAA;AACH,KAAA;;IAED,IAAI/B,IAAI,KAAK,CAAb,EAAgB;AACZ,MAAA,OAAO,EAAP,CAAA;AACH,KAAA;;IAED,IAAIA,IAAI,KAAK,CAAb,EAAgB;AACZ,MAAA,OAAO,CAAC+B,IAAI,CAAC,CAAD,CAAL,CAAP,CAAA;AACH,KAAA;;IAED,IAAI/B,IAAI,KAAK,CAAb,EAAgB;MACZ,OAAO,CAAC+B,IAAI,CAAC,CAAD,CAAL,EAAUA,IAAI,CAAC,CAAD,CAAd,CAAP,CAAA;AACH,KAAA;;IAED,IAAI/B,IAAI,KAAK,CAAb,EAAgB;AACZ,MAAA,OAAO,CAAC+B,IAAI,CAAC,CAAD,CAAL,EAAUA,IAAI,CAAC,CAAD,CAAd,EAAmBA,IAAI,CAAC,CAAD,CAAvB,CAAP,CAAA;AACH,KAAA;;IAED,IAAMC,KAAK,GAAG,EAAd,CAAA;;IAEA,KAAK,IAAIpI,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGoG,IAA5B,EAAkCpG,KAAK,EAAvC,EAA2C;AACvCoI,MAAAA,KAAK,CAACpI,KAAD,CAAL,GAAemI,IAAI,CAACnI,KAAD,CAAnB,CAAA;AACH,KAAA;;AAED,IAAA,OAAOoI,KAAP,CAAA;GA3BJ,CAAA;AA6BH;;ACvCD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAAT,CAAmBC,KAAnB,EAAiC9G,KAAjC,EAA6C;AACzC,EAAA,IAAQvB,MAAR,GAAmBqI,KAAnB,CAAQrI,MAAR,CAAA;;EAEA,KAAK,IAAID,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGC,MAA5B,EAAoC,EAAED,KAAtC,EAA6C;AACzC,IAAA,IAAIsI,KAAK,CAACtI,KAAD,CAAL,KAAiBwB,KAArB,EAA4B;MACxB,OAAOxB,KAAK,GAAG,CAAf,CAAA;AACH,KAAA;AACJ,GAAA;;AAED,EAAA,OAAO,CAAP,CAAA;AACH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASuI,qBAAT,GAAiC;EACpC,IAAMjG,KAAY,GAAG,EAArB,CAAA;EACA,IAAM/B,IAAc,GAAG,EAAvB,CAAA;AAEA,EAAA,OAAO,SAASiI,eAAT,CAAyBzI,GAAzB,EAAsCyB,KAAtC,EAAkD;IACrD,IAAMuG,IAAI,GAAG,OAAOvG,KAApB,CAAA;;AAEA,IAAA,IAAIuG,IAAI,KAAK,UAAT,IAAuBA,IAAI,KAAK,QAApC,EAA8C;MAC1C,OAAOvG,KAAK,CAACiH,QAAN,EAAP,CAAA;AACH,KAAA;;AAED,IAAA,IAAI,OAAOjH,KAAP,KAAiB,QAArB,EAA+B;MAC3B,IAAIc,KAAK,CAACrC,MAAV,EAAkB;AACd,QAAA,IAAMyI,UAAU,GAAGL,SAAS,CAAC/F,KAAD,EAAQ,IAAR,CAA5B,CAAA;;QAEA,IAAIoG,UAAU,KAAK,CAAnB,EAAsB;AAClBpG,UAAAA,KAAK,CAACA,KAAK,CAACrC,MAAP,CAAL,GAAsB,IAAtB,CAAA;AACH,SAFD,MAEO;UACHqC,KAAK,CAACP,MAAN,CAAa2G,UAAb,CAAA,CAAA;UACAnI,IAAI,CAACwB,MAAL,CAAY2G,UAAZ,CAAA,CAAA;AACH,SAAA;;AAEDnI,QAAAA,IAAI,CAACA,IAAI,CAACN,MAAN,CAAJ,GAAoBF,GAApB,CAAA;AAEA,QAAA,IAAM4I,WAAW,GAAGN,SAAS,CAAC/F,KAAD,EAAQd,KAAR,CAA7B,CAAA;;QAEA,IAAImH,WAAW,KAAK,CAApB,EAAuB;AACnB,UAAA,OAAA,OAAA,IACIpI,IAAI,CAACsG,KAAL,CAAW,CAAX,EAAc8B,WAAd,CAAA,CAA2BC,IAA3B,CAAgC,GAAhC,CAAA,IAAwC,GAD5C,CAAA,GAAA,GAAA,CAAA;AAGH,SAAA;AACJ,OAnBD,MAmBO;AACHtG,QAAAA,KAAK,CAAC,CAAD,CAAL,GAAWd,KAAX,CAAA;AACAjB,QAAAA,IAAI,CAAC,CAAD,CAAJ,GAAUR,GAAV,CAAA;AACH,OAAA;;AAED,MAAA,OAAOyB,KAAP,CAAA;AACH,KAAA;;AAED,IAAA,OAAO,KAAKA,KAAZ,CAAA;GAnCJ,CAAA;AAqCH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASqH,sBAAT,CAAsCC,GAAtC,EAAiD;EACpD,IAAMC,SAAS,GAAG,OAAOD,GAAzB,CAAA;EAEA,OAAOA,GAAG,KAAKC,SAAS,KAAK,QAAd,IAA0BA,SAAS,KAAK,UAA7C,CAAH,GACDC,IAAI,CAACC,SAAL,CAAeH,GAAf,EAAoBP,qBAAqB,EAAzC,CADC,GAEDO,GAFN,CAAA;AAGH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASI,yBAAT,CAAmCf,IAAnC,EAA8C;EACjD,IAAIpI,GAAG,GAAG,GAAV,CAAA;;AAEA,EAAA,KAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGmI,IAAI,CAAClI,MAAjC,EAAyCD,KAAK,EAA9C,EAAkD;IAC9CD,GAAG,IAAI8I,sBAAsB,CAACV,IAAI,CAACnI,KAAD,CAAL,CAAtB,GAAsC,GAA7C,CAAA;AACH,GAAA;;EAED,OAAO,CAACD,GAAD,CAAP,CAAA;AACH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASoJ,qBAAT,CAA+B9G,OAA/B,EAAiD;EACpD,OAAO,OAAOA,OAAO,CAACpD,UAAf,KAA8B,UAA9B,GACDoD,OAAO,CAACpD,UADP,GAEDiK,yBAFN,CAAA;AAGH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASE,uBAAT,CAAiC9I,QAAjC,EAAgDP,GAAhD,EAA0D;EAC7D,OAAOO,QAAQ,CAAC,CAAD,CAAR,KAAgBP,GAAG,CAAC,CAAD,CAA1B,CAAA;AACH;;AC3HM,SAASsJ,sBAAT,CACHrI,EADG,EAEa;AAChB,EAAA,IAAI,OAAOA,EAAP,KAAc,UAAlB,EAA8B;AAC1B,IAAA,OAAO,UACHsI,aADG,EAEHC,2BAFG,EAGH5D,QAHG,EAAA;MAAA,OAII3E,EAAE,CAAC2E,QAAQ,CAACrD,KAAV,EAAiBqD,QAAQ,CAACtD,OAA1B,EAAmCsD,QAAnC,CAJN,CAAA;KAAP,CAAA;AAKH,GAAA;AACJ,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAAS6D,UAAT,CAAoBnH,OAApB,EAA+C;AAClD,EAAA,OACIA,OAAO,CAAC5D,UAAR,IACC4D,OAAO,CAACjE,WAAR,IAAuBqL,SADxB,IAECpH,OAAO,CAAC7D,cAAR,IAA0BkL,YAF3B,IAGAC,kBAJJ,CAAA;AAMH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,gBAAT,CAA0BvH,OAA1B,EAAuE;EAC1E,OACIA,OAAO,CAAC1D,UAAR,IACC0D,OAAO,CAAC9D,YAAR,IAAwB6K,uBADzB,IAEA1K,SAHJ,CAAA;AAKH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASmL,eAAT,CAAyBxH,OAAzB,EAAqE;AACxE,EAAA,OAAOzC,OAAO,CACVyC,OAAO,CAAC9D,YAAR,IAAwB4K,qBAAqB,CAAC9G,OAAD,CADnC,EAEV,OAAOA,OAAO,CAAClD,aAAf,KAAiC,UAAjC,IAA+CkD,OAAO,CAAClD,aAF7C,EAGV,OAAOkD,OAAO,CAACxD,OAAf,KAA2B,QAA3B,IACIqJ,oBAAoB,CAAC7F,OAAO,CAACxD,OAAT,CAJd,CAAd,CAAA;AAMH;;AC3EM,SAASiL,uBAAT,CACHtH,MADG,EAEL;AACE,EAAA,IACetD,iBADf,GAEIsD,MAFJ,CACIH,OADJ,CACenD,iBADf,CAAA;AAIA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACI,EAAA,IAAM6K,iBAAiB,GAAG,SAASA,iBAAT,GAGxB;AAAA,IAAA,KAAA,IAAA,IAAA,GAAA,SAAA,CAAA,MAAA,EADK5B,IACL,GAAA,IAAA,KAAA,CAAA,IAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,GAAA,IAAA,EAAA,IAAA,EAAA,EAAA;MADKA,IACL,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AAAA,KAAA;;AACE,IAAA,IAAI,CAACjJ,iBAAiB,CAACiJ,IAAD,CAAtB,EAA8B;AAC1B,MAAA,OAAO3F,MAAM,CAAC9C,KAAP,CAAa,IAAb,EAAmByI,IAAnB,CAAP,CAAA;AACH,KAAA;;IAED,IAAM6B,MAAM,GAAGxH,MAAM,CAACxB,EAAP,CAAUtB,KAAV,CAAgB,IAAhB,EAAsByI,IAAtB,CAAf,CAAA;AAEA3F,IAAAA,MAAM,CAACiD,GAAP,CAAW0C,IAAX,EAAiB6B,MAAjB,CAAA,CAAA;AAEA,IAAA,OAAOA,MAAP,CAAA;GAZJ,CAAA;;AAeAlF,EAAAA,oBAAoB,CAACtC,MAAD,EAASuH,iBAAT,CAApB,CAAA;AAEA,EAAA,OAAOA,iBAAP,CAAA;AACH;;;;ACJD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACME,IAAAA,KAAY,GAAG,SAAfA,KAAe,CAGnBjJ,EAHmB,EAGKkJ,aAHL,EAGoC;AAGrD,EAAA,IAAM7H,OAAgB,GAAG6H,aAAa,IAAI/L,eAA1C,CAAA;;AAEA,EAAA,IAAI4C,QAAQ,CAACC,EAAD,CAAZ,EAAkB;AACd,IAAA,IAAMmJ,SAAS,GAAGnJ,EAAE,CAACyF,gBAArB,CAAA;IACA,IAAM2D,aAAa,GAAG3J,YAAY,CAC9BO,EAAE,CAACqB,OAD2B,EAE9BA,OAF8B,CAAlC,CAAA;AAKA,IAAA,OAAO4H,KAAK,CAAsBE,SAAtB,EAAiCC,aAAjC,CAAZ,CAAA;AACH,GAAA;;AAED,EAAA,IAAI,OAAOpJ,EAAP,KAAc,QAAlB,EAA4B;AACxB,IAAA,OAAO,UAIHqJ,SAJG,EAKHC,cALG,EAML;AAGE,MAAA,IAAI,OAAOD,SAAP,KAAqB,UAAzB,EAAqC;AACjC,QAAA,IAAMD,cAAa,GAAG3J,YAAY,CAC9BO,EAD8B,EAE9BsJ,cAF8B,CAAlC,CAAA;;AAKA,QAAA,OAAOL,KAAK,CAACI,SAAD,EAAYD,cAAZ,CAAZ,CAAA;AACH,OAAA;;AAED,MAAA,IAAMA,aAAa,GAAG3J,YAAY,CAC9BO,EAD8B,EAE9BqJ,SAF8B,CAAlC,CAAA;MAKA,OAAOJ,KAAK,CAACG,aAAD,CAAZ,CAAA;KAvBJ,CAAA;AAyBH,GAAA;;EAED,IAAI/H,OAAO,CAAC/D,OAAZ,EAAqB;AACjB,IAAA,OAAO8I,qBAAqB,CAAC6C,KAAD,EAAQjJ,EAAR,EAAYqB,OAAZ,CAA5B,CAAA;AACH,GAAA;;AAED,EAAA,IAAMkI,gBAAyB,GAAA,QAAA,CAAA,EAAA,EACxBpM,eADwB,EAExBkE,OAFwB,EAAA;IAG3BzD,MAAM,EACF,OAAOyD,OAAO,CAACzD,MAAf,KAA0B,QAA1B,IAAsCyD,OAAO,CAACzD,MAAR,IAAkB,CAAxD,GACMyD,OAAO,CAACzD,MADd,GAEMT,eAAe,CAACS,MANC;IAO3BC,OAAO,EACH,OAAOwD,OAAO,CAACxD,OAAf,KAA2B,QAA3B,IAAuCwD,OAAO,CAACxD,OAAR,IAAmB,CAA1D,GACMwD,OAAO,CAACxD,OADd,GAEMV,eAAe,CAACU,OAVC;IAW3BC,OAAO,EACH,OAAOuD,OAAO,CAACvD,OAAf,KAA2B,QAA3B,IAAuCuD,OAAO,CAACvD,OAAR,IAAmB,CAA1D,GACMuD,OAAO,CAACvD,OADd,GAEMX,eAAe,CAACW,OAdC;AAe3BE,IAAAA,WAAW,EAAEqD,OAAO,CAACrD,WAAR,IAAuB4E,qBAAqB,CAAC5C,EAAD,CAAA;GAf7D,CAAA,CAAA;;EAiBA,IAAMlB,WAA8B,GAAG,EAAvC,CAAA;;AAEA,EAqBIyK,gBArBJ,CACI9L,UADJ,CAAA;AAAA,MAqBI8L,gBArBJ,CAEInM,WAFJ,CAAA;AAAA,UAGIC,SAHJ,GAqBIkM,gBArBJ,CAGIlM,SAHJ,CAAA;AAAA,MAqBIkM,gBArBJ,CAIIjM,OAJJ,CAAA;AAAA,MAqBIiM,gBArBJ,CAKIhM,YALJ,CAAA;AAAA,MAqBIgM,gBArBJ,CAMI/L,cANJ,CAAA;AAAA,MAqBI+L,gBArBJ,CAOI5L,UAPJ,CAAA;AAAA,MAqBI4L,gBArBJ,CAQI3L,MARJ,CAAA;AAAA,MAqBI2L,gBArBJ,CASI1L,OATJ,CAAA;AAAA,UAUIC,OAVJ,GAqBIyL,gBArBJ,CAUIzL,OAVJ,CAAA;AAAA,MAWI8B,UAXJ,GAqBI2J,gBArBJ,CAWI3J,UAXJ,CAAA;AAAA,MAYIC,aAZJ,GAqBI0J,gBArBJ,CAYI1J,aAZJ,CAAA;AAAA,MAaIC,UAbJ,GAqBIyJ,gBArBJ,CAaIzJ,UAbJ,CAAA;AAAA,MAqBIyJ,gBArBJ,CAcIxL,QAdJ,CAAA;AAAA,MAqBIwL,gBArBJ,CAeIvL,WAfJ,CAAA;AAAA,MAqBIuL,gBArBJ,CAgBItL,UAhBJ,CAAA;AAAA,UAiBIC,iBAjBJ,GAqBIqL,gBArBJ,CAiBIrL,iBAjBJ,CAAA;AAAA,MAqBIqL,gBArBJ,CAkBIpL,aAlBJ,CAAA;AAAA,MAqBIoL,gBArBJ,CAmBInL,YAnBJ,CAAA;UAoBOoL,aApBP,iCAqBID,gBArBJ,EAAA,SAAA,EAAA;;AAuBA,EAAA,IAAMpK,OAAO,GAAGqJ,UAAU,CAACe,gBAAD,CAA1B,CAAA;AACA,EAAA,IAAMnK,aAAa,GAAGwJ,gBAAgB,CAACW,gBAAD,CAAtC,CAAA;EAEA,IAAME,aAAa,GAAG1H,gBAAgB,CAClCjD,WADkC,EAElCyK,gBAFkC,EAGlCpK,OAHkC,EAIlCC,aAJkC,CAAtC,CAAA;AAMA,EAAA,IAAMsK,YAAY,GAAGlG,eAAe,CAAC+F,gBAAD,CAApC,CAAA;AAEA,EAAA,IAAMzE,YAAY,GAAG+D,eAAe,CAACU,gBAAD,CAApC,CAAA;;EAEA,IAAM7D,mBAAwC,gBACvC8D,aADuC,EAAA;AAE1CrK,IAAAA,OAAO,EAAPA,OAF0C;AAG1CC,IAAAA,aAAa,EAAbA,aAH0C;AAI1C/B,IAAAA,SAAS,EAATA,SAJ0C;AAK1CS,IAAAA,OAAO,EAAPA,OAL0C;AAM1C8B,IAAAA,UAAU,EAAEyI,sBAAsB,CAC9BhK,OAAO,CACHuB,UADG,EAEH6J,aAAa,CAAC7J,UAFX,EAGH8J,YAAY,CAAC9J,UAHV,CADuB,CANQ;AAa1CC,IAAAA,aAAa,EAAEwI,sBAAsB,CAACxI,aAAD,CAbK;AAc1CC,IAAAA,UAAU,EAAEuI,sBAAsB,CAC9BhK,OAAO,CACHyB,UADG,EAEH2J,aAAa,CAAC3J,UAFX,EAGH4J,YAAY,CAAC5J,UAHV,CADuB,CAdQ;AAqB1CgF,IAAAA,YAAY,EAAZA,YAAAA;GArBJ,CAAA,CAAA;;AAwBA,EAAA,IAAMH,QAAQ,GAAGgF,OAAO,CAAC3J,EAAD,EAAK0F,mBAAL,CAAxB,CAAA;AAEA,EAAA,IAAIlE,MAAM,GAAGuE,mBAAmB,CAAsBpB,QAAtB,EAAgC;AAC5D7F,IAAAA,WAAW,EAAXA,WAD4D;AAE5DuC,IAAAA,OAAO,EAAEkI,gBAFmD;AAG5D9D,IAAAA,gBAAgB,EAAEzF,EAAAA;AAH0C,GAAhC,CAAhC,CAAA;;AAMA,EAAA,IAAI9B,iBAAJ,EAAuB;AACnBsD,IAAAA,MAAM,GAAGsH,uBAAuB,CAAgBtH,MAAhB,CAAhC,CAAA;AACH,GAAA;;EAEDvB,OAAO,CAACuB,MAAD,EAAUxB,EAAD,CAAkBG,IAA3B,EAAiCkB,OAAO,CAACrD,WAAzC,CAAP,CAAA;AAEA,EAAA,OAAOwD,MAAP,CAAA;AACH,EAhJD;AAkJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAyH,KAAK,CAAC3G,UAAN,GAAmBA,UAAnB,CAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA2G,KAAK,CAAC1G,YAAN,GAAqBA,YAArB,CAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA0G,KAAK,CAACrK,OAAN,GAAgB,YAA8B;EAC1C,OAAOA,OAAO,CAAP,KAAA,CAAA,KAAA,CAAA,EAAA,SAAA,CAAA,IAA6BqK,KAApC,CAAA;AACH,CAFD,CAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAA,KAAK,CAACW,IAAN,GAAaX,KAAK,CAAC;AAAE7L,EAAAA,WAAW,EAAE,IAAA;AAAf,CAAD,CAAlB,CAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA6L,KAAK,CAACjG,QAAN,GAAiBA,QAAjB,CAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACAiG,KAAK,CAACY,QAAN,GAAiBZ,KAAK,CAAC;AAAEnL,EAAAA,OAAO,EAAEgM,QAAAA;AAAX,CAAD,CAAtB,CAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACAb,KAAK,CAAC9G,iBAAN,GAA0B,SAASA,iBAAT,GAAsC;EAC5D,OAAOF,UAAU,CAACE,iBAAlB,CAAA;AACH,CAFD,CAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA8G,KAAK,CAAClJ,QAAN,GAAiB,SAASA,QAAT,CAAkBC,EAAlB,EAAyC;EACtD,OAAO,OAAOA,EAAP,KAAc,UAAd,IAA4B,CAAC,CAACA,EAAE,CAACD,QAAxC,CAAA;AACH,CAFD,CAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAkJ,KAAK,CAACxL,UAAN,GAAmB,UAAUsM,UAAV,EAA+B;AAC9C,EAAA,OAAOd,KAAK,CAAC;AAAExL,IAAAA,UAAU,EAAEsM,UAAAA;AAAd,GAAD,CAAZ,CAAA;AACH,CAFD,CAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAd,KAAK,CAACtL,UAAN,GAAmB,UAAUqM,UAAV,EAAqC;AACpD,EAAA,OAAOf,KAAK,CAAC;AAAEtL,IAAAA,UAAU,EAAEqM,UAAAA;AAAd,GAAD,CAAZ,CAAA;AACH,CAFD,CAAA;;AAmDA,SAASpM,MAAT,CASIA,MATJ,EAUIqM,aAVJ,EAWE;EACE,IAAIA,aAAa,KAAK,IAAtB,EAA4B;AACxB,IAAA,OAAOhB,KAAK,CAAC;AACTrL,MAAAA,MAAM,EAANA,MADS;AAETQ,MAAAA,YAAY,EAAE6L,aAAAA;AAFL,KAAD,CAAZ,CAAA;AAIH,GAAA;;AAED,EAAA,IAAI,OAAOA,aAAP,KAAyB,QAA7B,EAAuC;AACnC,IAAA,IAAQlM,QAAR,GAAmCkM,aAAnC,CAAQlM,QAAR;AAAA,QAAkBK,YAAlB,GAAmC6L,aAAnC,CAAkB7L,YAAlB,CAAA;AAEA,IAAA,OAAO6K,KAAK,CAAC;AACTrL,MAAAA,MAAM,EAANA,MADS;AAETG,MAAAA,QAAQ,EAARA,QAFS;AAGTK,MAAAA,YAAY,EAAZA,YAAAA;AAHS,KAAD,CAAZ,CAAA;AAKH,GAAA;;AAED,EAAA,IAAI,OAAO6L,aAAP,KAAyB,UAA7B,EAAyC;AACrC,IAAA,OAAOhB,KAAK,CAAC;AACTrL,MAAAA,MAAM,EAANA,MADS;AAETG,MAAAA,QAAQ,EAAEkM,aAFD;AAGT7L,MAAAA,YAAY,EAAE,IAAA;AAHL,KAAD,CAAZ,CAAA;AAKH,GAAA;;AAED,EAAA,OAAO6K,KAAK,CAAC;AAAErL,IAAAA,MAAM,EAANA,MAAAA;AAAF,GAAD,CAAZ,CAAA;AACH,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAqL,KAAK,CAACrL,MAAN,GAAeA,MAAf,CAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACAqL,KAAK,CAACpL,OAAN,GAAgB,SAASA,OAAT,CAAiBA,OAAjB,EAAkC;AAC9C,EAAA,OAAOoL,KAAK,CAAC;AAAEpL,IAAAA,OAAO,EAAPA,OAAAA;AAAF,GAAD,CAAZ,CAAA;AACH,CAFD,CAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAoL,KAAK,CAACnL,OAAN,GAAgB,SAASA,OAAT,CAAiBA,OAAjB,EAAkC;AAC9C,EAAA,OAAOmL,KAAK,CAAC;AAAEnL,IAAAA,OAAO,EAAPA,OAAAA;AAAF,GAAD,CAAZ,CAAA;AACH,CAFD,CAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAmL,KAAK,CAAC7F,OAAN,GAAgB,UAAUpF,WAAV,EAA+B;AAC3C,EAAA,OAAOiL,KAAK,CAAC;AAAEjL,IAAAA,WAAW,EAAXA,WAAAA;AAAF,GAAD,CAAZ,CAAA;AACH,CAFD,CAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAiL,KAAK,CAACiB,OAAN,GAAgBjB,KAAK,CAAC;AAClB5L,EAAAA,SAAS,EAAE,IADO;AAElBe,EAAAA,YAAY,EAAE,IAAA;AAFI,CAAD,CAArB,CAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA6K,KAAK,CAACkB,KAAN,GAAclB,KAAK,CAAC;AAAE3L,EAAAA,OAAO,EAAE,IAAA;AAAX,CAAD,CAAnB,CAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA2L,KAAK,CAACmB,SAAN,GAAkBnB,KAAK,CAAC;AAAE1L,EAAAA,YAAY,EAAE,IAAA;AAAhB,CAAD,CAAvB,CAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA0L,KAAK,CAACoB,aAAN,GAAsB,UAAUpM,UAAV,EAAiC;AACnD,EAAA,OAAOgL,KAAK,CAAC;AAAE1L,IAAAA,YAAY,EAAE,IAAhB;AAAsBU,IAAAA,UAAU,EAAVA,UAAAA;AAAtB,GAAD,CAAZ,CAAA;AACH,CAFD,CAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAgL,KAAK,CAACqB,OAAN,GAAgBrB,KAAK,CAAC;AAAEzL,EAAAA,cAAc,EAAE,IAAA;AAAlB,CAAD,CAArB,CAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACAyL,KAAK,CAAC9K,aAAN,GAAsB,UAClBA,aADkB,EAAA;AAAA,EAAA,OAEjB8K,KAAK,CAAC;AAAE9K,IAAAA,aAAa,EAAbA,aAAAA;AAAF,GAAD,CAFY,CAAA;AAAA,CAAtB,CAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA8K,KAAK,CAAC/K,iBAAN,GAA0B,UACtBA,iBADsB,EAAA;AAAA,EAAA,OAErB+K,KAAK,CAAC;AAAE/K,IAAAA,iBAAiB,EAAjBA,iBAAAA;AAAF,GAAD,CAFgB,CAAA;AAAA,CAA1B;AAKA;;;AACAkC,MAAM,CAACC,cAAP,CAAsB4I,KAAtB,EAA6B,SAA7B,EAAwC;AACpC3I,EAAAA,YAAY,EAAE,KADsB;AAEpCC,EAAAA,UAAU,EAAE,KAFwB;AAGpCC,EAAAA,KAAK,EAAEyI,KAH6B;AAIpCxI,EAAAA,QAAQ,EAAE,KAAA;AAJ0B,CAAxC,CAAA;;;;"}