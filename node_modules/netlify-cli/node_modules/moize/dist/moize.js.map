{"version": 3, "file": "moize.js", "sources": ["../src/constants.ts", "../src/utils.ts", "../src/maxAge.ts", "../src/stats.ts", "../src/instance.ts", "../src/component.ts", "../src/maxArgs.ts", "../src/serialize.ts", "../src/options.ts", "../src/updateCacheForKey.ts", "../src/index.ts"], "sourcesContent": ["import type { Options } from '../index.d';\n\n/**\n * @private\n *\n * @constant DEFAULT_OPTIONS\n */\nexport const DEFAULT_OPTIONS: Options = {\n    isDeepEqual: false,\n    isPromise: false,\n    isReact: false,\n    isSerialized: false,\n    isShallowEqual: false,\n    matchesArg: undefined,\n    matchesKey: undefined,\n    maxAge: undefined,\n    maxArgs: undefined,\n    maxSize: 1,\n    onExpire: undefined,\n    profileName: undefined,\n    serializer: undefined,\n    updateCacheForKey: undefined,\n    transformArgs: undefined,\n    updateExpire: false,\n};\n", "import { DEFAULT_OPTIONS } from './constants';\n\nimport type {\n    Expiration,\n    Fn,\n    <PERSON>E<PERSON>l,\n    IsMatching<PERSON>ey,\n    Key,\n    Moizeable,\n    Moized,\n    Options,\n} from '../index.d';\n\n/**\n * @private\n *\n * @description\n * method to combine functions and return a single function that fires them all\n *\n * @param functions the functions to compose\n * @returns the composed function\n */\nexport function combine<Arg, Result>(\n    ...functions: Fn<Arg>[]\n): Fn<Arg, Result> | undefined {\n    return functions.reduce(function (f: any, g: any) {\n        if (typeof f === 'function') {\n            return typeof g === 'function'\n                ? function (this: any) {\n                      f.apply(this, arguments);\n                      g.apply(this, arguments);\n                  }\n                : f;\n        }\n\n        if (typeof g === 'function') {\n            return g;\n        }\n    });\n}\n\n/**\n * @private\n *\n * @description\n * method to compose functions and return a single function\n *\n * @param functions the functions to compose\n * @returns the composed function\n */\nexport function compose<Method>(...functions: Method[]): Method {\n    return functions.reduce(function (f: any, g: any) {\n        if (typeof f === 'function') {\n            return typeof g === 'function'\n                ? function (this: any) {\n                      return f(g.apply(this, arguments));\n                  }\n                : f;\n        }\n\n        if (typeof g === 'function') {\n            return g;\n        }\n    });\n}\n\n/**\n * @private\n *\n * @description\n * find the index of the expiration based on the key\n *\n * @param expirations the list of expirations\n * @param key the key to match\n * @returns the index of the expiration\n */\nexport function findExpirationIndex(expirations: Expiration[], key: Key) {\n    for (let index = 0; index < expirations.length; index++) {\n        if (expirations[index].key === key) {\n            return index;\n        }\n    }\n\n    return -1;\n}\n\n/**\n * @private\n *\n * @description\n * create function that finds the index of the key in the list of cache keys\n *\n * @param isEqual the function to test individual argument equality\n * @param isMatchingKey the function to test full key equality\n * @returns the function that finds the index of the key\n */\nexport function createFindKeyIndex(\n    isEqual: IsEqual,\n    isMatchingKey: IsMatchingKey | undefined\n) {\n    const areKeysEqual: IsMatchingKey =\n        typeof isMatchingKey === 'function'\n            ? isMatchingKey\n            : function (cacheKey: Key, key: Key) {\n                  for (let index = 0; index < key.length; index++) {\n                      if (!isEqual(cacheKey[index], key[index])) {\n                          return false;\n                      }\n                  }\n\n                  return true;\n              };\n\n    return function (keys: Key[], key: Key) {\n        for (let keysIndex = 0; keysIndex < keys.length; keysIndex++) {\n            if (\n                keys[keysIndex].length === key.length &&\n                areKeysEqual(keys[keysIndex], key)\n            ) {\n                return keysIndex;\n            }\n        }\n\n        return -1;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * merge two options objects, combining or composing functions as necessary\n *\n * @param originalOptions the options that already exist on the method\n * @param newOptions the new options to merge\n * @returns the merged options\n */\nexport function mergeOptions(\n    originalOptions: Options,\n    newOptions: Options\n): Options {\n    return !newOptions || newOptions === DEFAULT_OPTIONS\n        ? originalOptions\n        : {\n              ...originalOptions,\n              ...newOptions,\n              onCacheAdd: combine(\n                  originalOptions.onCacheAdd,\n                  newOptions.onCacheAdd\n              ),\n              onCacheChange: combine(\n                  originalOptions.onCacheChange,\n                  newOptions.onCacheChange\n              ),\n              onCacheHit: combine(\n                  originalOptions.onCacheHit,\n                  newOptions.onCacheHit\n              ),\n              transformArgs: compose(\n                  originalOptions.transformArgs,\n                  newOptions.transformArgs\n              ),\n          };\n}\n\nexport function isMoized(fn: Moizeable | Moized | Options): fn is Moized {\n    return typeof fn === 'function' && (fn as Moizeable).isMoized;\n}\n\nexport function setName(\n    fn: Moized,\n    originalFunctionName: string,\n    profileName: string\n) {\n    try {\n        const name = profileName || originalFunctionName || 'anonymous';\n\n        Object.defineProperty(fn, 'name', {\n            configurable: true,\n            enumerable: false,\n            value: `moized(${name})`,\n            writable: true,\n        });\n    } catch {\n        // For engines where `function.name` is not configurable, do nothing.\n    }\n}\n", "import { createFindKeyIndex, findExpirationIndex } from './utils';\n\nimport type {\n    Cache,\n    Expiration,\n    Fn,\n    IsEqual,\n    IsMatching<PERSON>ey,\n    Key,\n    OnCacheOperation,\n    Options,\n} from '../index.d';\n\n/**\n * @private\n *\n * @description\n * clear an active expiration and remove it from the list if applicable\n *\n * @param expirations the list of expirations\n * @param key the key to clear\n * @param shouldRemove should the expiration be removed from the list\n */\nexport function clearExpiration(\n    expirations: Expiration[],\n    key: Key,\n    shouldRemove?: boolean\n) {\n    const expirationIndex = findExpirationIndex(expirations, key);\n\n    if (expirationIndex !== -1) {\n        clearTimeout(expirations[expirationIndex].timeoutId);\n\n        if (shouldRemove) {\n            expirations.splice(expirationIndex, 1);\n        }\n    }\n}\n\n/**\n * @private\n *\n * @description\n * Create the timeout for the given expiration method. If the ability to `unref`\n * exists, then apply it to avoid process locks in NodeJS.\n *\n * @param expirationMethod the method to fire upon expiration\n * @param maxAge the time to expire after\n * @returns the timeout ID\n */\nexport function createTimeout(expirationMethod: () => void, maxAge: number) {\n    const timeoutId = setTimeout(expirationMethod, maxAge);\n\n    if (typeof timeoutId.unref === 'function') {\n        timeoutId.unref();\n    }\n\n    return timeoutId;\n}\n\n/**\n * @private\n *\n * @description\n * create a function that, when an item is added to the cache, adds an expiration for it\n *\n * @param expirations the mutable expirations array\n * @param options the options passed on initialization\n * @param isEqual the function to check argument equality\n * @param isMatchingKey the function to check complete key equality\n * @returns the onCacheAdd function to handle expirations\n */\nexport function createOnCacheAddSetExpiration(\n    expirations: Expiration[],\n    options: Options,\n    isEqual: IsEqual,\n    isMatchingKey: IsMatchingKey\n): OnCacheOperation {\n    const { maxAge } = options;\n\n    return function onCacheAdd(\n        cache: Cache,\n        moizedOptions: Options,\n        moized: Fn\n    ) {\n        const key: any = cache.keys[0];\n\n        if (findExpirationIndex(expirations, key) === -1) {\n            const expirationMethod = function () {\n                const findKeyIndex = createFindKeyIndex(isEqual, isMatchingKey);\n\n                const keyIndex: number = findKeyIndex(cache.keys, key);\n                const value: any = cache.values[keyIndex];\n\n                if (~keyIndex) {\n                    cache.keys.splice(keyIndex, 1);\n                    cache.values.splice(keyIndex, 1);\n\n                    if (typeof options.onCacheChange === 'function') {\n                        options.onCacheChange(cache, moizedOptions, moized);\n                    }\n                }\n\n                clearExpiration(expirations, key, true);\n\n                if (\n                    typeof options.onExpire === 'function' &&\n                    options.onExpire(key) === false\n                ) {\n                    cache.keys.unshift(key);\n                    cache.values.unshift(value);\n\n                    onCacheAdd(cache, moizedOptions, moized);\n\n                    if (typeof options.onCacheChange === 'function') {\n                        options.onCacheChange(cache, moizedOptions, moized);\n                    }\n                }\n            };\n\n            expirations.push({\n                expirationMethod,\n                key,\n                timeoutId: createTimeout(expirationMethod, maxAge),\n            });\n        }\n    };\n}\n\n/**\n * @private\n *\n * @description\n * creates a function that, when a cache item is hit, reset the expiration\n *\n * @param expirations the mutable expirations array\n * @param options the options passed on initialization\n * @returns the onCacheAdd function to handle expirations\n */\nexport function createOnCacheHitResetExpiration(\n    expirations: Expiration[],\n    options: Options\n): OnCacheOperation {\n    return function onCacheHit(cache: Cache) {\n        const key = cache.keys[0];\n        const expirationIndex = findExpirationIndex(expirations, key);\n\n        if (~expirationIndex) {\n            clearExpiration(expirations, key, false);\n\n            expirations[expirationIndex].timeoutId = createTimeout(\n                expirations[expirationIndex].expirationMethod,\n                options.maxAge\n            );\n        }\n    };\n}\n\n/**\n * @private\n *\n * @description\n * get the micro-memoize options specific to the maxAge option\n *\n * @param expirations the expirations for the memoized function\n * @param options the options passed to the moizer\n * @param isEqual the function to test equality of the key on a per-argument basis\n * @param isMatchingKey the function to test equality of the whole key\n * @returns the object of options based on the entries passed\n */\nexport function getMaxAgeOptions(\n    expirations: Expiration[],\n    options: Options,\n    isEqual: IsEqual,\n    isMatchingKey: IsMatchingKey\n): {\n    onCacheAdd: OnCacheOperation | undefined;\n    onCacheHit: OnCacheOperation | undefined;\n} {\n    const onCacheAdd =\n        typeof options.maxAge === 'number' && isFinite(options.maxAge)\n            ? createOnCacheAddSetExpiration(\n                  expirations,\n                  options,\n                  isEqual,\n                  isMatchingKey\n              )\n            : undefined;\n\n    return {\n        onCacheAdd,\n        onCacheHit:\n            onCacheAdd && options.updateExpire\n                ? createOnCacheHitResetExpiration(expirations, options)\n                : undefined,\n    };\n}\n", "import type {\n    Fn,\n    FunctionalComponent,\n    GlobalStatsObject,\n    OnCacheOperation,\n    Options,\n    StatsCache,\n    StatsProfile,\n} from '../index.d';\n\nexport const statsCache: StatsCache = {\n    anonymousProfileNameCounter: 1,\n    isCollectingStats: false,\n    profiles: {},\n};\n\nlet hasWarningDisplayed = false;\n\nexport function clearStats(profileName?: string) {\n    if (profileName) {\n        delete statsCache.profiles[profileName];\n    } else {\n        statsCache.profiles = {};\n    }\n}\n\n/**\n * @private\n *\n * @description\n * activate stats collection\n *\n * @param isCollectingStats should stats be collected\n */\nexport function collectStats(isCollectingStats = true) {\n    statsCache.isCollectingStats = isCollectingStats;\n}\n\n/**\n * @private\n *\n * @description\n * create a function that increments the number of calls for the specific profile\n */\nexport function createOnCacheAddIncrementCalls(options: Options) {\n    const { profileName } = options;\n\n    return function () {\n        if (profileName && !statsCache.profiles[profileName]) {\n            statsCache.profiles[profileName] = {\n                calls: 0,\n                hits: 0,\n            };\n        }\n\n        statsCache.profiles[profileName].calls++;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * create a function that increments the number of calls and cache hits for the specific profile\n */\nexport function createOnCacheHitIncrementCallsAndHits(options: Options) {\n    return function () {\n        const { profiles } = statsCache;\n        const { profileName } = options;\n\n        if (!profiles[profileName]) {\n            profiles[profileName] = {\n                calls: 0,\n                hits: 0,\n            };\n        }\n\n        profiles[profileName].calls++;\n        profiles[profileName].hits++;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * get the profileName for the function when one is not provided\n *\n * @param fn the function to be memoized\n * @returns the derived profileName for the function\n */\nexport function getDefaultProfileName(\n    fn: Fn | FunctionalComponent<Record<string, unknown>>\n) {\n    return (\n        (fn as FunctionalComponent<Record<string, unknown>>).displayName ||\n        fn.name ||\n        `Anonymous ${statsCache.anonymousProfileNameCounter++}`\n    );\n}\n\n/**\n * @private\n *\n * @description\n * get the usage percentage based on the number of hits and total calls\n *\n * @param calls the number of calls made\n * @param hits the number of cache hits when called\n * @returns the usage as a percentage string\n */\nexport function getUsagePercentage(calls: number, hits: number) {\n    return calls ? `${((hits / calls) * 100).toFixed(4)}%` : '0.0000%';\n}\n\n/**\n * @private\n *\n * @description\n * get the statistics for a given method or all methods\n *\n * @param [profileName] the profileName to get the statistics for (get all when not provided)\n * @returns the object with stats information\n */\nexport function getStats(profileName?: string): GlobalStatsObject {\n    if (!statsCache.isCollectingStats && !hasWarningDisplayed) {\n        console.warn(\n            'Stats are not currently being collected, please run \"collectStats\" to enable them.'\n        ); // eslint-disable-line no-console\n\n        hasWarningDisplayed = true;\n    }\n\n    const { profiles } = statsCache;\n\n    if (profileName) {\n        if (!profiles[profileName]) {\n            return {\n                calls: 0,\n                hits: 0,\n                usage: '0.0000%',\n            };\n        }\n\n        const { [profileName]: profile } = profiles;\n\n        return {\n            ...profile,\n            usage: getUsagePercentage(profile.calls, profile.hits),\n        };\n    }\n\n    const completeStats: StatsProfile = Object.keys(statsCache.profiles).reduce(\n        (completeProfiles, profileName) => {\n            completeProfiles.calls += profiles[profileName].calls;\n            completeProfiles.hits += profiles[profileName].hits;\n\n            return completeProfiles;\n        },\n        {\n            calls: 0,\n            hits: 0,\n        }\n    );\n\n    return {\n        ...completeStats,\n        profiles: Object.keys(profiles).reduce(\n            (computedProfiles, profileName) => {\n                computedProfiles[profileName] = getStats(profileName);\n\n                return computedProfiles;\n            },\n            {} as Record<string, StatsProfile>\n        ),\n        usage: getUsagePercentage(completeStats.calls, completeStats.hits),\n    };\n}\n\n/**\n * @private\n *\n * @function getStatsOptions\n *\n * @description\n * get the options specific to storing statistics\n *\n * @param {Options} options the options passed to the moizer\n * @returns {Object} the options specific to keeping stats\n */\nexport function getStatsOptions(options: Options): {\n    onCacheAdd?: OnCacheOperation;\n    onCacheHit?: OnCacheOperation;\n} {\n    return statsCache.isCollectingStats\n        ? {\n              onCacheAdd: createOnCacheAddIncrementCalls(options),\n              onCacheHit: createOnCacheHitIncrementCallsAndHits(options),\n          }\n        : {};\n}\n", "import { clearExpiration } from './maxAge';\nimport { clearStats, getStats } from './stats';\nimport { createFindKeyIndex } from './utils';\n\nimport type {\n    Fn,\n    Key,\n    Memoized,\n    Moizeable,\n    MoizeConfiguration,\n    Moized,\n    Options,\n    StatsProfile,\n} from '../index.d';\n\nconst ALWAYS_SKIPPED_PROPERTIES: Record<string, boolean> = {\n    arguments: true,\n    callee: true,\n    caller: true,\n    constructor: true,\n    length: true,\n    name: true,\n    prototype: true,\n};\n\n/**\n * @private\n *\n * @description\n * copy the static properties from the original function to the moized\n * function\n *\n * @param originalFn the function copying from\n * @param newFn the function copying to\n * @param skippedProperties the list of skipped properties, if any\n */\nexport function copyStaticProperties(\n    originalFn: Fn,\n    newFn: Fn,\n    skippedProperties: string[] = []\n) {\n    Object.getOwnPropertyNames(originalFn).forEach((property) => {\n        if (\n            !ALWAYS_SKIPPED_PROPERTIES[property] &&\n            skippedProperties.indexOf(property) === -1\n        ) {\n            const descriptor = Object.getOwnPropertyDescriptor(\n                originalFn,\n                property\n            );\n\n            if (descriptor.get || descriptor.set) {\n                Object.defineProperty(newFn, property, descriptor);\n            } else {\n                newFn[property as keyof typeof newFn] =\n                    originalFn[property as keyof typeof originalFn];\n            }\n        }\n    });\n}\n\n/**\n * @private\n *\n * @description\n * add methods to the moized fuction object that allow extra features\n *\n * @param memoized the memoized function from micro-memoize\n */\nexport function addInstanceMethods<OriginalFn extends Fn>(\n    memoized: Moizeable,\n    { expirations }: MoizeConfiguration<OriginalFn>\n) {\n    const { options } = memoized;\n\n    const findKeyIndex = createFindKeyIndex(\n        options.isEqual,\n        options.isMatchingKey\n    );\n\n    const moized = memoized as unknown as Moized<OriginalFn, Options>;\n\n    moized.clear = function () {\n        const {\n            _microMemoizeOptions: { onCacheChange },\n            cache,\n        } = moized;\n\n        cache.keys.length = 0;\n        cache.values.length = 0;\n\n        if (onCacheChange) {\n            onCacheChange(cache, moized.options, moized);\n        }\n\n        return true;\n    };\n\n    moized.clearStats = function () {\n        clearStats(moized.options.profileName);\n    };\n\n    moized.get = function (key: Key) {\n        const {\n            _microMemoizeOptions: { transformKey },\n            cache,\n        } = moized;\n\n        const cacheKey = transformKey ? transformKey(key) : key;\n        const keyIndex = findKeyIndex(cache.keys, cacheKey);\n\n        return keyIndex !== -1 ? moized.apply(this, key) : undefined;\n    };\n\n    moized.getStats = function (): StatsProfile {\n        return getStats(moized.options.profileName);\n    };\n\n    moized.has = function (key: Key) {\n        const { transformKey } = moized._microMemoizeOptions;\n\n        const cacheKey = transformKey ? transformKey(key) : key;\n\n        return findKeyIndex(moized.cache.keys, cacheKey) !== -1;\n    };\n\n    moized.keys = function () {\n        return moized.cacheSnapshot.keys;\n    };\n\n    moized.remove = function (key: Key) {\n        const {\n            _microMemoizeOptions: { onCacheChange, transformKey },\n            cache,\n        } = moized;\n\n        const keyIndex = findKeyIndex(\n            cache.keys,\n            transformKey ? transformKey(key) : key\n        );\n\n        if (keyIndex === -1) {\n            return false;\n        }\n\n        const existingKey = cache.keys[keyIndex];\n\n        cache.keys.splice(keyIndex, 1);\n        cache.values.splice(keyIndex, 1);\n\n        if (onCacheChange) {\n            onCacheChange(cache, moized.options, moized);\n        }\n\n        clearExpiration(expirations, existingKey, true);\n\n        return true;\n    };\n\n    moized.set = function (key: Key, value: any) {\n        const { _microMemoizeOptions, cache, options } = moized;\n        const { onCacheAdd, onCacheChange, transformKey } =\n            _microMemoizeOptions;\n\n        const cacheKey = transformKey ? transformKey(key) : key;\n        const keyIndex = findKeyIndex(cache.keys, cacheKey);\n\n        if (keyIndex === -1) {\n            const cutoff = options.maxSize - 1;\n\n            if (cache.size > cutoff) {\n                cache.keys.length = cutoff;\n                cache.values.length = cutoff;\n            }\n\n            cache.keys.unshift(cacheKey);\n            cache.values.unshift(value);\n\n            if (options.isPromise) {\n                cache.updateAsyncCache(moized);\n            }\n\n            if (onCacheAdd) {\n                onCacheAdd(cache, options, moized);\n            }\n\n            if (onCacheChange) {\n                onCacheChange(cache, options, moized);\n            }\n        } else {\n            const existingKey = cache.keys[keyIndex];\n\n            cache.values[keyIndex] = value;\n\n            if (keyIndex > 0) {\n                cache.orderByLru(existingKey, value, keyIndex);\n            }\n\n            if (options.isPromise) {\n                cache.updateAsyncCache(moized);\n            }\n\n            if (typeof onCacheChange === 'function') {\n                onCacheChange(cache, options, moized);\n            }\n        }\n    };\n\n    moized.values = function () {\n        return moized.cacheSnapshot.values;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * add propeties to the moized fuction object that surfaces extra information\n *\n * @param memoized the memoized function\n * @param expirations the list of expirations for cache items\n * @param options the options passed to the moizer\n * @param originalFunction the function that is being memoized\n */\nexport function addInstanceProperties<OriginalFn extends Moizeable>(\n    memoized: Memoized<OriginalFn>,\n    {\n        expirations,\n        options: moizeOptions,\n        originalFunction,\n    }: MoizeConfiguration<OriginalFn>\n) {\n    const { options: microMemoizeOptions } = memoized;\n\n    Object.defineProperties(memoized, {\n        _microMemoizeOptions: {\n            configurable: true,\n            get() {\n                return microMemoizeOptions;\n            },\n        },\n\n        cacheSnapshot: {\n            configurable: true,\n            get() {\n                const { cache: currentCache } = memoized;\n\n                return {\n                    keys: currentCache.keys.slice(0),\n                    size: currentCache.size,\n                    values: currentCache.values.slice(0),\n                };\n            },\n        },\n\n        expirations: {\n            configurable: true,\n            get() {\n                return expirations;\n            },\n        },\n\n        expirationsSnapshot: {\n            configurable: true,\n            get() {\n                return expirations.slice(0);\n            },\n        },\n\n        isMoized: {\n            configurable: true,\n            get() {\n                return true;\n            },\n        },\n\n        options: {\n            configurable: true,\n            get() {\n                return moizeOptions;\n            },\n        },\n\n        originalFunction: {\n            configurable: true,\n            get() {\n                return originalFunction;\n            },\n        },\n    });\n\n    const moized = memoized as unknown as Moized<OriginalFn, Options>;\n\n    copyStaticProperties(originalFunction, moized);\n}\n\n/**\n * @private\n *\n * @description\n * add methods and properties to the memoized function for more features\n *\n * @param memoized the memoized function\n * @param configuration the configuration object for the instance\n * @returns the memoized function passed\n */\nexport function createMoizeInstance<\n    OriginalFn extends Moizeable,\n    CombinedOptions extends Options\n>(\n    memoized: Memoized<OriginalFn>,\n    configuration: MoizeConfiguration<OriginalFn>\n) {\n    addInstanceMethods<OriginalFn>(memoized, configuration);\n    addInstanceProperties<OriginalFn>(memoized, configuration);\n\n    return memoized as Moized<OriginalFn, CombinedOptions>;\n}\n", "import { copyStaticProperties } from './instance';\nimport { setName } from './utils';\n\nimport type {\n    Moize,\n    Moized as MoizedFunction,\n    Moizeable,\n    Options,\n} from '../index.d';\n\n// This was stolen from React internals, which allows us to create React elements without needing\n// a dependency on the React library itself.\nconst REACT_ELEMENT_TYPE =\n    typeof Symbol === 'function' && Symbol.for\n        ? Symbol.for('react.element')\n        : 0xeac7;\n\n/**\n * @private\n *\n * @description\n * Create a component that memoizes based on `props` and legacy `context`\n * on a per-instance basis. This requires creating a component class to\n * store the memoized function. The cost is quite low, and avoids the\n * need to have access to the React dependency by basically re-creating\n * the basic essentials for a component class and the results of the\n * `createElement` function.\n *\n * @param moizer the top-level moize method\n * @param fn the component to memoize\n * @param options the memoization options\n * @returns the memoized component\n */\nexport function createMoizedComponent<OriginalFn extends Moizeable>(\n    moizer: Moize,\n    fn: OriginalFn,\n    options: Options\n) {\n    /**\n     * This is a hack override setting the necessary options\n     * for a React component to be memoized. In the main `moize`\n     * method, if the `isReact` option is set it is short-circuited\n     * to call this function, and these overrides allow the\n     * necessary transformKey method to be derived.\n     *\n     * The order is based on:\n     * 1) Set the necessary aspects of transformKey for React components.\n     * 2) Allow setting of other options and overrides of those aspects\n     *    if desired (for example, `isDeepEqual` will use deep equality).\n     * 3) Always set `isReact` to false to prevent infinite loop.\n     */\n    const reactMoizer = moizer({\n        maxArgs: 2,\n        isShallowEqual: true,\n        ...options,\n        isReact: false,\n    });\n\n    if (!fn.displayName) {\n        // @ts-ignore - allow setting of displayName\n        fn.displayName = fn.name || 'Component';\n    }\n\n    function Moized<Props extends Record<string, unknown>, Context, Updater>(\n        this: any,\n        props: Props,\n        context: Context,\n        updater: Updater\n    ) {\n        this.props = props;\n        this.context = context;\n        this.updater = updater;\n\n        this.MoizedComponent = reactMoizer(fn);\n    }\n\n    Moized.prototype.isReactComponent = {};\n\n    Moized.prototype.render = function () {\n        return {\n            $$typeof: REACT_ELEMENT_TYPE,\n            type: this.MoizedComponent,\n            props: this.props,\n            ref: null,\n            key: null,\n            _owner: null,\n        } as JSX.Element;\n    };\n\n    copyStaticProperties(fn, Moized, ['contextType', 'contextTypes']);\n\n    Moized.displayName = `Moized(${fn.displayName || fn.name || 'Component'})`;\n\n    setName(Moized as MoizedFunction, fn.name, options.profileName);\n\n    return Moized;\n}\n", "import type { Key } from '../index.d';\n\nexport function createGetInitialArgs(size: number) {\n    /**\n     * @private\n     *\n     * @description\n     * take the first N number of items from the array (faster than slice)\n     *\n     * @param args the args to take from\n     * @returns the shortened list of args as an array\n     */\n    return function (args: Key): Key {\n        if (size >= args.length) {\n            return args;\n        }\n\n        if (size === 0) {\n            return [];\n        }\n\n        if (size === 1) {\n            return [args[0]];\n        }\n\n        if (size === 2) {\n            return [args[0], args[1]];\n        }\n\n        if (size === 3) {\n            return [args[0], args[1], args[2]];\n        }\n\n        const clone = [];\n\n        for (let index = 0; index < size; index++) {\n            clone[index] = args[index];\n        }\n\n        return clone;\n    };\n}\n", "import type { Key, Options } from '../index.d';\n\n/**\n * @function getCutoff\n *\n * @description\n * faster `Array.prototype.indexOf` implementation build for slicing / splicing\n *\n * @param array the array to match the value in\n * @param value the value to match\n * @returns the matching index, or -1\n */\nfunction getCutoff(array: any[], value: any) {\n    const { length } = array;\n\n    for (let index = 0; index < length; ++index) {\n        if (array[index] === value) {\n            return index + 1;\n        }\n    }\n\n    return 0;\n}\n\n/**\n * @private\n *\n * @description\n * custom replacer for the stringify function\n *\n * @returns if function then toString of it, else the value itself\n */\nexport function createDefaultReplacer() {\n    const cache: any[] = [];\n    const keys: string[] = [];\n\n    return function defaultReplacer(key: string, value: any) {\n        const type = typeof value;\n\n        if (type === 'function' || type === 'symbol') {\n            return value.toString();\n        }\n\n        if (typeof value === 'object') {\n            if (cache.length) {\n                const thisCutoff = getCutoff(cache, this);\n\n                if (thisCutoff === 0) {\n                    cache[cache.length] = this;\n                } else {\n                    cache.splice(thisCutoff);\n                    keys.splice(thisCutoff);\n                }\n\n                keys[keys.length] = key;\n\n                const valueCutoff = getCutoff(cache, value);\n\n                if (valueCutoff !== 0) {\n                    return `[ref=${\n                        keys.slice(0, valueCutoff).join('.') || '.'\n                    }]`;\n                }\n            } else {\n                cache[0] = value;\n                keys[0] = key;\n            }\n\n            return value;\n        }\n\n        return '' + value;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * get the stringified version of the argument passed\n *\n * @param arg argument to stringify\n * @returns the stringified argument\n */\nexport function getStringifiedArgument<Type>(arg: Type) {\n    const typeOfArg = typeof arg;\n\n    return arg && (typeOfArg === 'object' || typeOfArg === 'function')\n        ? JSON.stringify(arg, createDefaultReplacer())\n        : arg;\n}\n\n/**\n * @private\n *\n * @description\n * serialize the arguments passed\n *\n * @param options the options passed to the moizer\n * @param options.maxArgs the cap on the number of arguments used in serialization\n * @returns argument serialization method\n */\nexport function defaultArgumentSerializer(args: Key) {\n    let key = '|';\n\n    for (let index = 0; index < args.length; index++) {\n        key += getStringifiedArgument(args[index]) + '|';\n    }\n\n    return [key];\n}\n\n/**\n * @private\n *\n * @description\n * based on the options passed, either use the serializer passed or generate the internal one\n *\n * @param options the options passed to the moized function\n * @returns the function to use in serializing the arguments\n */\nexport function getSerializerFunction(options: Options) {\n    return typeof options.serializer === 'function'\n        ? options.serializer\n        : defaultArgumentSerializer;\n}\n\n/**\n * @private\n *\n * @description\n * are the serialized keys equal to one another\n *\n * @param cacheKey the cache key to compare\n * @param key the key to test\n * @returns are the keys equal\n */\nexport function getIsSerializedKeyEqual(cacheKey: Key, key: Key) {\n    return cacheKey[0] === key[0];\n}\n", "import { deepEqual, sameValueZeroEqual, shallowEqual } from 'fast-equals';\nimport { createGetInitialArgs } from './maxArgs';\nimport { getIsSerializedKeyEqual, getSerializerFunction } from './serialize';\nimport { compose } from './utils';\n\nimport type {\n    Cache,\n    IsEqual,\n    IsMatchingKey,\n    MicroMemoizeOptions,\n    Moized,\n    OnCacheOperation,\n    Options,\n    TransformKey,\n} from '../index.d';\n\nexport function createOnCacheOperation(\n    fn?: OnCacheOperation\n): OnCacheOperation {\n    if (typeof fn === 'function') {\n        return (\n            _cacheIgnored: Cache,\n            _microMemoizeOptionsIgnored: MicroMemoizeOptions,\n            memoized: Moized\n        ): void => fn(memoized.cache, memoized.options, memoized);\n    }\n}\n\n/**\n * @private\n *\n * @description\n * get the isEqual method passed to micro-memoize\n *\n * @param options the options passed to the moizer\n * @returns the isEqual method to apply\n */\nexport function getIsEqual(options: Options): IsEqual {\n    return (\n        options.matchesArg ||\n        (options.isDeepEqual && deepEqual) ||\n        (options.isShallowEqual && shallowEqual) ||\n        sameValueZeroEqual\n    );\n}\n\n/**\n * @private\n *\n * @description\n * get the isEqual method passed to micro-memoize\n *\n * @param options the options passed to the moizer\n * @returns the isEqual method to apply\n */\nexport function getIsMatchingKey(options: Options): IsMatchingKey | undefined {\n    return (\n        options.matchesKey ||\n        (options.isSerialized && getIsSerializedKeyEqual) ||\n        undefined\n    );\n}\n\n/**\n * @private\n *\n * @description\n * get the function that will transform the key based on the arguments passed\n *\n * @param options the options passed to the moizer\n * @returns the function to transform the key with\n */\nexport function getTransformKey(options: Options): TransformKey | undefined {\n    return compose(\n        options.isSerialized && getSerializerFunction(options),\n        typeof options.transformArgs === 'function' && options.transformArgs,\n        typeof options.maxArgs === 'number' &&\n            createGetInitialArgs(options.maxArgs)\n    ) as TransformKey;\n}\n", "import { copyStaticProperties } from './instance';\n\nimport type { Moized } from '../index.d';\n\nexport function createRefreshableMoized<MoizedFn extends Moized>(\n    moized: MoizedFn\n) {\n    const {\n        options: { updateCacheForKey },\n    } = moized;\n\n    /**\n     * @private\n     *\n     * @description\n     * Wrapper around already-`moize`d function which will intercept the memoization\n     * and call the underlying function directly with the purpose of updating the cache\n     * for the given key.\n     *\n     * Promise values use a tweak of the logic that exists at cache.updateAsyncCache, which\n     * reverts to the original value if the promise is rejected and there was already a cached\n     * value.\n     */\n    const refreshableMoized = function refreshableMoized(\n        this: any,\n        ...args: Parameters<typeof moized.fn>\n    ) {\n        if (!updateCacheForKey(args)) {\n            return moized.apply(this, args);\n        }\n\n        const result = moized.fn.apply(this, args);\n\n        moized.set(args, result);\n\n        return result;\n    } as typeof moized;\n\n    copyStaticProperties(moized, refreshableMoized);\n\n    return refreshableMoized;\n}\n", "import memoize from 'micro-memoize';\nimport { createMoizedComponent } from './component';\nimport { DEFAULT_OPTIONS } from './constants';\nimport { createMoizeInstance } from './instance';\nimport { getMaxAgeOptions } from './maxAge';\nimport {\n    createOnCacheOperation,\n    getIsEqual,\n    getIsMatchingKey,\n    getTransformKey,\n} from './options';\nimport {\n    clearStats,\n    collectStats,\n    getDefaultProfileName,\n    getStats,\n    getStatsOptions,\n    statsCache,\n} from './stats';\nimport { createRefreshableMoized } from './updateCacheForKey';\nimport { combine, compose, isMoized, mergeOptions, setName } from './utils';\n\nimport type {\n    Expiration,\n    IsEqual,\n    IsMatchingKey,\n    MicroMemoizeOptions,\n    Moize,\n    Moizeable,\n    Moized,\n    OnExpire,\n    Options,\n    Serialize,\n    TransformKey,\n    UpdateCacheForKey,\n} from '../index.d';\n\n/**\n * @module moize\n */\n\n/**\n * @description\n * memoize a function based its arguments passed, potentially improving runtime performance\n *\n * @example\n * import moize from 'moize';\n *\n * // standard implementation\n * const fn = (foo, bar) => `${foo} ${bar}`;\n * const memoizedFn = moize(fn);\n *\n * // implementation with options\n * const fn = async (id) => get(`http://foo.com/${id}`);\n * const memoizedFn = moize(fn, {isPromise: true, maxSize: 5});\n *\n * // implementation with convenience methods\n * const Foo = ({foo}) => <div>{foo}</div>;\n * const MemoizedFoo = moize.react(Foo);\n *\n * @param fn the function to memoized, or a list of options when currying\n * @param [options=DEFAULT_OPTIONS] the options to apply\n * @returns the memoized function\n */\nconst moize: Moize = function <\n    Fn extends Moizeable,\n    PassedOptions extends Options\n>(fn: Fn | PassedOptions, passedOptions?: PassedOptions) {\n    type CombinedOptions = Options & PassedOptions;\n\n    const options: Options = passedOptions || DEFAULT_OPTIONS;\n\n    if (isMoized(fn)) {\n        const moizeable = fn.originalFunction as Fn;\n        const mergedOptions = mergeOptions(\n            fn.options,\n            options\n        ) as CombinedOptions;\n\n        return moize<Fn, CombinedOptions>(moizeable, mergedOptions);\n    }\n\n    if (typeof fn === 'object') {\n        return function <\n            CurriedFn extends Moizeable,\n            CurriedOptions extends Options\n        >(\n            curriedFn: CurriedFn | CurriedOptions,\n            curriedOptions: CurriedOptions\n        ) {\n            type CombinedCurriedOptions = CombinedOptions & CurriedOptions;\n\n            if (typeof curriedFn === 'function') {\n                const mergedOptions = mergeOptions(\n                    fn as CombinedOptions,\n                    curriedOptions\n                ) as CombinedCurriedOptions;\n\n                return moize(curriedFn, mergedOptions);\n            }\n\n            const mergedOptions = mergeOptions(\n                fn as CombinedOptions,\n                curriedFn as CurriedOptions\n            );\n\n            return moize(mergedOptions);\n        };\n    }\n\n    if (options.isReact) {\n        return createMoizedComponent(moize, fn, options);\n    }\n\n    const coalescedOptions: Options = {\n        ...DEFAULT_OPTIONS,\n        ...options,\n        maxAge:\n            typeof options.maxAge === 'number' && options.maxAge >= 0\n                ? options.maxAge\n                : DEFAULT_OPTIONS.maxAge,\n        maxArgs:\n            typeof options.maxArgs === 'number' && options.maxArgs >= 0\n                ? options.maxArgs\n                : DEFAULT_OPTIONS.maxArgs,\n        maxSize:\n            typeof options.maxSize === 'number' && options.maxSize >= 0\n                ? options.maxSize\n                : DEFAULT_OPTIONS.maxSize,\n        profileName: options.profileName || getDefaultProfileName(fn),\n    };\n    const expirations: Array<Expiration> = [];\n\n    const {\n        matchesArg: equalsIgnored,\n        isDeepEqual: isDeepEqualIgnored,\n        isPromise,\n        isReact: isReactIgnored,\n        isSerialized: isSerialzedIgnored,\n        isShallowEqual: isShallowEqualIgnored,\n        matchesKey: matchesKeyIgnored,\n        maxAge: maxAgeIgnored,\n        maxArgs: maxArgsIgnored,\n        maxSize,\n        onCacheAdd,\n        onCacheChange,\n        onCacheHit,\n        onExpire: onExpireIgnored,\n        profileName: profileNameIgnored,\n        serializer: serializerIgnored,\n        updateCacheForKey,\n        transformArgs: transformArgsIgnored,\n        updateExpire: updateExpireIgnored,\n        ...customOptions\n    } = coalescedOptions;\n\n    const isEqual = getIsEqual(coalescedOptions);\n    const isMatchingKey = getIsMatchingKey(coalescedOptions);\n\n    const maxAgeOptions = getMaxAgeOptions(\n        expirations,\n        coalescedOptions,\n        isEqual,\n        isMatchingKey\n    );\n    const statsOptions = getStatsOptions(coalescedOptions);\n\n    const transformKey = getTransformKey(coalescedOptions);\n\n    const microMemoizeOptions: MicroMemoizeOptions = {\n        ...customOptions,\n        isEqual,\n        isMatchingKey,\n        isPromise,\n        maxSize,\n        onCacheAdd: createOnCacheOperation(\n            combine(\n                onCacheAdd,\n                maxAgeOptions.onCacheAdd,\n                statsOptions.onCacheAdd\n            )\n        ),\n        onCacheChange: createOnCacheOperation(onCacheChange),\n        onCacheHit: createOnCacheOperation(\n            combine(\n                onCacheHit,\n                maxAgeOptions.onCacheHit,\n                statsOptions.onCacheHit\n            )\n        ),\n        transformKey,\n    };\n\n    const memoized = memoize(fn, microMemoizeOptions);\n\n    let moized = createMoizeInstance<Fn, CombinedOptions>(memoized, {\n        expirations,\n        options: coalescedOptions,\n        originalFunction: fn,\n    });\n\n    if (updateCacheForKey) {\n        moized = createRefreshableMoized<typeof moized>(moized);\n    }\n\n    setName(moized, (fn as Moizeable).name, options.profileName);\n\n    return moized;\n};\n\n/**\n * @function\n * @name clearStats\n * @memberof module:moize\n * @alias moize.clearStats\n *\n * @description\n * clear all existing stats stored\n */\nmoize.clearStats = clearStats;\n\n/**\n * @function\n * @name collectStats\n * @memberof module:moize\n * @alias moize.collectStats\n *\n * @description\n * start collecting statistics\n */\nmoize.collectStats = collectStats;\n\n/**\n * @function\n * @name compose\n * @memberof module:moize\n * @alias moize.compose\n *\n * @description\n * method to compose moized methods and return a single moized function\n *\n * @param moized the functions to compose\n * @returns the composed function\n */\nmoize.compose = function (...moized: Moize[]) {\n    return compose<Moize>(...moized) || moize;\n};\n\n/**\n * @function\n * @name deep\n * @memberof module:moize\n * @alias moize.deep\n *\n * @description\n * should deep equality check be used\n *\n * @returns the moizer function\n */\nmoize.deep = moize({ isDeepEqual: true });\n\n/**\n * @function\n * @name getStats\n * @memberof module:moize\n * @alias moize.getStats\n *\n * @description\n * get the statistics of a given profile, or overall usage\n *\n * @returns statistics for a given profile or overall usage\n */\nmoize.getStats = getStats;\n\n/**\n * @function\n * @name infinite\n * @memberof module:moize\n * @alias moize.infinite\n *\n * @description\n * a moized method that will remove all limits from the cache size\n *\n * @returns the moizer function\n */\nmoize.infinite = moize({ maxSize: Infinity });\n\n/**\n * @function\n * @name isCollectingStats\n * @memberof module:moize\n * @alias moize.isCollectingStats\n *\n * @description\n * are stats being collected\n *\n * @returns are stats being collected\n */\nmoize.isCollectingStats = function isCollectingStats(): boolean {\n    return statsCache.isCollectingStats;\n};\n\n/**\n * @function\n * @name isMoized\n * @memberof module:moize\n * @alias moize.isMoized\n *\n * @description\n * is the fn passed a moized function\n *\n * @param fn the object to test\n * @returns is fn a moized function\n */\nmoize.isMoized = function isMoized(fn: any): fn is Moized {\n    return typeof fn === 'function' && !!fn.isMoized;\n};\n\n/**\n * @function\n * @name matchesArg\n * @memberof module:moize\n * @alias moize.matchesArg\n *\n * @description\n * a moized method where the arg matching method is the custom one passed\n *\n * @param keyMatcher the method to compare against those in cache\n * @returns the moizer function\n */\nmoize.matchesArg = function (argMatcher: IsEqual) {\n    return moize({ matchesArg: argMatcher });\n};\n\n/**\n * @function\n * @name matchesKey\n * @memberof module:moize\n * @alias moize.matchesKey\n *\n * @description\n * a moized method where the key matching method is the custom one passed\n *\n * @param keyMatcher the method to compare against those in cache\n * @returns the moizer function\n */\nmoize.matchesKey = function (keyMatcher: IsMatchingKey) {\n    return moize({ matchesKey: keyMatcher });\n};\n\nfunction maxAge<MaxAge extends number>(\n    maxAge: MaxAge\n): Moize<{ maxAge: MaxAge }>;\nfunction maxAge<MaxAge extends number, UpdateExpire extends boolean>(\n    maxAge: MaxAge,\n    expireOptions: UpdateExpire\n): Moize<{ maxAge: MaxAge; updateExpire: UpdateExpire }>;\nfunction maxAge<MaxAge extends number, ExpireHandler extends OnExpire>(\n    maxAge: MaxAge,\n    expireOptions: ExpireHandler\n): Moize<{ maxAge: MaxAge; onExpire: ExpireHandler }>;\nfunction maxAge<\n    MaxAge extends number,\n    ExpireHandler extends OnExpire,\n    ExpireOptions extends {\n        onExpire: ExpireHandler;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions: ExpireOptions\n): Moize<{ maxAge: MaxAge; onExpire: ExpireOptions['onExpire'] }>;\nfunction maxAge<\n    MaxAge extends number,\n    UpdateExpire extends boolean,\n    ExpireOptions extends {\n        updateExpire: UpdateExpire;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions: ExpireOptions\n): Moize<{ maxAge: MaxAge; updateExpire: UpdateExpire }>;\nfunction maxAge<\n    MaxAge extends number,\n    ExpireHandler extends OnExpire,\n    UpdateExpire extends boolean,\n    ExpireOptions extends {\n        onExpire: ExpireHandler;\n        updateExpire: UpdateExpire;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions: ExpireOptions\n): Moize<{\n    maxAge: MaxAge;\n    onExpire: ExpireHandler;\n    updateExpire: UpdateExpire;\n}>;\nfunction maxAge<\n    MaxAge extends number,\n    ExpireHandler extends OnExpire,\n    UpdateExpire extends boolean,\n    ExpireOptions extends {\n        onExpire?: ExpireHandler;\n        updateExpire?: UpdateExpire;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions?: ExpireHandler | UpdateExpire | ExpireOptions\n) {\n    if (expireOptions === true) {\n        return moize({\n            maxAge,\n            updateExpire: expireOptions,\n        });\n    }\n\n    if (typeof expireOptions === 'object') {\n        const { onExpire, updateExpire } = expireOptions;\n\n        return moize({\n            maxAge,\n            onExpire,\n            updateExpire,\n        });\n    }\n\n    if (typeof expireOptions === 'function') {\n        return moize({\n            maxAge,\n            onExpire: expireOptions,\n            updateExpire: true,\n        });\n    }\n\n    return moize({ maxAge });\n}\n\n/**\n * @function\n * @name maxAge\n * @memberof module:moize\n * @alias moize.maxAge\n *\n * @description\n * a moized method where the age of the cache is limited to the number of milliseconds passed\n *\n * @param maxAge the TTL of the value in cache\n * @returns the moizer function\n */\nmoize.maxAge = maxAge;\n\n/**\n * @function\n * @name maxArgs\n * @memberof module:moize\n * @alias moize.maxArgs\n *\n * @description\n * a moized method where the number of arguments used for determining cache is limited to the value passed\n *\n * @param maxArgs the number of args to base the key on\n * @returns the moizer function\n */\nmoize.maxArgs = function maxArgs(maxArgs: number) {\n    return moize({ maxArgs });\n};\n\n/**\n * @function\n * @name maxSize\n * @memberof module:moize\n * @alias moize.maxSize\n *\n * @description\n * a moized method where the total size of the cache is limited to the value passed\n *\n * @param maxSize the maximum size of the cache\n * @returns the moizer function\n */\nmoize.maxSize = function maxSize(maxSize: number) {\n    return moize({ maxSize });\n};\n\n/**\n * @function\n * @name profile\n * @memberof module:moize\n * @alias moize.profile\n *\n * @description\n * a moized method with a profile name\n *\n * @returns the moizer function\n */\nmoize.profile = function (profileName: string) {\n    return moize({ profileName });\n};\n\n/**\n * @function\n * @name promise\n * @memberof module:moize\n * @alias moize.promise\n *\n * @description\n * a moized method specific to caching resolved promise / async values\n *\n * @returns the moizer function\n */\nmoize.promise = moize({\n    isPromise: true,\n    updateExpire: true,\n});\n\n/**\n * @function\n * @name react\n * @memberof module:moize\n * @alias moize.react\n *\n * @description\n * a moized method specific to caching React element values\n *\n * @returns the moizer function\n */\nmoize.react = moize({ isReact: true });\n\n/**\n * @function\n * @name serialize\n * @memberof module:moize\n * @alias moize.serialize\n *\n * @description\n * a moized method that will serialize the arguments passed to use as the cache key\n *\n * @returns the moizer function\n */\nmoize.serialize = moize({ isSerialized: true });\n\n/**\n * @function\n * @name serializeWith\n * @memberof module:moize\n * @alias moize.serializeWith\n *\n * @description\n * a moized method that will serialize the arguments passed to use as the cache key\n * based on the serializer passed\n *\n * @returns the moizer function\n */\nmoize.serializeWith = function (serializer: Serialize) {\n    return moize({ isSerialized: true, serializer });\n};\n\n/**\n * @function\n * @name shallow\n * @memberof module:moize\n * @alias moize.shallow\n *\n * @description\n * should shallow equality check be used\n *\n * @returns the moizer function\n */\nmoize.shallow = moize({ isShallowEqual: true });\n\n/**\n * @function\n * @name transformArgs\n * @memberof module:moize\n * @alias moize.transformArgs\n *\n * @description\n * transform the args to allow for specific cache key comparison\n *\n * @param transformArgs the args transformer\n * @returns the moizer function\n */\nmoize.transformArgs = <Transformer extends TransformKey>(\n    transformArgs: Transformer\n) => moize({ transformArgs });\n\n/**\n * @function\n * @name updateCacheForKey\n * @memberof module:moize\n * @alias moize.updateCacheForKey\n *\n * @description\n * update the cache for a given key when the method passed returns truthy\n *\n * @param updateCacheForKey the method to determine when to update cache\n * @returns the moizer function\n */\nmoize.updateCacheForKey = <UpdateWhen extends UpdateCacheForKey>(\n    updateCacheForKey: UpdateWhen\n) => moize({ updateCacheForKey });\n\n// Add self-referring `default` property for edge-case cross-compatibility of mixed ESM/CommonJS usage.\n// This property is frozen and non-enumerable to avoid visibility on iteration or accidental overrides.\nObject.defineProperty(moize, 'default', {\n    configurable: false,\n    enumerable: false,\n    value: moize,\n    writable: false,\n});\n\nexport default moize;\n"], "names": ["DEFAULT_OPTIONS", "isDeepEqual", "isPromise", "isReact", "isSerialized", "isShallowEqual", "matchesArg", "undefined", "matchesKey", "maxAge", "maxArgs", "maxSize", "onExpire", "profileName", "serializer", "updateCacheForKey", "transformArgs", "updateExpire", "combine", "functions", "reduce", "f", "g", "apply", "arguments", "compose", "findExpirationIndex", "expirations", "key", "index", "length", "createFindKeyIndex", "isEqual", "isMatchingKey", "areKeysEqual", "cache<PERSON>ey", "keys", "keysIndex", "mergeOptions", "originalOptions", "newOptions", "onCacheAdd", "onCacheChange", "onCacheHit", "isMoized", "fn", "setName", "originalFunctionName", "name", "Object", "defineProperty", "configurable", "enumerable", "value", "writable", "clearExpiration", "<PERSON><PERSON><PERSON><PERSON>", "expirationIndex", "clearTimeout", "timeoutId", "splice", "createTimeout", "expirationMethod", "setTimeout", "unref", "createOnCacheAddSetExpiration", "options", "cache", "moizedOptions", "moized", "findKeyIndex", "keyIndex", "values", "unshift", "push", "createOnCacheHitResetExpiration", "getMaxAgeOptions", "isFinite", "statsCache", "anonymousProfileNameCounter", "isCollectingStats", "profiles", "hasWarningDisplayed", "clearStats", "collectStats", "createOnCacheAddIncrementCalls", "calls", "hits", "createOnCacheHitIncrementCallsAndHits", "getDefaultProfileName", "displayName", "getUsagePercentage", "toFixed", "getStats", "console", "warn", "usage", "profile", "completeStats", "completeProfiles", "computedProfiles", "getStatsOptions", "ALWAYS_SKIPPED_PROPERTIES", "callee", "caller", "constructor", "prototype", "copyStaticProperties", "originalFn", "newFn", "skippedProperties", "getOwnPropertyNames", "for<PERSON>ach", "property", "indexOf", "descriptor", "getOwnPropertyDescriptor", "get", "set", "addInstanceMethods", "memoized", "clear", "_microMemoizeOptions", "transform<PERSON>ey", "has", "cacheSnapshot", "remove", "existingKey", "cutoff", "size", "updateAsyncCache", "orderByLru", "addInstanceProperties", "moizeOptions", "originalFunction", "microMemoizeOptions", "defineProperties", "currentCache", "slice", "expirationsSnapshot", "createMoizeInstance", "configuration", "REACT_ELEMENT_TYPE", "Symbol", "for", "createMoizedComponent", "moizer", "reactMoizer", "Moized", "props", "context", "updater", "MoizedComponent", "isReactComponent", "render", "$$typeof", "type", "ref", "_owner", "createGetInitialArgs", "args", "clone", "<PERSON><PERSON><PERSON><PERSON>", "array", "createDefaultReplacer", "defaultReplacer", "toString", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "join", "getStringifiedArgument", "arg", "typeOfArg", "JSON", "stringify", "defaultArgumentSerializer", "getSerializerFunction", "getIsSerializedKeyEqual", "createOnCacheOperation", "_cacheIgnored", "_microMemoizeOptionsIgnored", "getIsEqual", "deepEqual", "shallowEqual", "sameValueZeroEqual", "getIsMatchingKey", "getTransformKey", "createRefreshableMoized", "refreshableMoized", "result", "moize", "passedOptions", "moizeable", "mergedOptions", "curriedFn", "curriedOptions", "coalescedOptions", "customOptions", "maxAgeOptions", "statsOptions", "memoize", "deep", "infinite", "Infinity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "expireOptions", "promise", "react", "serialize", "serializeWith", "shallow"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAEA;EACA;EACA;EACA;EACA;EACO,IAAMA,eAAwB,GAAG;EACpCC,EAAAA,WAAW,EAAE,KADuB;EAEpCC,EAAAA,SAAS,EAAE,KAFyB;EAGpCC,EAAAA,OAAO,EAAE,KAH2B;EAIpCC,EAAAA,YAAY,EAAE,KAJsB;EAKpCC,EAAAA,cAAc,EAAE,KALoB;EAMpCC,EAAAA,UAAU,EAAEC,SANwB;EAOpCC,EAAAA,UAAU,EAAED,SAPwB;EAQpCE,EAAAA,MAAM,EAAEF,SAR4B;EASpCG,EAAAA,OAAO,EAAEH,SAT2B;EAUpCI,EAAAA,OAAO,EAAE,CAV2B;EAWpCC,EAAAA,QAAQ,EAAEL,SAX0B;EAYpCM,EAAAA,WAAW,EAAEN,SAZuB;EAapCO,EAAAA,UAAU,EAAEP,SAbwB;EAcpCQ,EAAAA,iBAAiB,EAAER,SAdiB;EAepCS,EAAAA,aAAa,EAAET,SAfqB;EAgBpCU,EAAAA,YAAY,EAAE,KAAA;EAhBsB,CAAjC;;ECMP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASC,OAAT,GAEwB;EAAA,EAAA,KAAA,IAAA,IAAA,GAAA,SAAA,CAAA,MAAA,EADxBC,SACwB,GAAA,IAAA,KAAA,CAAA,IAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,GAAA,IAAA,EAAA,IAAA,EAAA,EAAA;MADxBA,SACwB,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;EAAA,GAAA;;IAC3B,OAAOA,SAAS,CAACC,MAAV,CAAiB,UAAUC,CAAV,EAAkBC,CAAlB,EAA0B;EAC9C,IAAA,IAAI,OAAOD,CAAP,KAAa,UAAjB,EAA6B;EACzB,MAAA,OAAO,OAAOC,CAAP,KAAa,UAAb,GACD,YAAqB;EACjBD,QAAAA,CAAC,CAACE,KAAF,CAAQ,IAAR,EAAcC,SAAd,CAAA,CAAA;EACAF,QAAAA,CAAC,CAACC,KAAF,CAAQ,IAAR,EAAcC,SAAd,CAAA,CAAA;EACH,OAJA,GAKDH,CALN,CAAA;EAMH,KAAA;;EAED,IAAA,IAAI,OAAOC,CAAP,KAAa,UAAjB,EAA6B;EACzB,MAAA,OAAOA,CAAP,CAAA;EACH,KAAA;EACJ,GAbM,CAAP,CAAA;EAcH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASG,OAAT,GAAyD;EAAA,EAAA,KAAA,IAAA,KAAA,GAAA,SAAA,CAAA,MAAA,EAA7BN,SAA6B,GAAA,IAAA,KAAA,CAAA,KAAA,CAAA,EAAA,KAAA,GAAA,CAAA,EAAA,KAAA,GAAA,KAAA,EAAA,KAAA,EAAA,EAAA;MAA7BA,SAA6B,CAAA,KAAA,CAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA;EAAA,GAAA;;IAC5D,OAAOA,SAAS,CAACC,MAAV,CAAiB,UAAUC,CAAV,EAAkBC,CAAlB,EAA0B;EAC9C,IAAA,IAAI,OAAOD,CAAP,KAAa,UAAjB,EAA6B;EACzB,MAAA,OAAO,OAAOC,CAAP,KAAa,UAAb,GACD,YAAqB;UACjB,OAAOD,CAAC,CAACC,CAAC,CAACC,KAAF,CAAQ,IAAR,EAAcC,SAAd,CAAD,CAAR,CAAA;EACH,OAHA,GAIDH,CAJN,CAAA;EAKH,KAAA;;EAED,IAAA,IAAI,OAAOC,CAAP,KAAa,UAAjB,EAA6B;EACzB,MAAA,OAAOA,CAAP,CAAA;EACH,KAAA;EACJ,GAZM,CAAP,CAAA;EAaH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASI,mBAAT,CAA6BC,WAA7B,EAAwDC,GAAxD,EAAkE;EACrE,EAAA,KAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGF,WAAW,CAACG,MAAxC,EAAgDD,KAAK,EAArD,EAAyD;MACrD,IAAIF,WAAW,CAACE,KAAD,CAAX,CAAmBD,GAAnB,KAA2BA,GAA/B,EAAoC;EAChC,MAAA,OAAOC,KAAP,CAAA;EACH,KAAA;EACJ,GAAA;;EAED,EAAA,OAAO,CAAC,CAAR,CAAA;EACH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASE,kBAAT,CACHC,OADG,EAEHC,aAFG,EAGL;EACE,EAAA,IAAMC,YAA2B,GAC7B,OAAOD,aAAP,KAAyB,UAAzB,GACMA,aADN,GAEM,UAAUE,QAAV,EAAyBP,GAAzB,EAAmC;EAC/B,IAAA,KAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGD,GAAG,CAACE,MAAhC,EAAwCD,KAAK,EAA7C,EAAiD;EAC7C,MAAA,IAAI,CAACG,OAAO,CAACG,QAAQ,CAACN,KAAD,CAAT,EAAkBD,GAAG,CAACC,KAAD,CAArB,CAAZ,EAA2C;EACvC,QAAA,OAAO,KAAP,CAAA;EACH,OAAA;EACJ,KAAA;;EAED,IAAA,OAAO,IAAP,CAAA;KAVd,CAAA;EAaA,EAAA,OAAO,UAAUO,IAAV,EAAuBR,GAAvB,EAAiC;EACpC,IAAA,KAAK,IAAIS,SAAS,GAAG,CAArB,EAAwBA,SAAS,GAAGD,IAAI,CAACN,MAAzC,EAAiDO,SAAS,EAA1D,EAA8D;QAC1D,IACID,IAAI,CAACC,SAAD,CAAJ,CAAgBP,MAAhB,KAA2BF,GAAG,CAACE,MAA/B,IACAI,YAAY,CAACE,IAAI,CAACC,SAAD,CAAL,EAAkBT,GAAlB,CAFhB,EAGE;EACE,QAAA,OAAOS,SAAP,CAAA;EACH,OAAA;EACJ,KAAA;;EAED,IAAA,OAAO,CAAC,CAAR,CAAA;KAVJ,CAAA;EAYH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASC,YAAT,CACHC,eADG,EAEHC,UAFG,EAGI;IACP,OAAO,CAACA,UAAD,IAAeA,UAAU,KAAKxC,eAA9B,GACDuC,eADC,GAAA,QAAA,CAAA,EAAA,EAGMA,eAHN,EAIMC,UAJN,EAAA;MAKGC,UAAU,EAAEvB,OAAO,CACfqB,eAAe,CAACE,UADD,EAEfD,UAAU,CAACC,UAFI,CALtB;MASGC,aAAa,EAAExB,OAAO,CAClBqB,eAAe,CAACG,aADE,EAElBF,UAAU,CAACE,aAFO,CATzB;MAaGC,UAAU,EAAEzB,OAAO,CACfqB,eAAe,CAACI,UADD,EAEfH,UAAU,CAACG,UAFI,CAbtB;MAiBG3B,aAAa,EAAES,OAAO,CAClBc,eAAe,CAACvB,aADE,EAElBwB,UAAU,CAACxB,aAFO,CAAA;KAjBhC,CAAA,CAAA;EAsBH,CAAA;EAEM,SAAS4B,QAAT,CAAkBC,EAAlB,EAAkE;EACrE,EAAA,OAAO,OAAOA,EAAP,KAAc,UAAd,IAA6BA,EAAD,CAAkBD,QAArD,CAAA;EACH,CAAA;EAEM,SAASE,OAAT,CACHD,EADG,EAEHE,oBAFG,EAGHlC,WAHG,EAIL;IACE,IAAI;EACA,IAAA,IAAMmC,IAAI,GAAGnC,WAAW,IAAIkC,oBAAf,IAAuC,WAApD,CAAA;EAEAE,IAAAA,MAAM,CAACC,cAAP,CAAsBL,EAAtB,EAA0B,MAA1B,EAAkC;EAC9BM,MAAAA,YAAY,EAAE,IADgB;EAE9BC,MAAAA,UAAU,EAAE,KAFkB;QAG9BC,KAAK,EAAA,SAAA,GAAYL,IAAZ,GAHyB,GAAA;EAI9BM,MAAAA,QAAQ,EAAE,IAAA;OAJd,CAAA,CAAA;KAHJ,CASE,gBAAM;EAEP,GAAA;EACJ;;EC7KD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASC,eAAT,CACH5B,WADG,EAEHC,GAFG,EAGH4B,YAHG,EAIL;EACE,EAAA,IAAMC,eAAe,GAAG/B,mBAAmB,CAACC,WAAD,EAAcC,GAAd,CAA3C,CAAA;;EAEA,EAAA,IAAI6B,eAAe,KAAK,CAAC,CAAzB,EAA4B;EACxBC,IAAAA,YAAY,CAAC/B,WAAW,CAAC8B,eAAD,CAAX,CAA6BE,SAA9B,CAAZ,CAAA;;EAEA,IAAA,IAAIH,YAAJ,EAAkB;EACd7B,MAAAA,WAAW,CAACiC,MAAZ,CAAmBH,eAAnB,EAAoC,CAApC,CAAA,CAAA;EACH,KAAA;EACJ,GAAA;EACJ,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASI,aAAT,CAAuBC,gBAAvB,EAAqDrD,MAArD,EAAqE;EACxE,EAAA,IAAMkD,SAAS,GAAGI,UAAU,CAACD,gBAAD,EAAmBrD,MAAnB,CAA5B,CAAA;;EAEA,EAAA,IAAI,OAAOkD,SAAS,CAACK,KAAjB,KAA2B,UAA/B,EAA2C;EACvCL,IAAAA,SAAS,CAACK,KAAV,EAAA,CAAA;EACH,GAAA;;EAED,EAAA,OAAOL,SAAP,CAAA;EACH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASM,6BAAT,CACHtC,WADG,EAEHuC,OAFG,EAGHlC,OAHG,EAIHC,aAJG,EAKa;EAChB,EAAA,IAAQxB,MAAR,GAAmByD,OAAnB,CAAQzD,MAAR,CAAA;IAEA,OAAO,SAASgC,UAAT,CACH0B,KADG,EAEHC,aAFG,EAGHC,MAHG,EAIL;EACE,IAAA,IAAMzC,GAAQ,GAAGuC,KAAK,CAAC/B,IAAN,CAAW,CAAX,CAAjB,CAAA;;MAEA,IAAIV,mBAAmB,CAACC,WAAD,EAAcC,GAAd,CAAnB,KAA0C,CAAC,CAA/C,EAAkD;EAC9C,MAAA,IAAMkC,gBAAgB,GAAG,SAAnBA,gBAAmB,GAAY;EACjC,QAAA,IAAMQ,YAAY,GAAGvC,kBAAkB,CAACC,OAAD,EAAUC,aAAV,CAAvC,CAAA;UAEA,IAAMsC,QAAgB,GAAGD,YAAY,CAACH,KAAK,CAAC/B,IAAP,EAAaR,GAAb,CAArC,CAAA;EACA,QAAA,IAAMyB,KAAU,GAAGc,KAAK,CAACK,MAAN,CAAaD,QAAb,CAAnB,CAAA;;UAEA,IAAI,CAACA,QAAL,EAAe;EACXJ,UAAAA,KAAK,CAAC/B,IAAN,CAAWwB,MAAX,CAAkBW,QAAlB,EAA4B,CAA5B,CAAA,CAAA;EACAJ,UAAAA,KAAK,CAACK,MAAN,CAAaZ,MAAb,CAAoBW,QAApB,EAA8B,CAA9B,CAAA,CAAA;;EAEA,UAAA,IAAI,OAAOL,OAAO,CAACxB,aAAf,KAAiC,UAArC,EAAiD;EAC7CwB,YAAAA,OAAO,CAACxB,aAAR,CAAsByB,KAAtB,EAA6BC,aAA7B,EAA4CC,MAA5C,CAAA,CAAA;EACH,WAAA;EACJ,SAAA;;EAEDd,QAAAA,eAAe,CAAC5B,WAAD,EAAcC,GAAd,EAAmB,IAAnB,CAAf,CAAA;;EAEA,QAAA,IACI,OAAOsC,OAAO,CAACtD,QAAf,KAA4B,UAA5B,IACAsD,OAAO,CAACtD,QAAR,CAAiBgB,GAAjB,CAAA,KAA0B,KAF9B,EAGE;EACEuC,UAAAA,KAAK,CAAC/B,IAAN,CAAWqC,OAAX,CAAmB7C,GAAnB,CAAA,CAAA;EACAuC,UAAAA,KAAK,CAACK,MAAN,CAAaC,OAAb,CAAqBpB,KAArB,CAAA,CAAA;EAEAZ,UAAAA,UAAU,CAAC0B,KAAD,EAAQC,aAAR,EAAuBC,MAAvB,CAAV,CAAA;;EAEA,UAAA,IAAI,OAAOH,OAAO,CAACxB,aAAf,KAAiC,UAArC,EAAiD;EAC7CwB,YAAAA,OAAO,CAACxB,aAAR,CAAsByB,KAAtB,EAA6BC,aAA7B,EAA4CC,MAA5C,CAAA,CAAA;EACH,WAAA;EACJ,SAAA;SA7BL,CAAA;;QAgCA1C,WAAW,CAAC+C,IAAZ,CAAiB;EACbZ,QAAAA,gBAAgB,EAAhBA,gBADa;EAEblC,QAAAA,GAAG,EAAHA,GAFa;EAGb+B,QAAAA,SAAS,EAAEE,aAAa,CAACC,gBAAD,EAAmBrD,MAAnB,CAAA;SAH5B,CAAA,CAAA;EAKH,KAAA;KA7CL,CAAA;EA+CH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASkE,+BAAT,CACHhD,WADG,EAEHuC,OAFG,EAGa;EAChB,EAAA,OAAO,SAASvB,UAAT,CAAoBwB,KAApB,EAAkC;EACrC,IAAA,IAAMvC,GAAG,GAAGuC,KAAK,CAAC/B,IAAN,CAAW,CAAX,CAAZ,CAAA;EACA,IAAA,IAAMqB,eAAe,GAAG/B,mBAAmB,CAACC,WAAD,EAAcC,GAAd,CAA3C,CAAA;;MAEA,IAAI,CAAC6B,eAAL,EAAsB;EAClBF,MAAAA,eAAe,CAAC5B,WAAD,EAAcC,GAAd,EAAmB,KAAnB,CAAf,CAAA;EAEAD,MAAAA,WAAW,CAAC8B,eAAD,CAAX,CAA6BE,SAA7B,GAAyCE,aAAa,CAClDlC,WAAW,CAAC8B,eAAD,CAAX,CAA6BK,gBADqB,EAElDI,OAAO,CAACzD,MAF0C,CAAtD,CAAA;EAIH,KAAA;KAXL,CAAA;EAaH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASmE,gBAAT,CACHjD,WADG,EAEHuC,OAFG,EAGHlC,OAHG,EAIHC,aAJG,EAQL;IACE,IAAMQ,UAAU,GACZ,OAAOyB,OAAO,CAACzD,MAAf,KAA0B,QAA1B,IAAsCoE,QAAQ,CAACX,OAAO,CAACzD,MAAT,CAA9C,GACMwD,6BAA6B,CACzBtC,WADyB,EAEzBuC,OAFyB,EAGzBlC,OAHyB,EAIzBC,aAJyB,CADnC,GAOM1B,SARV,CAAA;IAUA,OAAO;EACHkC,IAAAA,UAAU,EAAVA,UADG;EAEHE,IAAAA,UAAU,EACNF,UAAU,IAAIyB,OAAO,CAACjD,YAAtB,GACM0D,+BAA+B,CAAChD,WAAD,EAAcuC,OAAd,CADrC,GAEM3D,SAAAA;KALd,CAAA;EAOH;;EC1LM,IAAMuE,UAAsB,GAAG;EAClCC,EAAAA,2BAA2B,EAAE,CADK;EAElCC,EAAAA,iBAAiB,EAAE,KAFe;EAGlCC,EAAAA,QAAQ,EAAE,EAAA;EAHwB,CAA/B,CAAA;EAMP,IAAIC,mBAAmB,GAAG,KAA1B,CAAA;EAEO,SAASC,UAAT,CAAoBtE,WAApB,EAA0C;EAC7C,EAAA,IAAIA,WAAJ,EAAiB;EACb,IAAA,OAAOiE,UAAU,CAACG,QAAX,CAAoBpE,WAApB,CAAP,CAAA;EACH,GAFD,MAEO;MACHiE,UAAU,CAACG,QAAX,GAAsB,EAAtB,CAAA;EACH,GAAA;EACJ,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASG,YAAT,CAAsBJ,iBAAtB,EAAgD;EAAA,EAAA,IAA1BA,iBAA0B,KAAA,KAAA,CAAA,EAAA;EAA1BA,IAAAA,iBAA0B,GAAN,IAAM,CAAA;EAAA,GAAA;;IACnDF,UAAU,CAACE,iBAAX,GAA+BA,iBAA/B,CAAA;EACH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;;EACO,SAASK,8BAAT,CAAwCnB,OAAxC,EAA0D;EAC7D,EAAA,IAAQrD,WAAR,GAAwBqD,OAAxB,CAAQrD,WAAR,CAAA;EAEA,EAAA,OAAO,YAAY;MACf,IAAIA,WAAW,IAAI,CAACiE,UAAU,CAACG,QAAX,CAAoBpE,WAApB,CAApB,EAAsD;EAClDiE,MAAAA,UAAU,CAACG,QAAX,CAAoBpE,WAApB,CAAmC,GAAA;EAC/ByE,QAAAA,KAAK,EAAE,CADwB;EAE/BC,QAAAA,IAAI,EAAE,CAAA;SAFV,CAAA;EAIH,KAAA;;EAEDT,IAAAA,UAAU,CAACG,QAAX,CAAoBpE,WAApB,EAAiCyE,KAAjC,EAAA,CAAA;KARJ,CAAA;EAUH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;;EACO,SAASE,qCAAT,CAA+CtB,OAA/C,EAAiE;EACpE,EAAA,OAAO,YAAY;EACf,IAAA,IAAQe,QAAR,GAAqBH,UAArB,CAAQG,QAAR,CAAA;EACA,IAAA,IAAQpE,WAAR,GAAwBqD,OAAxB,CAAQrD,WAAR,CAAA;;EAEA,IAAA,IAAI,CAACoE,QAAQ,CAACpE,WAAD,CAAb,EAA4B;QACxBoE,QAAQ,CAACpE,WAAD,CAAR,GAAwB;EACpByE,QAAAA,KAAK,EAAE,CADa;EAEpBC,QAAAA,IAAI,EAAE,CAAA;SAFV,CAAA;EAIH,KAAA;;EAEDN,IAAAA,QAAQ,CAACpE,WAAD,CAAR,CAAsByE,KAAtB,EAAA,CAAA;EACAL,IAAAA,QAAQ,CAACpE,WAAD,CAAR,CAAsB0E,IAAtB,EAAA,CAAA;KAZJ,CAAA;EAcH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASE,qBAAT,CACH5C,EADG,EAEL;IACE,OACKA,EAAD,CAAqD6C,WAArD,IACA7C,EAAE,CAACG,IADH,IAEa8B,YAAAA,GAAAA,UAAU,CAACC,2BAAX,EAHjB,CAAA;EAKH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASY,kBAAT,CAA4BL,KAA5B,EAA2CC,IAA3C,EAAyD;EAC5D,EAAA,OAAOD,KAAK,GAAM,CAAEC,IAAI,GAAGD,KAAR,GAAiB,GAAlB,EAAuBM,OAAvB,CAA+B,CAA/B,CAAN,SAA6C,SAAzD,CAAA;EACH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASC,QAAT,CAAkBhF,WAAlB,EAA2D;EAC9D,EAAA,IAAI,CAACiE,UAAU,CAACE,iBAAZ,IAAiC,CAACE,mBAAtC,EAA2D;EACvDY,IAAAA,OAAO,CAACC,IAAR,CACI,oFADJ,EADuD;;EAKvDb,IAAAA,mBAAmB,GAAG,IAAtB,CAAA;EACH,GAAA;;EAED,EAAA,IAAQD,QAAR,GAAqBH,UAArB,CAAQG,QAAR,CAAA;;EAEA,EAAA,IAAIpE,WAAJ,EAAiB;EACb,IAAA,IAAI,CAACoE,QAAQ,CAACpE,WAAD,CAAb,EAA4B;QACxB,OAAO;EACHyE,QAAAA,KAAK,EAAE,CADJ;EAEHC,QAAAA,IAAI,EAAE,CAFH;EAGHS,QAAAA,KAAK,EAAE,SAAA;SAHX,CAAA;EAKH,KAAA;;EAED,IAAA,IAAuBC,OAAvB,GAAmChB,QAAnC,CAASpE,WAAT,CAAA,CAAA;EAEA,IAAA,OAAA,QAAA,CAAA,EAAA,EACOoF,OADP,EAAA;QAEID,KAAK,EAAEL,kBAAkB,CAACM,OAAO,CAACX,KAAT,EAAgBW,OAAO,CAACV,IAAxB,CAAA;EAF7B,KAAA,CAAA,CAAA;EAIH,GAAA;;EAED,EAAA,IAAMW,aAA2B,GAAGjD,MAAM,CAACb,IAAP,CAAY0C,UAAU,CAACG,QAAvB,CAAA,CAAiC7D,MAAjC,CAChC,UAAC+E,gBAAD,EAAmBtF,WAAnB,EAAmC;MAC/BsF,gBAAgB,CAACb,KAAjB,IAA0BL,QAAQ,CAACpE,WAAD,CAAR,CAAsByE,KAAhD,CAAA;MACAa,gBAAgB,CAACZ,IAAjB,IAAyBN,QAAQ,CAACpE,WAAD,CAAR,CAAsB0E,IAA/C,CAAA;EAEA,IAAA,OAAOY,gBAAP,CAAA;EACH,GAN+B,EAOhC;EACIb,IAAAA,KAAK,EAAE,CADX;EAEIC,IAAAA,IAAI,EAAE,CAAA;EAFV,GAPgC,CAApC,CAAA;EAaA,EAAA,OAAA,QAAA,CAAA,EAAA,EACOW,aADP,EAAA;EAEIjB,IAAAA,QAAQ,EAAEhC,MAAM,CAACb,IAAP,CAAY6C,QAAZ,CAAsB7D,CAAAA,MAAtB,CACN,UAACgF,gBAAD,EAAmBvF,WAAnB,EAAmC;EAC/BuF,MAAAA,gBAAgB,CAACvF,WAAD,CAAhB,GAAgCgF,QAAQ,CAAChF,WAAD,CAAxC,CAAA;EAEA,MAAA,OAAOuF,gBAAP,CAAA;OAJE,EAMN,EANM,CAFd;MAUIJ,KAAK,EAAEL,kBAAkB,CAACO,aAAa,CAACZ,KAAf,EAAsBY,aAAa,CAACX,IAApC,CAAA;EAV7B,GAAA,CAAA,CAAA;EAYH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASc,eAAT,CAAyBnC,OAAzB,EAGL;IACE,OAAOY,UAAU,CAACE,iBAAX,GACD;EACIvC,IAAAA,UAAU,EAAE4C,8BAA8B,CAACnB,OAAD,CAD9C;MAEIvB,UAAU,EAAE6C,qCAAqC,CAACtB,OAAD,CAAA;EAFrD,GADC,GAKD,EALN,CAAA;EAMH;;ECzLD,IAAMoC,yBAAkD,GAAG;EACvD9E,EAAAA,SAAS,EAAE,IAD4C;EAEvD+E,EAAAA,MAAM,EAAE,IAF+C;EAGvDC,EAAAA,MAAM,EAAE,IAH+C;EAIvDC,EAAAA,WAAW,EAAE,IAJ0C;EAKvD3E,EAAAA,MAAM,EAAE,IAL+C;EAMvDkB,EAAAA,IAAI,EAAE,IANiD;EAOvD0D,EAAAA,SAAS,EAAE,IAAA;EAP4C,CAA3D,CAAA;EAUA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASC,oBAAT,CACHC,UADG,EAEHC,KAFG,EAGHC,iBAHG,EAIL;EAAA,EAAA,IADEA,iBACF,KAAA,KAAA,CAAA,EAAA;EADEA,IAAAA,iBACF,GADgC,EAChC,CAAA;EAAA,GAAA;;IACE7D,MAAM,CAAC8D,mBAAP,CAA2BH,UAA3B,EAAuCI,OAAvC,CAA+C,UAACC,QAAD,EAAc;EACzD,IAAA,IACI,CAACX,yBAAyB,CAACW,QAAD,CAA1B,IACAH,iBAAiB,CAACI,OAAlB,CAA0BD,QAA1B,CAAwC,KAAA,CAAC,CAF7C,EAGE;QACE,IAAME,UAAU,GAAGlE,MAAM,CAACmE,wBAAP,CACfR,UADe,EAEfK,QAFe,CAAnB,CAAA;;EAKA,MAAA,IAAIE,UAAU,CAACE,GAAX,IAAkBF,UAAU,CAACG,GAAjC,EAAsC;EAClCrE,QAAAA,MAAM,CAACC,cAAP,CAAsB2D,KAAtB,EAA6BI,QAA7B,EAAuCE,UAAvC,CAAA,CAAA;EACH,OAFD,MAEO;EACHN,QAAAA,KAAK,CAACI,QAAD,CAAL,GACIL,UAAU,CAACK,QAAD,CADd,CAAA;EAEH,OAAA;EACJ,KAAA;KAhBL,CAAA,CAAA;EAkBH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASM,kBAAT,CACHC,QADG,EAGL,IAAA,EAAA;IAAA,IADI7F,WACJ,QADIA,WACJ,CAAA;EACE,EAAA,IAAQuC,OAAR,GAAoBsD,QAApB,CAAQtD,OAAR,CAAA;IAEA,IAAMI,YAAY,GAAGvC,kBAAkB,CACnCmC,OAAO,CAAClC,OAD2B,EAEnCkC,OAAO,CAACjC,aAF2B,CAAvC,CAAA;IAKA,IAAMoC,MAAM,GAAGmD,QAAf,CAAA;;IAEAnD,MAAM,CAACoD,KAAP,GAAe,YAAY;EACvB,IAAA,IAC4B/E,aAD5B,GAGI2B,MAHJ,CACIqD,oBADJ,CAC4BhF,aAD5B;EAAA,QAEIyB,KAFJ,GAGIE,MAHJ,CAEIF,KAFJ,CAAA;EAKAA,IAAAA,KAAK,CAAC/B,IAAN,CAAWN,MAAX,GAAoB,CAApB,CAAA;EACAqC,IAAAA,KAAK,CAACK,MAAN,CAAa1C,MAAb,GAAsB,CAAtB,CAAA;;EAEA,IAAA,IAAIY,aAAJ,EAAmB;QACfA,aAAa,CAACyB,KAAD,EAAQE,MAAM,CAACH,OAAf,EAAwBG,MAAxB,CAAb,CAAA;EACH,KAAA;;EAED,IAAA,OAAO,IAAP,CAAA;KAbJ,CAAA;;IAgBAA,MAAM,CAACc,UAAP,GAAoB,YAAY;EAC5BA,IAAAA,UAAU,CAACd,MAAM,CAACH,OAAP,CAAerD,WAAhB,CAAV,CAAA;KADJ,CAAA;;EAIAwD,EAAAA,MAAM,CAACgD,GAAP,GAAa,UAAUzF,GAAV,EAAoB;EAC7B,IAAA,IAC4B+F,YAD5B,GAGItD,MAHJ,CACIqD,oBADJ,CAC4BC,YAD5B;EAAA,QAEIxD,KAFJ,GAGIE,MAHJ,CAEIF,KAFJ,CAAA;MAKA,IAAMhC,QAAQ,GAAGwF,YAAY,GAAGA,YAAY,CAAC/F,GAAD,CAAf,GAAuBA,GAApD,CAAA;MACA,IAAM2C,QAAQ,GAAGD,YAAY,CAACH,KAAK,CAAC/B,IAAP,EAAaD,QAAb,CAA7B,CAAA;EAEA,IAAA,OAAOoC,QAAQ,KAAK,CAAC,CAAd,GAAkBF,MAAM,CAAC9C,KAAP,CAAa,IAAb,EAAmBK,GAAnB,CAAlB,GAA4CrB,SAAnD,CAAA;KATJ,CAAA;;IAYA8D,MAAM,CAACwB,QAAP,GAAkB,YAA0B;EACxC,IAAA,OAAOA,QAAQ,CAACxB,MAAM,CAACH,OAAP,CAAerD,WAAhB,CAAf,CAAA;KADJ,CAAA;;EAIAwD,EAAAA,MAAM,CAACuD,GAAP,GAAa,UAAUhG,GAAV,EAAoB;EAC7B,IAAA,IAAQ+F,YAAR,GAAyBtD,MAAM,CAACqD,oBAAhC,CAAQC,YAAR,CAAA;MAEA,IAAMxF,QAAQ,GAAGwF,YAAY,GAAGA,YAAY,CAAC/F,GAAD,CAAf,GAAuBA,GAApD,CAAA;EAEA,IAAA,OAAO0C,YAAY,CAACD,MAAM,CAACF,KAAP,CAAa/B,IAAd,EAAoBD,QAApB,CAAZ,KAA8C,CAAC,CAAtD,CAAA;KALJ,CAAA;;IAQAkC,MAAM,CAACjC,IAAP,GAAc,YAAY;EACtB,IAAA,OAAOiC,MAAM,CAACwD,aAAP,CAAqBzF,IAA5B,CAAA;KADJ,CAAA;;EAIAiC,EAAAA,MAAM,CAACyD,MAAP,GAAgB,UAAUlG,GAAV,EAAoB;MAChC,IAGIyC,qBAAAA,GAAAA,MAHJ,CACIqD,oBADJ;UAC4BhF,aAD5B,yBAC4BA,aAD5B;UAC2CiF,YAD3C,yBAC2CA,YAD3C;EAAA,QAEIxD,KAFJ,GAGIE,MAHJ,CAEIF,KAFJ,CAAA;EAKA,IAAA,IAAMI,QAAQ,GAAGD,YAAY,CACzBH,KAAK,CAAC/B,IADmB,EAEzBuF,YAAY,GAAGA,YAAY,CAAC/F,GAAD,CAAf,GAAuBA,GAFV,CAA7B,CAAA;;EAKA,IAAA,IAAI2C,QAAQ,KAAK,CAAC,CAAlB,EAAqB;EACjB,MAAA,OAAO,KAAP,CAAA;EACH,KAAA;;EAED,IAAA,IAAMwD,WAAW,GAAG5D,KAAK,CAAC/B,IAAN,CAAWmC,QAAX,CAApB,CAAA;EAEAJ,IAAAA,KAAK,CAAC/B,IAAN,CAAWwB,MAAX,CAAkBW,QAAlB,EAA4B,CAA5B,CAAA,CAAA;EACAJ,IAAAA,KAAK,CAACK,MAAN,CAAaZ,MAAb,CAAoBW,QAApB,EAA8B,CAA9B,CAAA,CAAA;;EAEA,IAAA,IAAI7B,aAAJ,EAAmB;QACfA,aAAa,CAACyB,KAAD,EAAQE,MAAM,CAACH,OAAf,EAAwBG,MAAxB,CAAb,CAAA;EACH,KAAA;;EAEDd,IAAAA,eAAe,CAAC5B,WAAD,EAAcoG,WAAd,EAA2B,IAA3B,CAAf,CAAA;EAEA,IAAA,OAAO,IAAP,CAAA;KA1BJ,CAAA;;EA6BA1D,EAAAA,MAAM,CAACiD,GAAP,GAAa,UAAU1F,GAAV,EAAoByB,KAApB,EAAgC;EACzC,IAAA,IAAQqE,oBAAR,GAAiDrD,MAAjD,CAAQqD,oBAAR;EAAA,QAA8BvD,KAA9B,GAAiDE,MAAjD,CAA8BF,KAA9B;EAAA,QAAqCD,OAArC,GAAiDG,MAAjD,CAAqCH,OAArC,CAAA;EACA,IAAA,IAAQzB,UAAR,GACIiF,oBADJ,CAAQjF,UAAR;EAAA,QAAoBC,aAApB,GACIgF,oBADJ,CAAoBhF,aAApB;EAAA,QAAmCiF,YAAnC,GACID,oBADJ,CAAmCC,YAAnC,CAAA;MAGA,IAAMxF,QAAQ,GAAGwF,YAAY,GAAGA,YAAY,CAAC/F,GAAD,CAAf,GAAuBA,GAApD,CAAA;MACA,IAAM2C,QAAQ,GAAGD,YAAY,CAACH,KAAK,CAAC/B,IAAP,EAAaD,QAAb,CAA7B,CAAA;;EAEA,IAAA,IAAIoC,QAAQ,KAAK,CAAC,CAAlB,EAAqB;EACjB,MAAA,IAAMyD,MAAM,GAAG9D,OAAO,CAACvD,OAAR,GAAkB,CAAjC,CAAA;;EAEA,MAAA,IAAIwD,KAAK,CAAC8D,IAAN,GAAaD,MAAjB,EAAyB;EACrB7D,QAAAA,KAAK,CAAC/B,IAAN,CAAWN,MAAX,GAAoBkG,MAApB,CAAA;EACA7D,QAAAA,KAAK,CAACK,MAAN,CAAa1C,MAAb,GAAsBkG,MAAtB,CAAA;EACH,OAAA;;EAED7D,MAAAA,KAAK,CAAC/B,IAAN,CAAWqC,OAAX,CAAmBtC,QAAnB,CAAA,CAAA;EACAgC,MAAAA,KAAK,CAACK,MAAN,CAAaC,OAAb,CAAqBpB,KAArB,CAAA,CAAA;;QAEA,IAAIa,OAAO,CAAChE,SAAZ,EAAuB;UACnBiE,KAAK,CAAC+D,gBAAN,CAAuB7D,MAAvB,CAAA,CAAA;EACH,OAAA;;EAED,MAAA,IAAI5B,UAAJ,EAAgB;EACZA,QAAAA,UAAU,CAAC0B,KAAD,EAAQD,OAAR,EAAiBG,MAAjB,CAAV,CAAA;EACH,OAAA;;EAED,MAAA,IAAI3B,aAAJ,EAAmB;EACfA,QAAAA,aAAa,CAACyB,KAAD,EAAQD,OAAR,EAAiBG,MAAjB,CAAb,CAAA;EACH,OAAA;EACJ,KAtBD,MAsBO;EACH,MAAA,IAAM0D,WAAW,GAAG5D,KAAK,CAAC/B,IAAN,CAAWmC,QAAX,CAApB,CAAA;EAEAJ,MAAAA,KAAK,CAACK,MAAN,CAAaD,QAAb,IAAyBlB,KAAzB,CAAA;;QAEA,IAAIkB,QAAQ,GAAG,CAAf,EAAkB;EACdJ,QAAAA,KAAK,CAACgE,UAAN,CAAiBJ,WAAjB,EAA8B1E,KAA9B,EAAqCkB,QAArC,CAAA,CAAA;EACH,OAAA;;QAED,IAAIL,OAAO,CAAChE,SAAZ,EAAuB;UACnBiE,KAAK,CAAC+D,gBAAN,CAAuB7D,MAAvB,CAAA,CAAA;EACH,OAAA;;EAED,MAAA,IAAI,OAAO3B,aAAP,KAAyB,UAA7B,EAAyC;EACrCA,QAAAA,aAAa,CAACyB,KAAD,EAAQD,OAAR,EAAiBG,MAAjB,CAAb,CAAA;EACH,OAAA;EACJ,KAAA;KA9CL,CAAA;;IAiDAA,MAAM,CAACG,MAAP,GAAgB,YAAY;EACxB,IAAA,OAAOH,MAAM,CAACwD,aAAP,CAAqBrD,MAA5B,CAAA;KADJ,CAAA;EAGH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAAS4D,qBAAT,CACHZ,QADG,EAOL,KAAA,EAAA;IAAA,IAJM7F,WAIN,SAJMA,WAIN;QAHe0G,YAGf,SAHMnE,OAGN;QAFMoE,gBAEN,SAFMA,gBAEN,CAAA;EACE,EAAA,IAAiBC,mBAAjB,GAAyCf,QAAzC,CAAQtD,OAAR,CAAA;EAEAjB,EAAAA,MAAM,CAACuF,gBAAP,CAAwBhB,QAAxB,EAAkC;EAC9BE,IAAAA,oBAAoB,EAAE;EAClBvE,MAAAA,YAAY,EAAE,IADI;EAElBkE,MAAAA,GAFkB,EAEZ,SAAA,GAAA,GAAA;EACF,QAAA,OAAOkB,mBAAP,CAAA;EACH,OAAA;OALyB;EAQ9BV,IAAAA,aAAa,EAAE;EACX1E,MAAAA,YAAY,EAAE,IADH;EAEXkE,MAAAA,GAFW,EAEL,SAAA,GAAA,GAAA;EACF,QAAA,IAAeoB,YAAf,GAAgCjB,QAAhC,CAAQrD,KAAR,CAAA;UAEA,OAAO;YACH/B,IAAI,EAAEqG,YAAY,CAACrG,IAAb,CAAkBsG,KAAlB,CAAwB,CAAxB,CADH;YAEHT,IAAI,EAAEQ,YAAY,CAACR,IAFhB;EAGHzD,UAAAA,MAAM,EAAEiE,YAAY,CAACjE,MAAb,CAAoBkE,KAApB,CAA0B,CAA1B,CAAA;WAHZ,CAAA;EAKH,OAAA;OAlByB;EAqB9B/G,IAAAA,WAAW,EAAE;EACTwB,MAAAA,YAAY,EAAE,IADL;EAETkE,MAAAA,GAFS,EAEH,SAAA,GAAA,GAAA;EACF,QAAA,OAAO1F,WAAP,CAAA;EACH,OAAA;OAzByB;EA4B9BgH,IAAAA,mBAAmB,EAAE;EACjBxF,MAAAA,YAAY,EAAE,IADG;EAEjBkE,MAAAA,GAFiB,EAEX,SAAA,GAAA,GAAA;EACF,QAAA,OAAO1F,WAAW,CAAC+G,KAAZ,CAAkB,CAAlB,CAAP,CAAA;EACH,OAAA;OAhCyB;EAmC9B9F,IAAAA,QAAQ,EAAE;EACNO,MAAAA,YAAY,EAAE,IADR;EAENkE,MAAAA,GAFM,EAEA,SAAA,GAAA,GAAA;EACF,QAAA,OAAO,IAAP,CAAA;EACH,OAAA;OAvCyB;EA0C9BnD,IAAAA,OAAO,EAAE;EACLf,MAAAA,YAAY,EAAE,IADT;EAELkE,MAAAA,GAFK,EAEC,SAAA,GAAA,GAAA;EACF,QAAA,OAAOgB,YAAP,CAAA;EACH,OAAA;OA9CyB;EAiD9BC,IAAAA,gBAAgB,EAAE;EACdnF,MAAAA,YAAY,EAAE,IADA;EAEdkE,MAAAA,GAFc,EAER,SAAA,GAAA,GAAA;EACF,QAAA,OAAOiB,gBAAP,CAAA;EACH,OAAA;EAJa,KAAA;KAjDtB,CAAA,CAAA;IAyDA,IAAMjE,MAAM,GAAGmD,QAAf,CAAA;EAEAb,EAAAA,oBAAoB,CAAC2B,gBAAD,EAAmBjE,MAAnB,CAApB,CAAA;EACH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASuE,mBAAT,CAIHpB,QAJG,EAKHqB,aALG,EAML;EACEtB,EAAAA,kBAAkB,CAAaC,QAAb,EAAuBqB,aAAvB,CAAlB,CAAA;EACAT,EAAAA,qBAAqB,CAAaZ,QAAb,EAAuBqB,aAAvB,CAArB,CAAA;EAEA,EAAA,OAAOrB,QAAP,CAAA;EACH;;ECnTD;EACA;EACA,IAAMsB,kBAAkB,GACpB,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAAvC,GACMD,MAAM,CAACC,GAAP,CAAW,eAAX,CADN,GAEM,MAHV,CAAA;EAKA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASC,qBAAT,CACHC,MADG,EAEHrG,EAFG,EAGHqB,OAHG,EAIL;EACE;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACI,IAAMiF,WAAW,GAAGD,MAAM,CAAA,QAAA,CAAA;EACtBxI,IAAAA,OAAO,EAAE,CADa;EAEtBL,IAAAA,cAAc,EAAE,IAAA;EAFM,GAAA,EAGnB6D,OAHmB,EAAA;EAItB/D,IAAAA,OAAO,EAAE,KAAA;KAJb,CAAA,CAAA,CAAA;;EAOA,EAAA,IAAI,CAAC0C,EAAE,CAAC6C,WAAR,EAAqB;EACjB;EACA7C,IAAAA,EAAE,CAAC6C,WAAH,GAAiB7C,EAAE,CAACG,IAAH,IAAW,WAA5B,CAAA;EACH,GAAA;;EAED,EAAA,SAASoG,MAAT,CAEIC,KAFJ,EAGIC,OAHJ,EAIIC,OAJJ,EAKE;MACE,IAAKF,CAAAA,KAAL,GAAaA,KAAb,CAAA;MACA,IAAKC,CAAAA,OAAL,GAAeA,OAAf,CAAA;MACA,IAAKC,CAAAA,OAAL,GAAeA,OAAf,CAAA;EAEA,IAAA,IAAA,CAAKC,eAAL,GAAuBL,WAAW,CAACtG,EAAD,CAAlC,CAAA;EACH,GAAA;;EAEDuG,EAAAA,MAAM,CAAC1C,SAAP,CAAiB+C,gBAAjB,GAAoC,EAApC,CAAA;;EAEAL,EAAAA,MAAM,CAAC1C,SAAP,CAAiBgD,MAAjB,GAA0B,YAAY;MAClC,OAAO;EACHC,MAAAA,QAAQ,EAAEb,kBADP;QAEHc,IAAI,EAAE,KAAKJ,eAFR;QAGHH,KAAK,EAAE,KAAKA,KAHT;EAIHQ,MAAAA,GAAG,EAAE,IAJF;EAKHjI,MAAAA,GAAG,EAAE,IALF;EAMHkI,MAAAA,MAAM,EAAE,IAAA;OANZ,CAAA;KADJ,CAAA;;IAWAnD,oBAAoB,CAAC9D,EAAD,EAAKuG,MAAL,EAAa,CAAC,aAAD,EAAgB,cAAhB,CAAb,CAApB,CAAA;IAEAA,MAAM,CAAC1D,WAAP,GAAA,SAAA,IAA+B7C,EAAE,CAAC6C,WAAH,IAAkB7C,EAAE,CAACG,IAArB,IAA6B,WAA5D,CAAA,GAAA,GAAA,CAAA;IAEAF,OAAO,CAACsG,MAAD,EAA2BvG,EAAE,CAACG,IAA9B,EAAoCkB,OAAO,CAACrD,WAA5C,CAAP,CAAA;EAEA,EAAA,OAAOuI,MAAP,CAAA;EACH;;EC9FM,SAASW,oBAAT,CAA8B9B,IAA9B,EAA4C;EAC/C;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACI,OAAO,UAAU+B,IAAV,EAA0B;EAC7B,IAAA,IAAI/B,IAAI,IAAI+B,IAAI,CAAClI,MAAjB,EAAyB;EACrB,MAAA,OAAOkI,IAAP,CAAA;EACH,KAAA;;MAED,IAAI/B,IAAI,KAAK,CAAb,EAAgB;EACZ,MAAA,OAAO,EAAP,CAAA;EACH,KAAA;;MAED,IAAIA,IAAI,KAAK,CAAb,EAAgB;EACZ,MAAA,OAAO,CAAC+B,IAAI,CAAC,CAAD,CAAL,CAAP,CAAA;EACH,KAAA;;MAED,IAAI/B,IAAI,KAAK,CAAb,EAAgB;QACZ,OAAO,CAAC+B,IAAI,CAAC,CAAD,CAAL,EAAUA,IAAI,CAAC,CAAD,CAAd,CAAP,CAAA;EACH,KAAA;;MAED,IAAI/B,IAAI,KAAK,CAAb,EAAgB;EACZ,MAAA,OAAO,CAAC+B,IAAI,CAAC,CAAD,CAAL,EAAUA,IAAI,CAAC,CAAD,CAAd,EAAmBA,IAAI,CAAC,CAAD,CAAvB,CAAP,CAAA;EACH,KAAA;;MAED,IAAMC,KAAK,GAAG,EAAd,CAAA;;MAEA,KAAK,IAAIpI,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGoG,IAA5B,EAAkCpG,KAAK,EAAvC,EAA2C;EACvCoI,MAAAA,KAAK,CAACpI,KAAD,CAAL,GAAemI,IAAI,CAACnI,KAAD,CAAnB,CAAA;EACH,KAAA;;EAED,IAAA,OAAOoI,KAAP,CAAA;KA3BJ,CAAA;EA6BH;;ECvCD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASC,SAAT,CAAmBC,KAAnB,EAAiC9G,KAAjC,EAA6C;EACzC,EAAA,IAAQvB,MAAR,GAAmBqI,KAAnB,CAAQrI,MAAR,CAAA;;IAEA,KAAK,IAAID,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGC,MAA5B,EAAoC,EAAED,KAAtC,EAA6C;EACzC,IAAA,IAAIsI,KAAK,CAACtI,KAAD,CAAL,KAAiBwB,KAArB,EAA4B;QACxB,OAAOxB,KAAK,GAAG,CAAf,CAAA;EACH,KAAA;EACJ,GAAA;;EAED,EAAA,OAAO,CAAP,CAAA;EACH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACO,SAASuI,qBAAT,GAAiC;IACpC,IAAMjG,KAAY,GAAG,EAArB,CAAA;IACA,IAAM/B,IAAc,GAAG,EAAvB,CAAA;EAEA,EAAA,OAAO,SAASiI,eAAT,CAAyBzI,GAAzB,EAAsCyB,KAAtC,EAAkD;MACrD,IAAMuG,IAAI,GAAG,OAAOvG,KAApB,CAAA;;EAEA,IAAA,IAAIuG,IAAI,KAAK,UAAT,IAAuBA,IAAI,KAAK,QAApC,EAA8C;QAC1C,OAAOvG,KAAK,CAACiH,QAAN,EAAP,CAAA;EACH,KAAA;;EAED,IAAA,IAAI,OAAOjH,KAAP,KAAiB,QAArB,EAA+B;QAC3B,IAAIc,KAAK,CAACrC,MAAV,EAAkB;EACd,QAAA,IAAMyI,UAAU,GAAGL,SAAS,CAAC/F,KAAD,EAAQ,IAAR,CAA5B,CAAA;;UAEA,IAAIoG,UAAU,KAAK,CAAnB,EAAsB;EAClBpG,UAAAA,KAAK,CAACA,KAAK,CAACrC,MAAP,CAAL,GAAsB,IAAtB,CAAA;EACH,SAFD,MAEO;YACHqC,KAAK,CAACP,MAAN,CAAa2G,UAAb,CAAA,CAAA;YACAnI,IAAI,CAACwB,MAAL,CAAY2G,UAAZ,CAAA,CAAA;EACH,SAAA;;EAEDnI,QAAAA,IAAI,CAACA,IAAI,CAACN,MAAN,CAAJ,GAAoBF,GAApB,CAAA;EAEA,QAAA,IAAM4I,WAAW,GAAGN,SAAS,CAAC/F,KAAD,EAAQd,KAAR,CAA7B,CAAA;;UAEA,IAAImH,WAAW,KAAK,CAApB,EAAuB;EACnB,UAAA,OAAA,OAAA,IACIpI,IAAI,CAACsG,KAAL,CAAW,CAAX,EAAc8B,WAAd,CAAA,CAA2BC,IAA3B,CAAgC,GAAhC,CAAA,IAAwC,GAD5C,CAAA,GAAA,GAAA,CAAA;EAGH,SAAA;EACJ,OAnBD,MAmBO;EACHtG,QAAAA,KAAK,CAAC,CAAD,CAAL,GAAWd,KAAX,CAAA;EACAjB,QAAAA,IAAI,CAAC,CAAD,CAAJ,GAAUR,GAAV,CAAA;EACH,OAAA;;EAED,MAAA,OAAOyB,KAAP,CAAA;EACH,KAAA;;EAED,IAAA,OAAO,KAAKA,KAAZ,CAAA;KAnCJ,CAAA;EAqCH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASqH,sBAAT,CAAsCC,GAAtC,EAAiD;IACpD,IAAMC,SAAS,GAAG,OAAOD,GAAzB,CAAA;IAEA,OAAOA,GAAG,KAAKC,SAAS,KAAK,QAAd,IAA0BA,SAAS,KAAK,UAA7C,CAAH,GACDC,IAAI,CAACC,SAAL,CAAeH,GAAf,EAAoBP,qBAAqB,EAAzC,CADC,GAEDO,GAFN,CAAA;EAGH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASI,yBAAT,CAAmCf,IAAnC,EAA8C;IACjD,IAAIpI,GAAG,GAAG,GAAV,CAAA;;EAEA,EAAA,KAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGmI,IAAI,CAAClI,MAAjC,EAAyCD,KAAK,EAA9C,EAAkD;MAC9CD,GAAG,IAAI8I,sBAAsB,CAACV,IAAI,CAACnI,KAAD,CAAL,CAAtB,GAAsC,GAA7C,CAAA;EACH,GAAA;;IAED,OAAO,CAACD,GAAD,CAAP,CAAA;EACH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASoJ,qBAAT,CAA+B9G,OAA/B,EAAiD;IACpD,OAAO,OAAOA,OAAO,CAACpD,UAAf,KAA8B,UAA9B,GACDoD,OAAO,CAACpD,UADP,GAEDiK,yBAFN,CAAA;EAGH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASE,uBAAT,CAAiC9I,QAAjC,EAAgDP,GAAhD,EAA0D;IAC7D,OAAOO,QAAQ,CAAC,CAAD,CAAR,KAAgBP,GAAG,CAAC,CAAD,CAA1B,CAAA;EACH;;EC3HM,SAASsJ,sBAAT,CACHrI,EADG,EAEa;EAChB,EAAA,IAAI,OAAOA,EAAP,KAAc,UAAlB,EAA8B;EAC1B,IAAA,OAAO,UACHsI,aADG,EAEHC,2BAFG,EAGH5D,QAHG,EAAA;QAAA,OAII3E,EAAE,CAAC2E,QAAQ,CAACrD,KAAV,EAAiBqD,QAAQ,CAACtD,OAA1B,EAAmCsD,QAAnC,CAJN,CAAA;OAAP,CAAA;EAKH,GAAA;EACJ,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAAS6D,UAAT,CAAoBnH,OAApB,EAA+C;EAClD,EAAA,OACIA,OAAO,CAAC5D,UAAR,IACC4D,OAAO,CAACjE,WAAR,IAAuBqL,oBADxB,IAECpH,OAAO,CAAC7D,cAAR,IAA0BkL,uBAF3B,IAGAC,6BAJJ,CAAA;EAMH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASC,gBAAT,CAA0BvH,OAA1B,EAAuE;IAC1E,OACIA,OAAO,CAAC1D,UAAR,IACC0D,OAAO,CAAC9D,YAAR,IAAwB6K,uBADzB,IAEA1K,SAHJ,CAAA;EAKH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASmL,eAAT,CAAyBxH,OAAzB,EAAqE;EACxE,EAAA,OAAOzC,OAAO,CACVyC,OAAO,CAAC9D,YAAR,IAAwB4K,qBAAqB,CAAC9G,OAAD,CADnC,EAEV,OAAOA,OAAO,CAAClD,aAAf,KAAiC,UAAjC,IAA+CkD,OAAO,CAAClD,aAF7C,EAGV,OAAOkD,OAAO,CAACxD,OAAf,KAA2B,QAA3B,IACIqJ,oBAAoB,CAAC7F,OAAO,CAACxD,OAAT,CAJd,CAAd,CAAA;EAMH;;EC3EM,SAASiL,uBAAT,CACHtH,MADG,EAEL;EACE,EAAA,IACetD,iBADf,GAEIsD,MAFJ,CACIH,OADJ,CACenD,iBADf,CAAA;EAIA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACI,EAAA,IAAM6K,iBAAiB,GAAG,SAASA,iBAAT,GAGxB;EAAA,IAAA,KAAA,IAAA,IAAA,GAAA,SAAA,CAAA,MAAA,EADK5B,IACL,GAAA,IAAA,KAAA,CAAA,IAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,GAAA,IAAA,EAAA,IAAA,EAAA,EAAA;QADKA,IACL,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;EAAA,KAAA;;EACE,IAAA,IAAI,CAACjJ,iBAAiB,CAACiJ,IAAD,CAAtB,EAA8B;EAC1B,MAAA,OAAO3F,MAAM,CAAC9C,KAAP,CAAa,IAAb,EAAmByI,IAAnB,CAAP,CAAA;EACH,KAAA;;MAED,IAAM6B,MAAM,GAAGxH,MAAM,CAACxB,EAAP,CAAUtB,KAAV,CAAgB,IAAhB,EAAsByI,IAAtB,CAAf,CAAA;EAEA3F,IAAAA,MAAM,CAACiD,GAAP,CAAW0C,IAAX,EAAiB6B,MAAjB,CAAA,CAAA;EAEA,IAAA,OAAOA,MAAP,CAAA;KAZJ,CAAA;;EAeAlF,EAAAA,oBAAoB,CAACtC,MAAD,EAASuH,iBAAT,CAApB,CAAA;EAEA,EAAA,OAAOA,iBAAP,CAAA;EACH;;;;ECJD;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACME,MAAAA,KAAY,GAAG,SAAfA,KAAe,CAGnBjJ,EAHmB,EAGKkJ,aAHL,EAGoC;EAGrD,EAAA,IAAM7H,OAAgB,GAAG6H,aAAa,IAAI/L,eAA1C,CAAA;;EAEA,EAAA,IAAI4C,QAAQ,CAACC,EAAD,CAAZ,EAAkB;EACd,IAAA,IAAMmJ,SAAS,GAAGnJ,EAAE,CAACyF,gBAArB,CAAA;MACA,IAAM2D,aAAa,GAAG3J,YAAY,CAC9BO,EAAE,CAACqB,OAD2B,EAE9BA,OAF8B,CAAlC,CAAA;EAKA,IAAA,OAAO4H,KAAK,CAAsBE,SAAtB,EAAiCC,aAAjC,CAAZ,CAAA;EACH,GAAA;;EAED,EAAA,IAAI,OAAOpJ,EAAP,KAAc,QAAlB,EAA4B;EACxB,IAAA,OAAO,UAIHqJ,SAJG,EAKHC,cALG,EAML;EAGE,MAAA,IAAI,OAAOD,SAAP,KAAqB,UAAzB,EAAqC;EACjC,QAAA,IAAMD,cAAa,GAAG3J,YAAY,CAC9BO,EAD8B,EAE9BsJ,cAF8B,CAAlC,CAAA;;EAKA,QAAA,OAAOL,KAAK,CAACI,SAAD,EAAYD,cAAZ,CAAZ,CAAA;EACH,OAAA;;EAED,MAAA,IAAMA,aAAa,GAAG3J,YAAY,CAC9BO,EAD8B,EAE9BqJ,SAF8B,CAAlC,CAAA;QAKA,OAAOJ,KAAK,CAACG,aAAD,CAAZ,CAAA;OAvBJ,CAAA;EAyBH,GAAA;;IAED,IAAI/H,OAAO,CAAC/D,OAAZ,EAAqB;EACjB,IAAA,OAAO8I,qBAAqB,CAAC6C,KAAD,EAAQjJ,EAAR,EAAYqB,OAAZ,CAA5B,CAAA;EACH,GAAA;;EAED,EAAA,IAAMkI,gBAAyB,GAAA,QAAA,CAAA,EAAA,EACxBpM,eADwB,EAExBkE,OAFwB,EAAA;MAG3BzD,MAAM,EACF,OAAOyD,OAAO,CAACzD,MAAf,KAA0B,QAA1B,IAAsCyD,OAAO,CAACzD,MAAR,IAAkB,CAAxD,GACMyD,OAAO,CAACzD,MADd,GAEMT,eAAe,CAACS,MANC;MAO3BC,OAAO,EACH,OAAOwD,OAAO,CAACxD,OAAf,KAA2B,QAA3B,IAAuCwD,OAAO,CAACxD,OAAR,IAAmB,CAA1D,GACMwD,OAAO,CAACxD,OADd,GAEMV,eAAe,CAACU,OAVC;MAW3BC,OAAO,EACH,OAAOuD,OAAO,CAACvD,OAAf,KAA2B,QAA3B,IAAuCuD,OAAO,CAACvD,OAAR,IAAmB,CAA1D,GACMuD,OAAO,CAACvD,OADd,GAEMX,eAAe,CAACW,OAdC;EAe3BE,IAAAA,WAAW,EAAEqD,OAAO,CAACrD,WAAR,IAAuB4E,qBAAqB,CAAC5C,EAAD,CAAA;KAf7D,CAAA,CAAA;;IAiBA,IAAMlB,WAA8B,GAAG,EAAvC,CAAA;;EAEA,EAqBIyK,gBArBJ,CACI9L,UADJ,CAAA;EAAA,MAqBI8L,gBArBJ,CAEInM,WAFJ,CAAA;EAAA,UAGIC,SAHJ,GAqBIkM,gBArBJ,CAGIlM,SAHJ,CAAA;EAAA,MAqBIkM,gBArBJ,CAIIjM,OAJJ,CAAA;EAAA,MAqBIiM,gBArBJ,CAKIhM,YALJ,CAAA;EAAA,MAqBIgM,gBArBJ,CAMI/L,cANJ,CAAA;EAAA,MAqBI+L,gBArBJ,CAOI5L,UAPJ,CAAA;EAAA,MAqBI4L,gBArBJ,CAQI3L,MARJ,CAAA;EAAA,MAqBI2L,gBArBJ,CASI1L,OATJ,CAAA;EAAA,UAUIC,OAVJ,GAqBIyL,gBArBJ,CAUIzL,OAVJ,CAAA;EAAA,MAWI8B,UAXJ,GAqBI2J,gBArBJ,CAWI3J,UAXJ,CAAA;EAAA,MAYIC,aAZJ,GAqBI0J,gBArBJ,CAYI1J,aAZJ,CAAA;EAAA,MAaIC,UAbJ,GAqBIyJ,gBArBJ,CAaIzJ,UAbJ,CAAA;EAAA,MAqBIyJ,gBArBJ,CAcIxL,QAdJ,CAAA;EAAA,MAqBIwL,gBArBJ,CAeIvL,WAfJ,CAAA;EAAA,MAqBIuL,gBArBJ,CAgBItL,UAhBJ,CAAA;EAAA,UAiBIC,iBAjBJ,GAqBIqL,gBArBJ,CAiBIrL,iBAjBJ,CAAA;EAAA,MAqBIqL,gBArBJ,CAkBIpL,aAlBJ,CAAA;EAAA,MAqBIoL,gBArBJ,CAmBInL,YAnBJ,CAAA;YAoBOoL,aApBP,iCAqBID,gBArBJ,EAAA,SAAA,EAAA;;EAuBA,EAAA,IAAMpK,OAAO,GAAGqJ,UAAU,CAACe,gBAAD,CAA1B,CAAA;EACA,EAAA,IAAMnK,aAAa,GAAGwJ,gBAAgB,CAACW,gBAAD,CAAtC,CAAA;IAEA,IAAME,aAAa,GAAG1H,gBAAgB,CAClCjD,WADkC,EAElCyK,gBAFkC,EAGlCpK,OAHkC,EAIlCC,aAJkC,CAAtC,CAAA;EAMA,EAAA,IAAMsK,YAAY,GAAGlG,eAAe,CAAC+F,gBAAD,CAApC,CAAA;EAEA,EAAA,IAAMzE,YAAY,GAAG+D,eAAe,CAACU,gBAAD,CAApC,CAAA;;IAEA,IAAM7D,mBAAwC,gBACvC8D,aADuC,EAAA;EAE1CrK,IAAAA,OAAO,EAAPA,OAF0C;EAG1CC,IAAAA,aAAa,EAAbA,aAH0C;EAI1C/B,IAAAA,SAAS,EAATA,SAJ0C;EAK1CS,IAAAA,OAAO,EAAPA,OAL0C;EAM1C8B,IAAAA,UAAU,EAAEyI,sBAAsB,CAC9BhK,OAAO,CACHuB,UADG,EAEH6J,aAAa,CAAC7J,UAFX,EAGH8J,YAAY,CAAC9J,UAHV,CADuB,CANQ;EAa1CC,IAAAA,aAAa,EAAEwI,sBAAsB,CAACxI,aAAD,CAbK;EAc1CC,IAAAA,UAAU,EAAEuI,sBAAsB,CAC9BhK,OAAO,CACHyB,UADG,EAEH2J,aAAa,CAAC3J,UAFX,EAGH4J,YAAY,CAAC5J,UAHV,CADuB,CAdQ;EAqB1CgF,IAAAA,YAAY,EAAZA,YAAAA;KArBJ,CAAA,CAAA;;EAwBA,EAAA,IAAMH,QAAQ,GAAGgF,2BAAO,CAAC3J,EAAD,EAAK0F,mBAAL,CAAxB,CAAA;EAEA,EAAA,IAAIlE,MAAM,GAAGuE,mBAAmB,CAAsBpB,QAAtB,EAAgC;EAC5D7F,IAAAA,WAAW,EAAXA,WAD4D;EAE5DuC,IAAAA,OAAO,EAAEkI,gBAFmD;EAG5D9D,IAAAA,gBAAgB,EAAEzF,EAAAA;EAH0C,GAAhC,CAAhC,CAAA;;EAMA,EAAA,IAAI9B,iBAAJ,EAAuB;EACnBsD,IAAAA,MAAM,GAAGsH,uBAAuB,CAAgBtH,MAAhB,CAAhC,CAAA;EACH,GAAA;;IAEDvB,OAAO,CAACuB,MAAD,EAAUxB,EAAD,CAAkBG,IAA3B,EAAiCkB,OAAO,CAACrD,WAAzC,CAAP,CAAA;EAEA,EAAA,OAAOwD,MAAP,CAAA;EACH,EAhJD;EAkJA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAyH,KAAK,CAAC3G,UAAN,GAAmBA,UAAnB,CAAA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA2G,KAAK,CAAC1G,YAAN,GAAqBA,YAArB,CAAA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA0G,KAAK,CAACrK,OAAN,GAAgB,YAA8B;IAC1C,OAAOA,OAAO,CAAP,KAAA,CAAA,KAAA,CAAA,EAAA,SAAA,CAAA,IAA6BqK,KAApC,CAAA;EACH,CAFD,CAAA;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAA,KAAK,CAACW,IAAN,GAAaX,KAAK,CAAC;EAAE7L,EAAAA,WAAW,EAAE,IAAA;EAAf,CAAD,CAAlB,CAAA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA6L,KAAK,CAACjG,QAAN,GAAiBA,QAAjB,CAAA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACAiG,KAAK,CAACY,QAAN,GAAiBZ,KAAK,CAAC;EAAEnL,EAAAA,OAAO,EAAEgM,QAAAA;EAAX,CAAD,CAAtB,CAAA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACAb,KAAK,CAAC9G,iBAAN,GAA0B,SAASA,iBAAT,GAAsC;IAC5D,OAAOF,UAAU,CAACE,iBAAlB,CAAA;EACH,CAFD,CAAA;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA8G,KAAK,CAAClJ,QAAN,GAAiB,SAASA,QAAT,CAAkBC,EAAlB,EAAyC;IACtD,OAAO,OAAOA,EAAP,KAAc,UAAd,IAA4B,CAAC,CAACA,EAAE,CAACD,QAAxC,CAAA;EACH,CAFD,CAAA;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAkJ,KAAK,CAACxL,UAAN,GAAmB,UAAUsM,UAAV,EAA+B;EAC9C,EAAA,OAAOd,KAAK,CAAC;EAAExL,IAAAA,UAAU,EAAEsM,UAAAA;EAAd,GAAD,CAAZ,CAAA;EACH,CAFD,CAAA;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAd,KAAK,CAACtL,UAAN,GAAmB,UAAUqM,UAAV,EAAqC;EACpD,EAAA,OAAOf,KAAK,CAAC;EAAEtL,IAAAA,UAAU,EAAEqM,UAAAA;EAAd,GAAD,CAAZ,CAAA;EACH,CAFD,CAAA;;EAmDA,SAASpM,MAAT,CASIA,MATJ,EAUIqM,aAVJ,EAWE;IACE,IAAIA,aAAa,KAAK,IAAtB,EAA4B;EACxB,IAAA,OAAOhB,KAAK,CAAC;EACTrL,MAAAA,MAAM,EAANA,MADS;EAETQ,MAAAA,YAAY,EAAE6L,aAAAA;EAFL,KAAD,CAAZ,CAAA;EAIH,GAAA;;EAED,EAAA,IAAI,OAAOA,aAAP,KAAyB,QAA7B,EAAuC;EACnC,IAAA,IAAQlM,QAAR,GAAmCkM,aAAnC,CAAQlM,QAAR;EAAA,QAAkBK,YAAlB,GAAmC6L,aAAnC,CAAkB7L,YAAlB,CAAA;EAEA,IAAA,OAAO6K,KAAK,CAAC;EACTrL,MAAAA,MAAM,EAANA,MADS;EAETG,MAAAA,QAAQ,EAARA,QAFS;EAGTK,MAAAA,YAAY,EAAZA,YAAAA;EAHS,KAAD,CAAZ,CAAA;EAKH,GAAA;;EAED,EAAA,IAAI,OAAO6L,aAAP,KAAyB,UAA7B,EAAyC;EACrC,IAAA,OAAOhB,KAAK,CAAC;EACTrL,MAAAA,MAAM,EAANA,MADS;EAETG,MAAAA,QAAQ,EAAEkM,aAFD;EAGT7L,MAAAA,YAAY,EAAE,IAAA;EAHL,KAAD,CAAZ,CAAA;EAKH,GAAA;;EAED,EAAA,OAAO6K,KAAK,CAAC;EAAErL,IAAAA,MAAM,EAANA,MAAAA;EAAF,GAAD,CAAZ,CAAA;EACH,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAqL,KAAK,CAACrL,MAAN,GAAeA,MAAf,CAAA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACAqL,KAAK,CAACpL,OAAN,GAAgB,SAASA,OAAT,CAAiBA,OAAjB,EAAkC;EAC9C,EAAA,OAAOoL,KAAK,CAAC;EAAEpL,IAAAA,OAAO,EAAPA,OAAAA;EAAF,GAAD,CAAZ,CAAA;EACH,CAFD,CAAA;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAoL,KAAK,CAACnL,OAAN,GAAgB,SAASA,OAAT,CAAiBA,OAAjB,EAAkC;EAC9C,EAAA,OAAOmL,KAAK,CAAC;EAAEnL,IAAAA,OAAO,EAAPA,OAAAA;EAAF,GAAD,CAAZ,CAAA;EACH,CAFD,CAAA;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAmL,KAAK,CAAC7F,OAAN,GAAgB,UAAUpF,WAAV,EAA+B;EAC3C,EAAA,OAAOiL,KAAK,CAAC;EAAEjL,IAAAA,WAAW,EAAXA,WAAAA;EAAF,GAAD,CAAZ,CAAA;EACH,CAFD,CAAA;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAiL,KAAK,CAACiB,OAAN,GAAgBjB,KAAK,CAAC;EAClB5L,EAAAA,SAAS,EAAE,IADO;EAElBe,EAAAA,YAAY,EAAE,IAAA;EAFI,CAAD,CAArB,CAAA;EAKA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA6K,KAAK,CAACkB,KAAN,GAAclB,KAAK,CAAC;EAAE3L,EAAAA,OAAO,EAAE,IAAA;EAAX,CAAD,CAAnB,CAAA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA2L,KAAK,CAACmB,SAAN,GAAkBnB,KAAK,CAAC;EAAE1L,EAAAA,YAAY,EAAE,IAAA;EAAhB,CAAD,CAAvB,CAAA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA0L,KAAK,CAACoB,aAAN,GAAsB,UAAUpM,UAAV,EAAiC;EACnD,EAAA,OAAOgL,KAAK,CAAC;EAAE1L,IAAAA,YAAY,EAAE,IAAhB;EAAsBU,IAAAA,UAAU,EAAVA,UAAAA;EAAtB,GAAD,CAAZ,CAAA;EACH,CAFD,CAAA;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAgL,KAAK,CAACqB,OAAN,GAAgBrB,KAAK,CAAC;EAAEzL,EAAAA,cAAc,EAAE,IAAA;EAAlB,CAAD,CAArB,CAAA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACAyL,KAAK,CAAC9K,aAAN,GAAsB,UAClBA,aADkB,EAAA;EAAA,EAAA,OAEjB8K,KAAK,CAAC;EAAE9K,IAAAA,aAAa,EAAbA,aAAAA;EAAF,GAAD,CAFY,CAAA;EAAA,CAAtB,CAAA;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA8K,KAAK,CAAC/K,iBAAN,GAA0B,UACtBA,iBADsB,EAAA;EAAA,EAAA,OAErB+K,KAAK,CAAC;EAAE/K,IAAAA,iBAAiB,EAAjBA,iBAAAA;EAAF,GAAD,CAFgB,CAAA;EAAA,CAA1B;EAKA;;;EACAkC,MAAM,CAACC,cAAP,CAAsB4I,KAAtB,EAA6B,SAA7B,EAAwC;EACpC3I,EAAAA,YAAY,EAAE,KADsB;EAEpCC,EAAAA,UAAU,EAAE,KAFwB;EAGpCC,EAAAA,KAAK,EAAEyI,KAH6B;EAIpCxI,EAAAA,QAAQ,EAAE,KAAA;EAJ0B,CAAxC,CAAA;;;;;;;;"}