{"name": "find-up", "version": "7.0.0", "description": "Find a file or directory by walking up parent directories", "license": "MIT", "repository": "sindresorhus/find-up", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "dependencies": {"locate-path": "^7.2.0", "path-exists": "^5.0.0", "unicorn-magic": "^0.1.0"}, "devDependencies": {"ava": "^5.3.1", "is-path-inside": "^4.0.0", "tempy": "^3.1.0", "tsd": "^0.29.0", "xo": "^0.56.0"}}