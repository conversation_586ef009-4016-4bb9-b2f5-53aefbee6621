import got from"got";

import{getOpts}from"./options.js";
import{addProgress}from"./progress.js";


export default async function fetchNodeWebsite(path,opts){
const{mirror,progress}=getOpts(path,opts);

const pathA=path.replace(LEADING_SLASH_REGEXP,"");
const response=await got.stream(pathA,{prefixUrl:mirror});

addProgress(response,progress,path);

return response;
}

const LEADING_SLASH_REGEXP=/^\//u;
//# sourceMappingURL=main.js.map