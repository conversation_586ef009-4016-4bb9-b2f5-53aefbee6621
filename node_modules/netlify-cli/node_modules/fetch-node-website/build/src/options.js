import isPlainObj from"is-plain-obj";

import{getDefaultMirror}from"./mirror.js";


export const getOpts=function(path,opts={}){
validateBasic(path,opts);
const{progress=false,mirror=getDefaultMirror()}=opts;
validateProgress(progress);
validate<PERSON>ir<PERSON>r(mirror);
return{progress,mirror};
};

const validateBasic=function(path,opts){
if(typeof path!=="string"||path.trim()===""){
throw new TypeError(`Path must be a non-empty string: ${path}`);
}

if(!isPlainObj(opts)){
throw new TypeError(`Options must be a plain object: ${opts}`);
}
};

const validateProgress=function(progress){
if(typeof progress!=="boolean"){
throw new TypeError(`Option "progress" must be a boolean: ${progress}`);
}
};

const validateMirror=function(mirror){
if(typeof mirror!=="string"){
throw new TypeError(`Option "mirror" must be a string: ${mirror}`);
}
};
//# sourceMappingURL=options.js.map