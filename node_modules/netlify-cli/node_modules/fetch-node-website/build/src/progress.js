import{finished}from"node:stream";
import{promisify}from"node:util";

import{MultiBar}from"cli-progress";
import colorsOption from"colors-option";
import figures from"figures";


const pFinished=promisify(finished);

const{green}=colorsOption();



export const addProgress=async function(response,progress,path){
if(!progress||!showsBar()){
return;
}

const bar=startBar(path);

response.on("downloadProgress",({percent})=>{
bar.update(percent);
});

try{
await pFinished(response,{writable:false});
}catch{}

stopBar(bar);
};

const MULTIBAR_OPTS={
format:`  ${green(figures.nodejs)}  {prefix}  {bar}`,
barCompleteChar:"\u2588",
barIncompleteChar:"\u2591",
stopOnComplete:true,
clearOnComplete:true,
hideCursor:true};






const multibar=new MultiBar(MULTIBAR_OPTS);


const startBar=function(path){
const bar=multibar.create();
const prefix=getPrefix(path);
bar.start(1,0,{prefix});
return bar;
};


const showsBar=function(){
return multibar.terminal.isTTY();
};


const getPrefix=function(path){
const version=VERSION_TEXT_REGEXP.exec(path);

if(version!==null){
return`${VERSION_TEXT} ${version[1].padEnd(VERSION_PADDING)}`;
}

if(INDEX_TEXT_REGEXP.test(path)){
return INDEX_TEXT;
}

return DEFAULT_TEXT;
};

const VERSION_TEXT_REGEXP=/^\/?v([\d.]+)\//u;
const INDEX_TEXT_REGEXP=/^\/?index.(json|tab)$/u;
const VERSION_PADDING=7;

const VERSION_TEXT="Node.js";
const INDEX_TEXT="List of Node.js versions";
const DEFAULT_TEXT="Node.js";


const stopBar=function(bar){
bar.stop();
multibar.remove(bar);


if(multibar.bars.length===0){
multibar.stop();
}
};
//# sourceMappingURL=progress.js.map