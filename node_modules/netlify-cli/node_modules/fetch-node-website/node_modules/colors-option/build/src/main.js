import{Chalk}from"chalk";

import{getOpts}from"./options.js";










export default function colorsOption(opts){
const{colors,stream,chalkOpts}=getOpts(opts);
const level=getLevel(colors,stream);
const chalk=new Chalk({...chalkOpts,level});
return chalk;
}

const getLevel=function(colors,stream){
if(colors===false){
return 0;
}

const terminalLevel=getTerminalLevel(stream);
return colors===undefined?terminalLevel:Math.max(terminalLevel,1);
};

const getTerminalLevel=function(stream){
return stream.isTTY?DEPTH_TO_LEVEL[stream.getColorDepth()]:0;
};


const DEPTH_TO_LEVEL={1:0,4:1,8:2,24:3};
//# sourceMappingURL=main.js.map