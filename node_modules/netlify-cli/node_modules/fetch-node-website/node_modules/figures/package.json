{"name": "figures", "version": "5.0.0", "description": "Unicode symbols with fallbacks for older terminals", "license": "MIT", "repository": "sindresorhus/figures", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=14"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "symbol", "symbols", "figure", "figures", "fallback"], "dependencies": {"escape-string-regexp": "^5.0.0", "is-unicode-supported": "^1.2.0"}, "devDependencies": {"ava": "^4.3.1", "tsd": "^0.22.0", "xo": "^0.51.0"}}