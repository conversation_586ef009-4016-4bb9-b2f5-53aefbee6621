const express = require('express');
const cors = require('cors');
const axios = require('axios');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// 模拟数据生成器（你可以替换为实际的GitHub API调用）
async function generateMockData() {
    const mockProjects = [
        {
            name: "awesome-project",
            url: "https://github.com/example/awesome-project",
            description: "An awesome project that does amazing things",
            stars: 1234,
            forks: 567,
            language: "JavaScript",
            author: "example-user",
            authorUrl: "https://github.com/example-user",
            createdAt: "2025-05-28"
        },
        {
            name: "cool-tool",
            url: "https://github.com/example/cool-tool",
            description: "A cool tool for developers",
            stars: 890,
            forks: 234,
            language: "Python",
            author: "cool-dev",
            authorUrl: "https://github.com/cool-dev",
            createdAt: "2025-05-25"
        }
        // 添加更多模拟数据...
    ];

    return {
        lastUpdate: new Date().toISOString(),
        weekly: mockProjects,
        monthly: mockProjects,
        quarterly: mockProjects,
        yearly: mockProjects.slice(0, 3)
    };
}

// 实际的GitHub API数据获取函数
async function fetchGitHubTrendingData() {
    try {
        const token = process.env.GITHUB_TOKEN;
        const headers = token ? { 'Authorization': `token ${token}` } : {};
        
        // 计算日期范围
        const now = new Date();
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        const quarterAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        const yearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

        const formatDate = (date) => date.toISOString().split('T')[0];

        // 构建搜索查询
        const buildQuery = (sinceDate) => `created:>${formatDate(sinceDate)}`;

        // 获取不同时间范围的数据
        const fetchPeriodData = async (query, perPage = 10) => {
            const response = await axios.get('https://api.github.com/search/repositories', {
                headers,
                params: {
                    q: query,
                    sort: 'stars',
                    order: 'desc',
                    per_page: perPage
                }
            });

            return response.data.items.map(repo => ({
                name: repo.name,
                url: repo.html_url,
                description: repo.description || '暂无描述',
                stars: repo.stargazers_count,
                forks: repo.forks_count,
                language: repo.language || '未知',
                author: repo.owner.login,
                authorUrl: repo.owner.html_url,
                createdAt: repo.created_at
            }));
        };

        const [weekly, monthly, quarterly, yearly] = await Promise.all([
            fetchPeriodData(buildQuery(weekAgo), 10),
            fetchPeriodData(buildQuery(monthAgo), 10),
            fetchPeriodData(buildQuery(quarterAgo), 10),
            fetchPeriodData(buildQuery(yearAgo), 3)
        ]);

        return {
            lastUpdate: new Date().toISOString(),
            weekly,
            monthly,
            quarterly,
            yearly
        };

    } catch (error) {
        console.error('获取GitHub数据失败:', error.message);
        // 如果API调用失败，返回模拟数据
        return await generateMockData();
    }
}

// API路由
app.get('/api/stats', async (req, res) => {
    try {
        const data = await fetchGitHubTrendingData();
        res.json(data);
    } catch (error) {
        console.error('API错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 健康检查路由
app.get('/api/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 默认路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/index.html'));
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`服务器运行在端口 ${PORT}`);
    console.log(`访问地址: http://localhost:${PORT}`);
});

module.exports = app;