const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// 安全和性能中间件
app.use(helmet({
    contentSecurityPolicy: false // 允许内联脚本，适合演示
}));
app.use(compression());
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// API 速率限制
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 每个IP最多100个请求
    message: { error: '请求过于频繁，请稍后再试' }
});
app.use('/api/', limiter);

// 内存数据存储（替代数据库，适合演示）
let memoryStore = {
    projects: {},
    lastUpdate: null,
    languageStats: {},
    trendsHistory: []
};

// GitHub API 服务类
class GitHubService {
    constructor() {
        this.token = process.env.GITHUB_TOKEN;
        this.baseURL = 'https://api.github.com';
        this.axios = require('axios');
    }

    async makeRequest(url, params = {}) {
        const headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'GitHub-Trending-Stats/2.0'
        };

        if (this.token) {
            headers['Authorization'] = `token ${this.token}`;
        }

        try {
            const response = await this.axios.get(url, { headers, params });
            return response.data;
        } catch (error) {
            console.error('GitHub API 请求失败:', error.message);
            throw error;
        }
    }

    async searchRepositories(query, options = {}) {
        const params = {
            q: query,
            sort: options.sort || 'stars',
            order: options.order || 'desc',
            per_page: options.perPage || 30
        };

        try {
            const data = await this.makeRequest(`${this.baseURL}/search/repositories`, params);
            
            return data.items.map(repo => ({
                github_id: repo.id,
                name: repo.name,
                full_name: repo.full_name,
                url: repo.html_url,
                description: repo.description || '暂无描述',
                stars: repo.stargazers_count,
                forks: repo.forks_count,
                language: repo.language || 'Unknown',
                topics: repo.topics || [],
                author: repo.owner.login,
                author_url: repo.owner.html_url,
                created_at: repo.created_at,
                updated_at: repo.updated_at
            }));
        } catch (error) {
            console.error('搜索仓库失败:', error.message);
            return this.getFallbackData();
        }
    }

    async getTrendingByPeriod(days, limit = 10) {
        const date = new Date();
        date.setDate(date.getDate() - days);
        const dateStr = date.toISOString().split('T')[0];
        
        const query = `created:>${dateStr}`;
        return await this.searchRepositories(query, { perPage: limit });
    }

    async getTrendingByLanguage() {
        const languages = ['JavaScript', 'Python', 'Java', 'TypeScript', 'Go', 'Rust'];
        const results = {};

        for (const language of languages) {
            try {
                const date = new Date();
                date.setDate(date.getDate() - 7);
                const dateStr = date.toISOString().split('T')[0];
                const query = `created:>${dateStr} language:${language}`;
                
                results[language] = await this.searchRepositories(query, { perPage: 5 });
                
                // 添加延迟避免速率限制
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                console.error(`获取 ${language} 数据失败:`, error.message);
                results[language] = [];
            }
        }

        return results;
    }

    // 备用数据，当API失败时使用
    getFallbackData() {
        return [
            {
                github_id: 1,
                name: "trending-project-1",
                full_name: "example/trending-project-1",
                url: "https://github.com/example/trending-project-1",
                description: "一个很棒的开源项目，展示最新的技术趋势",
                stars: 2500,
                forks: 450,
                language: "JavaScript",
                topics: ["javascript", "nodejs", "react"],
                author: "example-user",
                author_url: "https://github.com/example-user",
                created_at: "2025-05-25T10:00:00Z",
                updated_at: "2025-06-01T10:00:00Z"
            },
            {
                github_id: 2,
                name: "awesome-python-tool",
                full_name: "dev/awesome-python-tool",
                url: "https://github.com/dev/awesome-python-tool",
                description: "强大的Python开发工具，提升开发效率",
                stars: 1800,
                forks: 320,
                language: "Python",
                topics: ["python", "tools", "automation"],
                author: "dev-team",
                author_url: "https://github.com/dev-team",
                created_at: "2025-05-28T15:30:00Z",
                updated_at: "2025-06-01T09:15:00Z"
            },
            {
                github_id: 3,
                name: "go-microservice",
                full_name: "gopher/go-microservice",
                url: "https://github.com/gopher/go-microservice",
                description: "高性能Go微服务框架，支持云原生部署",
                stars: 3200,
                forks: 680,
                language: "Go",
                topics: ["go", "microservices", "docker"],
                author: "gopher-dev",
                author_url: "https://github.com/gopher-dev",
                created_at: "2025-05-20T08:45:00Z",
                updated_at: "2025-05-31T16:20:00Z"
            }
        ];
    }
}

const githubService = new GitHubService();

// 数据更新函数
async function updateTrendingData() {
    try {
        console.log('开始更新GitHub trending数据...');
        
        const [weekly, monthly, quarterly, yearly, languageStats] = await Promise.all([
            githubService.getTrendingByPeriod(7, 10),
            githubService.getTrendingByPeriod(30, 10),
            githubService.getTrendingByPeriod(90, 10),
            githubService.getTrendingByPeriod(365, 3),
            githubService.getTrendingByLanguage()
        ]);

        memoryStore.projects = { weekly, monthly, quarterly, yearly };
        memoryStore.languageStats = languageStats;
        memoryStore.lastUpdate = new Date().toISOString();
        
        console.log('数据更新完成:', {
            weekly: weekly.length,
            monthly: monthly.length,
            quarterly: quarterly.length,
            yearly: yearly.length
        });
        
    } catch (error) {
        console.error('更新数据失败:', error.message);
        
        // 如果没有缓存数据，不使用备用数据，让前端显示加载失败
        // if (!memoryStore.projects.weekly) {
        //     const fallbackData = githubService.getFallbackData();
        //     memoryStore.projects = {
        //         weekly: fallbackData,
        //         monthly: fallbackData,
        //         quarterly: fallbackData,
        //         yearly: fallbackData.slice(0, 3)
        //     };
        //     memoryStore.lastUpdate = new Date().toISOString();
        //     console.log('使用备用数据');
        // }
    }
}

// API 路由
app.get('/api/stats', async (req, res) => {
    try {
        // 如果没有数据或数据过期（超过1小时），则更新数据
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        const lastUpdate = memoryStore.lastUpdate ? new Date(memoryStore.lastUpdate) : null;
        
        if (!lastUpdate || lastUpdate < oneHourAgo) {
            await updateTrendingData();
        }
        
        res.json({
            ...memoryStore.projects,
            lastUpdate: memoryStore.lastUpdate
        });
    } catch (error) {
        console.error('API 错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

app.get('/api/stats/by-language', async (req, res) => {
    try {
        res.json(memoryStore.languageStats || {});
    } catch (error) {
        console.error('语言统计API错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

app.get('/api/trends/history', async (req, res) => {
    try {
        // 模拟历史趋势数据
        const mockTrendHistory = [
            { github_id: 1, date: '2025-05-25', period: 'weekly', total_projects: 1250 },
            { github_id: 1, date: '2025-05-26', period: 'weekly', total_projects: 1280 },
            { github_id: 1, date: '2025-05-27', period: 'weekly', total_projects: 1320 },
            { github_id: 1, date: '2025-05-28', period: 'weekly', total_projects: 1350 },
            { github_id: 1, date: '2025-05-29', period: 'weekly', total_projects: 1380 },
            { github_id: 1, date: '2025-05-30', period: 'weekly', total_projects: 1400 },
            { github_id: 1, date: '2025-06-01', period: 'weekly', total_projects: 1450 },

            { github_id: 2, date: '2025-05-25', period: 'weekly', total_projects: 800 },
            { github_id: 2, date: '2025-05-26', period: 'weekly', total_projects: 820 },
            { github_id: 2, date: '2025-05-27', period: 'weekly', total_projects: 850 },
            { github_id: 2, date: '2025-05-28', period: 'weekly', total_projects: 880 },
            { github_id: 2, date: '2025-05-29', period: 'weekly', total_projects: 900 },
            { github_id: 2, date: '2025-05-30', period: 'weekly', total_projects: 930 },
            { github_id: 2, date: '2025-06-01', period: 'weekly', total_projects: 950 },

            { github_id: 3, date: '2025-05-20', period: 'weekly', total_projects: 3000 },
            { github_id: 3, date: '2025-05-21', period: 'weekly', total_projects: 3050 },
            { github_id: 3, date: '2025-05-22', period: 'weekly', total_projects: 3100 },
            { github_id: 3, date: '2025-05-23', period: 'weekly', total_projects: 3150 },
            { github_id: 3, date: '2025-05-24', period: 'weekly', total_projects: 3200 },
            { github_id: 3, date: '2025-05-25', period: 'weekly', total_projects: 3250 },
            { github_id: 3, date: '2025-05-31', period: 'weekly', total_projects: 3300 }
        ];
        
        res.json(mockTrendHistory);
    } catch (error) {
        console.error('趋势历史API错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 缓存清理API
app.post('/api/cache/clear', (req, res) => {
    memoryStore.lastUpdate = null;
    res.json({ message: '缓存已清理' });
});

// 健康检查
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage()
    });
});

// 默认路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public/index.html'));
});

// 404处理
app.use('*', (req, res) => {
    res.status(404).json({ error: '页面未找到' });
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('服务器错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 服务器运行在端口 ${PORT}`);
    console.log(`📊 访问地址: http://localhost:${PORT}`);
    
    // 启动时立即更新一次数据
    updateTrendingData();
});

// 定时任务：每小时更新一次数据
if (process.env.NODE_ENV === 'production') {
    const cron = require('node-cron');
    
    // 每小时更新数据
    cron.schedule('0 * * * *', () => {
        console.log('定时任务：更新GitHub trending数据');
        updateTrendingData();
    });
    
    console.log('📅 定时任务已启动：每小时自动更新数据');
}

module.exports = app;